import { JRNextFunction, JRRequest, JRResponse, JREndpoint } from '@phinxlab/just-rpc';
export declare class ClonacionAcapEndpoint extends JREndpoint {
    private accionesMap;
    private ofertasMap;
    private ofertasTurnosMap;
    private ofertasGruposMap;
    private accionesOrientacionesMap;
    private tx;
    static CICLO_LECTIVO_ORIGINAL: number;
    static CICLO_LECTIVO_CLONAR: number;
    private contenidosCount;
    private accionRepo;
    private cicloLectivo;
    constructor(name?: string, path?: string);
    configure(): void;
    execute(req: JRRequest, res: JRResponse, next: JRNextFunction): Promise<JRResponse>;
    clonar(): Promise<void>;
    getCicloActual(): Promise<void>;
    cloneAcciones(): Promise<void>;
    cloneAccionesOrientaciones(): Promise<void>;
    cloneAccionesOrientacionesContenidos(): Promise<void>;
    cloneAccionesOrientacionesHabilidades(): Promise<void>;
    cloneAccionesOrientacionesPerfiles(): Promise<void>;
    cloneOfertas(): Promise<void>;
    cloneOfertasTurnos(): Promise<void>;
    cloneOfertasGrupos(): Promise<void>;
    cloneGruposSelecciones(): Promise<void>;
    cloneGrupoJornadas(): Promise<void>;
    cloneGrupoInscripciones(): Promise<void>;
}

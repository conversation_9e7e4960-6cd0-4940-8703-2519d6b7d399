import { ExtractorContext } from '../lib/commons';
type Establishment = {
    idestablecimiento: string;
    nombre: string;
    idtipoestablecimiento: number;
    cue: number;
    idsupervision: number;
    nombreabreviado: string;
    iddistritoescolar: number;
    numero: number;
    iddependenciafuncional: number;
    idmodalidad: number;
};
type Locality = {
    idlocalizacion: number;
    descripcion: string;
    iddomicilio: number;
    anexo: number;
    cueanexo: number;
    sede: boolean;
    idestablecimiento: number;
    imagenfachada: string;
};
type Address = {
    iddomicilio: number;
    calle: string;
    altura: string;
    piso: string;
    departamento: string;
    ciudad: string;
    idprovincia: number;
    codigopostal: string;
    asentamiento: string;
    manzana: string;
    casa: string;
    gkba: string;
    latlon: any;
    descripcion: string;
    cui: number;
};
type turn = {
    idturno: number;
    nombre: string;
    descripcion: string;
};
type orientacion = {
    idorientacion: number;
    descripcion: string;
};
type planstudy = {
    idplanestudio: number;
    idorientacion: number;
};
type cycle_school = {
    idciclolectivo: number;
    descripcion: string;
    anio: number;
    fechainicio: string;
    fechafin: string;
    idestadociclolectivo: number;
};
type status_cycle_school = {
    idestadociclolectivo: number;
    nombre: string;
};
type units_educative = {
    idunidadeducativa: number;
    nombreabreviado: string;
    nombre: string;
    idmodalidad: number;
    idnivel: number;
};
type level = {
    idnivel: number;
    descripcion: string;
};
type section = {
    idseccion: number;
    nombre: string;
    idlocalizacion: number;
    anio: string;
    division: string;
    idturno: number;
    jornada: string;
    idplanestudio: string;
    idciclolectivo: string;
    idunidadeducativa: string;
    capacidad_recom: string;
    capacidad_max: string;
    updated_at: string;
    updated_from: string;
};
export interface MiEscuelaExtractorContext extends ExtractorContext {
    establishment: Establishment[];
    establishmentType: {
        idtipoestablecimiento: number;
        abreviatura: string;
        descripcion: string;
    }[];
    supervision: {
        idsupervision: number;
        nombre: string;
        descripcion: string;
    }[];
    scholarDistrict: {
        iddistritoescolar: number;
        nombre: string;
        romano: string;
    }[];
    functionalDependency: {
        iddependenciafuncional: number;
        descripcion: string;
    }[];
    modality: {
        idmodalidad: number;
        descripcion: string;
    }[];
    address: Address[];
    level: level[];
    province: {
        idprovincia: number;
        descripcion: string;
    }[];
    educational_unit: units_educative[];
    status_cycle_school: status_cycle_school[];
    cycle_school: cycle_school[];
    plan_study: planstudy[];
    location: Locality[];
    orientation: orientacion[];
    turn: turn[];
    section: section[];
    studentMovement: {
        idalumnomovimiento: number;
        idalumno: number;
        idseccion: number;
        idciclolectivo: number;
        idestadoalumno: number;
    }[];
    studentState: {
        idestadoalumno: number;
        descripcion: string;
    }[];
    student: {
        idalumno: number;
        idpersona: number;
    }[];
    person: {
        nombre: string;
        apellido: string;
        idtipodocumento: number;
        documento: string;
        idpersona: number;
        sexo: number;
        fechanacimiento: string;
        iddomicilio: number;
        cuil: number;
        idprovincia: number;
        idpaisnac: number;
        idciudadnac: number;
        idprov_nacimiento: number;
        estado_civil: string;
        idnacionalidad: number;
        pob_indig_habla: string;
        pob_indig_pertenece: string;
        lengua_indig: string;
    }[];
    documentType: {
        idtipodocumento: number;
        descripcion: string;
    }[];
    country: {
        idpais: number;
        descripcion: string;
    }[];
    city: {
        idciudad: number;
        descripcion: string;
    }[];
}
export interface MiEscuelaExtractorTargetContext extends ExtractorContext {
    establishment: any[];
    establishmentType: any[];
    supervision: any[];
    scholarDistrict: any[];
    functionalDependency: any[];
    modality: any[];
    address: any[];
    studentMovement: any[];
    studentState: any[];
    matriculaState: any[];
    condition: any[];
    student: any[];
    person: any[];
    genres: any[];
    documentType: any[];
    country: any[];
    anios: any[];
    modality_level: any[];
}
export {};

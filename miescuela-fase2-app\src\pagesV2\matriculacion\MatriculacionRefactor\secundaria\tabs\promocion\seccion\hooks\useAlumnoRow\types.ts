import { type ProyeccionEstado, type CombinacionesPase, type EstadoPaseAnio, type Proyeccion } from '@miesc/lib/database/models'
import { type AlumnoTableRow } from '../../../../promocion/types/types'
import { type SeccionWithProyeccionV2 } from '@miesc/lib/database/models/SeccionV2'
import { type AccionPase } from '@miesc/lib/database/models/AccionPase'
import { type MateriasPendientesInfo } from '../../utils/materiasPendientesHelper'

export interface UseAlumnoRowProps {
  alumnosIds: string[]
  allEstadoPaseAnio: EstadoPaseAnio[]
  savedMotivo: string | null
  savedEstadoPaseAnio: string | null
  savedAccionPase: string
  combinacionesPase: CombinacionesPase[]
  proyeccion: Proyeccion | undefined
  proyeccionEstado: ProyeccionEstado[]
  selectedSeccion: SeccionWithProyeccionV2 | null
  updateAlumnosProyectadosToSave: (alumnosIds: string[], fields: Partial<AlumnoTableRow>, setHasChangesValue?: boolean) => void
  materiasPendientes?: MateriasPendientesInfo
}

export interface PaseDeAnioParsed {
  esNoConcurrira: boolean
  contieneTurno: boolean
  index: number
  accion: AccionPase
}

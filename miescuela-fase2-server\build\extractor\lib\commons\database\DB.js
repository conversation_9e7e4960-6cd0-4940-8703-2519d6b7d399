"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DB = exports.DBPool = void 0;
const pg_1 = require("pg");
const log_1 = require("../log");
class DBPool {
    constructor(config, name) {
        this.configure(config);
        this.log = new log_1.LogManager(`DB Pool ${name}`);
    }
    //FIXME IF YOU SET TWICE THE CONNECTION WILL GENERATE A MEMORY LEAK
    configure(config) {
        this.pool = new pg_1.Pool(config);
        this.pool.on('error', (err, client) => {
            this.log.error('Unexpected error on idle client', err);
            process.exit(-1);
        });
    }
    getConnectionQuery() {
        return this.connectionQuery;
    }
    getPool() {
        return this.pool;
    }
}
exports.DBPool = DBPool;
class DB {
    constructor(config, name = 'anonymous') {
        this.dbPool = new DBPool(config, name);
        this.log = new log_1.LogManager(`DB ${name}`);
    }
    async getConnection() {
        const pool = this.dbPool.getPool();
        const connectionQuery = this.dbPool.getConnectionQuery();
        //We check if the connetion is defined.
        if (!pool) {
            this.log.error('You must defined a connection in order to connect to the Database. Please call DBPool.configure()');
            throw new Error('You must defined a connection in order to connect to the Database. Please call DBPool.configure()');
        }
        //We connect to the database
        const connection = await pool.connect();
        //If there is a connection string we run it.
        if (connectionQuery)
            await connection.query(connectionQuery);
        return connection;
    }
    async query(sql, values) {
        const client = await this.getConnection();
        try {
            console.log('sql', sql);
            return await client.query(sql, values);
        }
        finally {
            client.release();
        }
    }
}
exports.DB = DB;
//# sourceMappingURL=DB.js.map
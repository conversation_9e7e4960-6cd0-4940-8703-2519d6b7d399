"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EstadoFolioEndpoint = void 0;
const dao_1 = require("../../orm/dao");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
class EstadoFolioEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.EstadoFolioDAO.entity.toLowerCase(), '/estadoFolio', dao_1.EstadoFolioDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.EstadoFolioEndpoint = EstadoFolioEndpoint;
//# sourceMappingURL=EstadoFolioEndpoint.js.map
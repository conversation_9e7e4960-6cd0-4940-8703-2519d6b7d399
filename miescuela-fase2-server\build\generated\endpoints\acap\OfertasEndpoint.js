"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfertasEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const just_rpc_1 = require("@phinxlab/just-rpc");
const helpers_1 = require("../../../utils/helpers");
const Log = new chino_sdk_1.ChinoLogManager('OfertasEndpoint');
class OfertasEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.ofertas', '/acap/ofertas', dao_1.OfertasDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async postInsertAction(value, session, req, tx) {
        await this.registrarHistoricosNuevasOfertas(value, session, req, tx);
        return Promise.resolve(value);
    }
    async postUpdateAction(value, session, req, tx) {
        await this.registrarHistoricosNuevosGrupos(value, session, req, tx);
        return Promise.resolve(value);
    }
    /**
     * registra un nuevo movimiento de referente en el historico cuando la oferta es creada y
     * registra fecha de alta del referente en el grupo.
     * nueva.
     * @param value
     * @param session
     * @param req
     * @param tx
     */
    async registrarHistoricosNuevasOfertas(value, session, req, tx) {
        const ofertaNueva = value;
        const gruposDeLaOferta = dao_1.OfertasDAO.extraerGruposDeOferta(ofertaNueva);
        if (gruposDeLaOferta.length === 0) {
            throw new Error('Error al extraer los grupos de la oferta.');
        }
        const historicoPersistencia = dao_1.OfertasDAO.prepararHistoricoPersistencia(gruposDeLaOferta, Number(ofertaNueva.idOferta));
        const context = chino_sdk_1.ChinoContext.fromSession(just_rpc_1.JRProtocolManager.get().recoverSession(req));
        try {
            await dao_1.OfertasDAO.registrarHistorico(historicoPersistencia, tx, context);
            const gruposConFechaAlta = dao_1.OfertasDAO.registrarfechaAltaReferenteEnGrupos(gruposDeLaOferta);
            await dao_1.OfertasGruposDAO.save(gruposConFechaAlta, tx, {
                context: context,
                aspect: 'ofertas_grupos_save_aspect',
            });
        }
        catch (error) {
            const errorMessage = `Error inesperado tratando de registrar historico para oferta nueva: ${error}`;
            Log.fatal(errorMessage);
            throw new chino_sdk_1.ChinoCustomError(errorMessage, 500);
        }
    }
    /**
     * registra un nuevo movimiento de referente en el historico cuando la oferta es actualizada y
     * se le han agregado nuevos grupos dentro de la oferta.
     * @param value
     * @param session
     * @param req
     * @param tx
     */
    async registrarHistoricosNuevosGrupos(value, session, req, tx) {
        const oferta = value;
        const grupos = dao_1.OfertasDAO.extraerGruposDeOferta(oferta);
        const gruposNuevos = grupos.filter((grupo) => grupo.fechaAltaReferente === null);
        if (gruposNuevos.length > 0) {
            const historicoPersistencia = dao_1.OfertasDAO.prepararHistoricoPersistencia(gruposNuevos, Number(oferta.idOferta));
            const context = chino_sdk_1.ChinoContext.fromSession(just_rpc_1.JRProtocolManager.get().recoverSession(req));
            try {
                await dao_1.OfertasDAO.registrarHistorico(historicoPersistencia, tx, context);
                const gruposConFechaAlta = dao_1.OfertasDAO.registrarfechaAltaReferenteEnGrupos(gruposNuevos);
                await dao_1.OfertasGruposDAO.save(gruposConFechaAlta, tx, {
                    context: context,
                    aspect: 'ofertas_grupos_save_aspect',
                });
            }
            catch (error) {
                const errorMessage = `Error inesperado tratando de registrar historico para nuevos grupos: ${error}`;
                Log.fatal(errorMessage);
                throw new chino_sdk_1.ChinoCustomError(errorMessage, 500);
            }
        }
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.OfertasEndpoint = OfertasEndpoint;
//# sourceMappingURL=OfertasEndpoint.js.map
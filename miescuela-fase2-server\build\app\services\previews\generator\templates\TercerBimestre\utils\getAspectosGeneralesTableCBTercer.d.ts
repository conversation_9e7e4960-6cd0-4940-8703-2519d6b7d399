type AspectosGeneralesTableCBTercer = {
    acompaniamientoPromocion?: string;
    ciclo: string;
    anio: string;
    tipoPeriodo: string;
    organizaParticipa?: string;
    espaciosConsolidando?: string;
    compromisoAprendizaje?: string;
    apoyoPregunta?: string;
    espaciosConsolidandoPregunta?: string;
    vinculoPedagogico?: string;
    apoyo?: string[];
    otrosApoyos?: string;
    promocionAcompanada?: string;
    efectivizado?: boolean;
    idTipoPeriodo?: number | null;
    acompaniamientoPregunta?: string;
    comprometeReconoce?: string;
    acompaniamientoAreaPregunta?: string;
    acompaniamientoArea?: string[];
};
declare const _default: ({ ciclo, anio, tipoPeriodo, apoyoPregunta, apoyo, otrosApoyos, acompaniamientoAreaPregunta, acompaniamientoArea, }: AspectosGeneralesTableCBTercer) => string;
export default _default;

import { JRRequest, JRResponse } from '@phinxlab/just-rpc';
import { EntityManager } from 'typeorm';
import { MiEscuelaEndpointCustom } from '../../../app/config';
import { AlumnoMovimiento, EspacioCurricularSeccion } from '../../orm/entities';
type Response = AlumnoMovimiento & {
    ecPendientes: EspacioCurricularSeccion[];
};
export declare class VerificacionConfirmacionMatriculaEndpoint extends MiEscuelaEndpointCustom {
    constructor(name?: string, path?: string);
    validate(params: {
        seccion?: string;
    }): void;
    exec(req: JRRequest, manager: EntityManager, res: JRResponse): Promise<Array<Response>>;
    configure(): void;
}
export {};

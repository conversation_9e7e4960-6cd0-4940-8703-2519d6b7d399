"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CertificadoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class CertificadoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.CertificadoDAO.entity.toLowerCase(), '/certificados', dao_1.CertificadoDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.CertificadoEndpoint = CertificadoEndpoint;
//# sourceMappingURL=CertificadoEndpoint.js.map
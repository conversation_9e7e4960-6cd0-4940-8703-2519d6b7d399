import { useMemo } from 'react'
import { useSecundarioNotasCuartoBimestreLibbyFetch } from '@miesc/lib/business/SecundarioNotasCuartoBimestre'
import type { CalificacionesCerradasData, CalificacionesCerradasResponse } from '../types/types'
import { PROYECCION_ESTADOS } from '@miesc/const/proyeccionEstadoTipo'

interface UseCalificacionesCerradasValidationProps {
  estadoTipo?: string
}

export const useCalificacionesCerradasValidation = ({
  estadoTipo
}: UseCalificacionesCerradasValidationProps = {}) => {
  const shouldFetch = PROYECCION_ESTADOS.PUEDE_INFORMAR_VACANTES_IEL === estadoTipo

  const { data: response = [], working: isLoading } = useSecundarioNotasCuartoBimestreLibbyFetch({
    enabled: shouldFetch,
    aspect: 'cuartoBimestreNotasCheck'
  })

  const calificacionesCerradasData = useMemo<CalificacionesCerradasData>(() => {
    if (!shouldFetch) {
      return {
        cerradas: true,
        tieneCuartoBimestreAbierto: false,
        tieneDiciembreAbierto: false,
        tieneFebMarAbierto: false
      }
    }

    if (isLoading) {
      return {
        cerradas: false,
        tieneCuartoBimestreAbierto: true,
        tieneDiciembreAbierto: true,
        tieneFebMarAbierto: false
      }
    }

    const data = (response[0] as unknown as CalificacionesCerradasResponse)

    return {
      cerradas: data?.cerradas ?? true,
      tieneCuartoBimestreAbierto: data?.tieneCuartoBimestreAbierto ?? false,
      tieneDiciembreAbierto: data?.tieneDiciembreAbierto ?? false,
      tieneFebMarAbierto: false
    }
  }, [shouldFetch, response, isLoading])

  return {
    calificacionesCerradasData,
    isLoading: shouldFetch && isLoading
  }
}

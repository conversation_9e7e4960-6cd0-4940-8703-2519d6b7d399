"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoInformesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class TipoInformesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.TipoInformesDAO.entity.toLowerCase(), '/public/' + dao_1.TipoInformesDAO.entity.toLowerCase(), dao_1.TipoInformesDAO);
    }
    getMethods() {
        return ['GET', 'POST', 'PUT', 'DELETE'];
    }
    getAllowGuest() {
        return false;
    }
}
exports.TipoInformesEndpoint = TipoInformesEndpoint;
//# sourceMappingURL=TipoInformesEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfertaReferenteHistoricoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const OfertaReferenteHistoricoDAO_1 = require("../orm/dao/OfertaReferenteHistoricoDAO");
class OfertaReferenteHistoricoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(OfertaReferenteHistoricoDAO_1.OfertaReferenteHistoricoDAO.entity.toLowerCase(), '/acap/' + OfertaReferenteHistoricoDAO_1.OfertaReferenteHistoricoDAO.entity.toLowerCase(), OfertaReferenteHistoricoDAO_1.OfertaReferenteHistoricoDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.OfertaReferenteHistoricoEndpoint = OfertaReferenteHistoricoEndpoint;
//# sourceMappingURL=OfertaReferenteHistoricoEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CicloAdultosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class CicloAdultosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.CicloAdultosDAO.entity.toLowerCase(), '/public/' + dao_1.CicloAdultosDAO.entity.toLowerCase(), dao_1.CicloAdultosDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.CicloAdultosEndpoint = CicloAdultosEndpoint;
//# sourceMappingURL=CicloAdultosEndpoint.js.map
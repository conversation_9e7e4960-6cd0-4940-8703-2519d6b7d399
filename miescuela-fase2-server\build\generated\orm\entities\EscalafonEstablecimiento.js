"use strict";
/*
import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Index('escalafon_establecimiento_pkey', ['idEscalafonEstablecimiento'], {
  unique: true,
})
@Entity('escalafon_establecimiento', { schema: 'public' })
export class EscalafonEstablecimiento {
  @PrimaryGeneratedColumn({
    type: 'integer',
    name: 'id_escalafon_establecimiento',
  })
  idEscalafonEstablecimiento: number;

  @Column('character varying', { name: 'descripcion_escalafon', length: 200 })
  descripcionEscalafon: string;
}
*/
//# sourceMappingURL=EscalafonEstablecimiento.js.map
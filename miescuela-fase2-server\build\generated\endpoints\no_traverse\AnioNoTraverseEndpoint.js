"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnioNoTraverseEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class AnioNoTraverseEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.AnioNoTraverseDAO.entity.toLowerCase(), '/public/nt/' + dao_1.AnioNoTraverseDAO.entity.toLowerCase(), dao_1.AnioNoTraverseDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.AnioNoTraverseEndpoint = AnioNoTraverseEndpoint;
//# sourceMappingURL=AnioNoTraverseEndpoint.js.map
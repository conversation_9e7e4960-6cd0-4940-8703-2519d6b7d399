interface Headers {
    [key: string]: string;
}
interface Params {
    [key: string]: string;
}
export declare enum METHOD {
    POST = "post",
    GET = "get",
    PUT = "put",
    DELETE = "delete"
}
type METHOD_OPTION = METHOD.PUT | METHOD.DELETE | METHOD.GET | METHOD.POST;
type RequestOptions = {
    url: string;
    body?: any;
    headers?: Headers;
    method?: METHOD_OPTION;
    params?: Params;
};
export declare class HTTPRequest {
    private applyHeaders;
    private applyParams;
    private config;
    run(requestOptions: RequestOptions): Promise<{
        data: any;
        status: string | number;
        headers: any;
    }>;
}
export {};

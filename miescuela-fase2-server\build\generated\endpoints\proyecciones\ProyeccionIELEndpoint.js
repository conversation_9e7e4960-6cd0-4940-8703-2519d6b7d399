"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProyeccionIELEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ProyeccionIELEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ProyeccionIELDAO.entity.toLowerCase(), '/public/' + dao_1.ProyeccionIELDAO.entity.toLowerCase(), dao_1.ProyeccionIELDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ProyeccionIELEndpoint = ProyeccionIELEndpoint;
//# sourceMappingURL=ProyeccionIELEndpoint.js.map
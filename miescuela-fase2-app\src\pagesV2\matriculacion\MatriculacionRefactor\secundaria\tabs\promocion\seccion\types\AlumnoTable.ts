import {
  type Proyeccion,
  type CombinacionesPase
} from '@miesc/lib/database/models'
import { type AlumnoTableRow } from '../../types/types'
import { type SeccionWithProyeccionV2 } from '@miesc/lib/database/models/SeccionV2'

export interface GetInitialValuesParams {
  alumnosRows: AlumnoTableRow[]
  proyecciones: Proyeccion[]
  combinacionesPase: CombinacionesPase[]
  pendientesCicloAnterioresMap?: Record<string, number>
  pendientesCicloActualMap?: Record<string, number>
  esSegundoPaso?: boolean
  seccionSeleccionada: SeccionWithProyeccionV2
}

export type FormatValuesParams = GetInitialValuesParams

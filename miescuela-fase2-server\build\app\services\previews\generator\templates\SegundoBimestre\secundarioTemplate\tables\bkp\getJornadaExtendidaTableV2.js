"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getJornadaExtendidaTableV2 = void 0;
const getJornadaExtendidaTableV2 = (jornadaExtendida, jeIndicadores, tipoPeriodo) => {
    let html = '<table class="table-bimestres" cellspacing="0" cellpadding="0">';
    jornadaExtendida.forEach((je, index) => {
        var _a, _b;
        if (Object.entries(je.data)
            .filter(([key, _]) => key !== 'observaciones')
            .every(([_, value]) => Boolean(value))) {
            const indicadorNota = jeIndicadores
                .filter((indi) => indi.jeMateria.idJEMateria === je.jemateria.idJEMateria &&
                je.data !== null &&
                indi.key in je.data)
                .map((indicador) => `
          <tr>
            <td class="cell to-left ff-arial fs-10 bt-1 bl-1 br-1">${indicador.pregunta}</td>
            <td class="cell to-left ff-arial fs-10 bt-1 br-1">${je.data ? je.data[indicador.key] : ''}</td>
          </tr>
        `).join('');
            const template = /*html*/ `
        <thead>
          ${index === 0
                ? `<tr class="heading">
            <th class="cell tac bold ff-arial fs-10 bt-1 bgc-gray bl-1 br-1" rowspan="2">
              ESPACIOS EDUCATIVOS DE JORNADA EXTENDIDA
            </th>
            <th class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bgc-gray">
              <p><b>PRIMER CUATRIMESTRE</b></p>
            </th>
            </tr>
            <tr>
              <th class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bgc-gray">
                <b>${tipoPeriodo}</b>
              </th>
            </tr>`
                : ''}
          <tr>
            <th class="cell tac bold ff-arial fs-10 bt-1 bgc-gray bl-1 br-1">${je.jemateria.descripcion}</th>
            <td class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bgc-gray">Calificación</td>
          </tr>
        </thead>
        <tbody>
          ${indicadorNota}
          <tr>
            <td class="cell to-left ff-arial fs-10 bt-1 bl-1 br-1 ${index === jornadaExtendida.length - 1 ? 'bb-1' : ''}">Observaciones</td>
            <td class="cell to-left ff-arial fs-10 bt-1 br-1 wb-all ${index === jornadaExtendida.length - 1 ? 'bb-1' : ''}">${(_b = (_a = je.data) === null || _a === void 0 ? void 0 : _a.observaciones) !== null && _b !== void 0 ? _b : ''}</td>
          </tr>
        </tbody>
  `;
            html += template;
        }
    });
    html += '</table>';
    return html;
};
exports.getJornadaExtendidaTableV2 = getJornadaExtendidaTableV2;
//# sourceMappingURL=getJornadaExtendidaTableV2.js.map
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { MiEscuelaEndpoint } from '../../app/config/endpoint/MiEscuelaEndpoints';
import { Localizacion } from '../orm/entities';
import { JRRequest } from '@phinxlab/just-rpc';
export declare class LocalizacionEndpoint extends MiEscuelaEndpoint<Localizacion> {
    constructor();
    private getUniqueByDistritoEscolar;
    postSelectAction(values: Localizacion[], session: ChinoSessionDTO, req: JRRequest): Promise<Localizacion[]>;
    getAllowGuest(): boolean;
}

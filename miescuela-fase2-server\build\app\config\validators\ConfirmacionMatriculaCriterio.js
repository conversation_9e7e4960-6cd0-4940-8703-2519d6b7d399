"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfirmacionMatriculaCriterioValidator = void 0;
const entities_1 = require("../../../generated/orm/entities");
const MiEscuelaValidator_1 = __importDefault(require("../MiEscuelaValidator"));
class ConfirmacionMatriculaCriterioValidator extends MiEscuelaValidator_1.default {
    constructor() {
        super(entities_1.ConfirmacionMatriculaCriterio);
    }
}
exports.ConfirmacionMatriculaCriterioValidator = ConfirmacionMatriculaCriterioValidator;
//# sourceMappingURL=ConfirmacionMatriculaCriterio.js.map
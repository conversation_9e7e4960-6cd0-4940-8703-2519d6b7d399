"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodigoVinculoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class CodigoVinculoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.CodigoVinculoDAO.entity.toLowerCase(), '/public/' + dao_1.CodigoVinculoDAO.entity.toLowerCase(), dao_1.CodigoVinculoDAO);
    }
    getAllowGuest() {
        return false;
    }
    getMethods() {
        return ['GET', 'POST'];
    }
}
exports.CodigoVinculoEndpoint = CodigoVinculoEndpoint;
//# sourceMappingURL=CodigoVinculoEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class DiaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.DiaDAO.entity.toLowerCase(), '/public/dias', dao_1.DiaDAO);
    }
    getAllowGuest() {
        return true;
    }
}
exports.DiaEndpoint = DiaEndpoint;
//# sourceMappingURL=DiaEndpoint.js.map
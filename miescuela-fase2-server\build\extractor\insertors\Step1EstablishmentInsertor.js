"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Step1EstablishmentInsertor = void 0;
const lib_1 = require("../lib");
const utils_1 = require("../utils");
const Logger = new lib_1.LogManager(`Insertor 1`);
class Step1EstablishmentInsertor {
    async run(context, target, targetContext) {
        // Modality
        Logger.info('Modality');
        for (const value of context.modality) {
            const { rows, } = await target.query(`INSERT INTO modalidad (id_modalidad, descripcion) VALUES ($1, $2) RETURNING *;`, [value.idmodalidad, value.descripcion]);
            targetContext.modality = (0, utils_1.addToArray)(targetContext.modality, rows);
        }
        // functionalDependency
        Logger.info('functionalDependency');
        for (const value of context.functionalDependency) {
            const { rows, } = await target.query(`INSERT INTO dependencia_funcional (id_dependencia_funcional, descripcion) VALUES ($1, $2) RETURNING *;`, [value.iddependenciafuncional, value.descripcion]);
            targetContext.functionalDependency = (0, utils_1.addToArray)(targetContext.functionalDependency, rows);
        }
        // scholarDistrict
        Logger.info('scholarDistrict');
        for (const value of context.scholarDistrict) {
            const { rows, } = await target.query(`INSERT INTO distrito_escolar (id_distrito_escolar, nombre, romano) VALUES ($1, $2, $3) RETURNING *;`, [value.iddistritoescolar, value.nombre, value.romano]);
            targetContext.scholarDistrict = (0, utils_1.addToArray)(targetContext.scholarDistrict, rows);
        }
        // supervision
        Logger.info('supervision');
        for (const value of context.supervision) {
            const { rows, } = await target.query(`INSERT INTO supervision (id_supervision, descripcion, nombre) VALUES ($1, $2, $3) RETURNING *;`, [value.idsupervision, value.descripcion, value.nombre]);
            targetContext.supervision = (0, utils_1.addToArray)(targetContext.supervision, rows);
        }
        // establishmentType
        Logger.info('establishmentType');
        for (const value of context.establishmentType) {
            const { rows, } = await target.query(`INSERT INTO tipo_establecimiento (id_tipo_establecimiento, descripcion, abreviatura) VALUES ($1, $2, $3) RETURNING *;`, [value.idtipoestablecimiento, value.descripcion, value.abreviatura]);
            targetContext.establishmentType = (0, utils_1.addToArray)(targetContext.establishmentType, rows);
        }
        // establishment
        Logger.info('establishment');
        for (const value of context.establishment) {
            const { rows, } = await target.query(`INSERT INTO establecimiento (id_establecimiento, nombre, cue, nombre_abreviado, numero, id_tipo_establecimiento, id_supervision, id_distrito_escolar, id_dependencia_funcional, id_modalidad) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *;`, [
                value.idestablecimiento,
                value.nombre,
                value.cue,
                value.nombreabreviado,
                value.numero,
                value.idtipoestablecimiento,
                value.idsupervision,
                value.iddistritoescolar,
                value.iddependenciafuncional,
                value.idmodalidad,
            ]);
            targetContext.establishment = (0, utils_1.addToArray)(targetContext.establishment, rows);
        }
        return targetContext;
    }
}
exports.Step1EstablishmentInsertor = Step1EstablishmentInsertor;
//# sourceMappingURL=Step1EstablishmentInsertor.js.map
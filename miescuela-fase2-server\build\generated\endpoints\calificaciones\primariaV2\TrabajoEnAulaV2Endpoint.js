"use strict";
// import { MiEscuelaEndpoint } from '../../../../app/config';
// import { QuehaceresDAO } from '../../../orm/dao';
// import { Quehaceres } from '../../../orm/entities';
// export class TrabajoEnAulaV2Endpoint extends MiEscuelaEndpoint<Quehaceres> {
//   constructor() {
//     super(
//       QuehaceresDAO.entity.toLowerCase(),
//       '/v2/calificaciones/trabajoenaula',
//       QuehaceresDAO,
//     );
//   }
//   getAllowGuest(): boolean {
//     return false;
//   }
// }
//# sourceMappingURL=TrabajoEnAulaV2Endpoint.js.map
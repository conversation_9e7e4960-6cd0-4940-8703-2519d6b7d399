"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const NewJoiErrorBuilder_1 = require("./builder/NewJoiErrorBuilder");
const PersonaAltaAlumnosBuilder_1 = require("./builder/PersonaAltaAlumnosBuilder");
const AltaMasivaAlumnoDAOUtils_1 = __importDefault(require("../utils/AltaMasivaAlumnoDAOUtils"));
jest.mock('@phinxlab/chino-sdk', () => ({
    ChinoContext: jest.fn().mockImplementation(() => ({
        session: {
            user: {
                id: '123',
            },
        },
    })),
    ChinoCustomError: jest.fn().mockImplementation((message, code) => ({
        message,
        code,
    })),
}));
describe('AltaMasivaAlumnoDAO', () => {
    it('getDataWithinErrors_ConErrores_RetornaAlumnoMovimientoConErrores', () => {
        const personaAltaAlumno = new PersonaAltaAlumnosBuilder_1.PersonaAltaAlumnosBuilder()
            .withCUEAnexo(12345678)
            .withNombre('Juan')
            .withApellido('Pérez')
            .withPais('Argentina')
            .withGenero('masculino')
            .withFechaNacimiento('2005-05-05')
            .withTipoDocumento('dni')
            .withDocumento('30123123')
            .withCicloLectivo('2025')
            .withTurno('Mañana')
            .withNivel('Secundario')
            .withAnio('2do')
            .withObservacionesError()
            .build();
        const errorMock = new NewJoiErrorBuilder_1.NewJoiErrorBuilder(personaAltaAlumno)
            .withInvalidData()
            .build();
        const result = AltaMasivaAlumnoDAOUtils_1.default.getDataWithinErrors(errorMock, [
            personaAltaAlumno,
        ]);
        expect(result[0]).toEqual({
            ...personaAltaAlumno,
            errores: {
                CUEAnexo: 'El CUE Anexo debe tener un valor mayor a 10000000',
                Nombre: 'El Nombre es obligatorio.',
                FechaNacimiento: 'La fecha de nacimiento no puede ser anterior al 1 de enero de 1930',
                Documento: 'El DNI debe ser solo numerico',
                CicloLectivo: 'El Ciclo Lectivo debe ser mayor a 2024',
            },
        });
    });
    it('getDataWithinErrors_SinErrores_RetornaAlumnoMovimientoSinErrores', () => {
        const personaAltaAlumno = new PersonaAltaAlumnosBuilder_1.PersonaAltaAlumnosBuilder()
            .withCUEAnexo(12345678)
            .withNombre('Juan')
            .withApellido('Pérez')
            .withPais('Argentina')
            .withGenero('masculino')
            .withFechaNacimiento('2005-05-05')
            .withTipoDocumento('dni')
            .withDocumento('30123123')
            .withCicloLectivo('2025')
            .withTurno('Mañana')
            .withNivel('Secundario')
            .withAnio('2do')
            .build();
        const errorMock = new NewJoiErrorBuilder_1.NewJoiErrorBuilder(personaAltaAlumno)
            .withValidData()
            .build();
        const result = AltaMasivaAlumnoDAOUtils_1.default.getDataWithinErrors(errorMock, [
            personaAltaAlumno,
        ]);
        expect(result[0]).toEqual({
            ...personaAltaAlumno,
        });
    });
    it('getDataWithinErrors_ConErroresVacios_RetornaAlumnoMovimientoSinErroresYObservaciones', () => {
        const personaAltaAlumno = new PersonaAltaAlumnosBuilder_1.PersonaAltaAlumnosBuilder()
            .withCUEAnexo(12345678)
            .withNombre('Juan')
            .withApellido('Pérez')
            .withPais('Argentina')
            .withGenero('masculino')
            .withFechaNacimiento('2005-05-05')
            .withTipoDocumento('dni')
            .withDocumento('30123123')
            .withCicloLectivo('2025')
            .withTurno('Mañana')
            .withNivel('Secundario')
            .withAnio('2do')
            .build();
        const errorMock = new NewJoiErrorBuilder_1.NewJoiErrorBuilder(personaAltaAlumno)
            .withZeroErrors()
            .build();
        const result = AltaMasivaAlumnoDAOUtils_1.default.getDataWithinErrors(errorMock, [
            {
                ...personaAltaAlumno,
            },
        ]);
        expect(result[0]).toEqual({
            ...personaAltaAlumno,
            Observaciones: 'Datos en forma correcta',
        });
    });
    it('JoiValidation_ConInvocacion_RetornaJoiArray', () => {
        const joiValidation = AltaMasivaAlumnoDAOUtils_1.default.JoiValidation();
        expect(joiValidation.type).toBe('array');
    });
    it('docDuplicateValidation_DocumentosDuplicados_LanzaError', () => {
        const documentos = ['12345678', '12345678', '12345678', '12345678'];
        expect(() => AltaMasivaAlumnoDAOUtils_1.default.docDuplicateValidation(documentos)).toThrowError('Hay personas con Documento duplicado');
    });
    it('docDuplicateValidation_DocumentosDuplicados_LanzaError', () => {
        const documentos = ['12345678', '87654321'];
        expect(() => AltaMasivaAlumnoDAOUtils_1.default.docDuplicateValidation(documentos)).not.toThrowError('Hay personas con Documento duplicado');
    });
    it('validateUniqueLocalizacion_ConOtrasLocalizaciones_RetornaError', () => {
        const localizaciones = [44444444, 66666666, 44444444, 66666666];
        expect(() => AltaMasivaAlumnoDAOUtils_1.default.validateUniqueLocalizacion(localizaciones)).toThrowError('Hay mas de Una Escuela');
    });
    it('localizacionDuplicateValidation_ConUnaSolaLocalizacionEnTodasLasFilas_RetornaLaUnicaLocalizacion', () => {
        const localizaciones = [44444444, 44444444];
        const localizacionesNoRepetidas = AltaMasivaAlumnoDAOUtils_1.default.validateUniqueLocalizacion(localizaciones);
        expect(localizacionesNoRepetidas).toEqual([44444444]);
    });
    it('localizacionDuplicateValidation_ConUnaSolaLocalizacion_RetornaLaUnicaLocalizacion', () => {
        const localizaciones = [44444444];
        const localizacionesNoRepetidas = AltaMasivaAlumnoDAOUtils_1.default.validateUniqueLocalizacion(localizaciones);
        expect(localizacionesNoRepetidas).toEqual([44444444]);
    });
});
//# sourceMappingURL=AltaMasivaAlumnoDAO.test.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PPSEndpoint = void 0;
const UploadFile_1 = require("../../utils/UploadFile");
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
const upload = (0, UploadFile_1.createValidation)([
    UploadFile_1.knownTypes['application/pdf'],
    UploadFile_1.knownTypes['application/msword'],
    UploadFile_1.knownTypes['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    UploadFile_1.knownTypes['image/jpeg'],
    UploadFile_1.knownTypes['image/png'],
    UploadFile_1.knownTypes['image/webp'],
], 10 * 1024 * 1024);
class PPSEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PPSDAO.entity.toLowerCase(), '/calificaciones/' + dao_1.PPSDAO.entity.toLowerCase(), dao_1.PPSDAO);
    }
    getAllowGuest() {
        return false;
    }
    async handleAdjuntos(req, session) {
        var _a, _b, _c, _d, _e, _f;
        const t = req.body;
        if ((_b = (_a = t.intervenciones) === null || _a === void 0 ? void 0 : _a.informe) === null || _b === void 0 ? void 0 : _b.base64) {
            t.intervenciones.informe.url = (_c = (await upload(t.intervenciones.informe.filename, t.intervenciones.informe.base64, t.intervenciones.informe.mimeType))) === null || _c === void 0 ? void 0 : _c.webViewLink;
            delete t.intervenciones.informe.base64;
            delete t.intervenciones.informe.mimeType;
        }
        if ((_e = (_d = t.antecedentes) === null || _d === void 0 ? void 0 : _d.informe) === null || _e === void 0 ? void 0 : _e.base64) {
            t.antecedentes.informe.url = (_f = (await upload(t.antecedentes.informe.filename, t.antecedentes.informe.base64, t.antecedentes.informe.mimeType))) === null || _f === void 0 ? void 0 : _f.webViewLink;
            delete t.antecedentes.informe.base64;
            delete t.antecedentes.informe.mimeType;
        }
    }
    async preUpdateAction(req, session) {
        return await this.handleAdjuntos(req, session);
    }
    async preInsertAction(req, session) {
        return await this.handleAdjuntos(req, session);
    }
}
exports.PPSEndpoint = PPSEndpoint;
//# sourceMappingURL=PPSEndpoint.js.map
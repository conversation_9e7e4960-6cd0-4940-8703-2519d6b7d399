import { JRRequest, JRResponse } from '@phinxlab/just-rpc';
import { MiEscuelaEndpointCustom } from '../../../app/config';
import { EntityManager } from 'typeorm';
export declare class CalificacionesF2CustomEndpoint extends MiEscuelaEndpointCustom {
    constructor(name?: string, path?: string);
    exec(req: JRRe<PERSON>, manager: EntityManager, res: JRResponse): Promise<any>;
    configure(): void;
}

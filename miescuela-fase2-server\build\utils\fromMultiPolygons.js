"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromMultipolygonStringToArray = void 0;
const fromMultipolygonStringToArray = (data) => {
    if (!data.includes('MULTIPOLYGON')) {
        return [];
    }
    const commaArray = data
        .replace('MULTIPOLYGON (((', '')
        .replace(')))', '')
        .split(', ');
    return commaArray.reduce((array, item) => {
        const coordinate = item.split(' ');
        return [...array, [parseFloat(coordinate[1]), parseFloat(coordinate[0])]];
    }, []);
};
exports.fromMultipolygonStringToArray = fromMultipolygonStringToArray;
//# sourceMappingURL=fromMultiPolygons.js.map
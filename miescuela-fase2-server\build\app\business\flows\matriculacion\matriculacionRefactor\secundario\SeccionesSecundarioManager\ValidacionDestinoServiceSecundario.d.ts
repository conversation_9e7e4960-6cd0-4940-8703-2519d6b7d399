import { Seccion } from '../../../../../../../generated/orm/entities';
import { SeccionesRepository } from '../../repositories/SeccionesRepository';
import { EntityManager } from 'typeorm';
import { ChinoContext } from '@phinxlab/chino-sdk';
import { MiEscuelaUser } from '../../../../..';
export declare class ValidacionDestinoServiceSecundario {
    private readonly seccionesRepository;
    constructor(seccionesRepository?: SeccionesRepository);
    getSeccionesConDestinoCombinaciones(secciones: Seccion[], user: MiEscuelaUser, tx: EntityManager, context: ChinoContext): Promise<string[]>;
    private getSeccionesActuales;
    private getCicloLectivoSiguiente;
    private getSeccionesDestino;
    private getCombinacionesPorAnios;
    private calcularSeccionesConDestino;
    private tieneDestinoValido;
    private obtenerAniosDestino;
    private isEstadoPaseSextoAnio;
    private existeSeccionDestinoCompatible;
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObservacionesLMEndpoint = void 0;
const dao_1 = require("../../orm/dao");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
class ObservacionesLMEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ObservacionesLMDAO.entity.toLowerCase(), '/observaciones', dao_1.ObservacionesLMDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ObservacionesLMEndpoint = ObservacionesLMEndpoint;
//# sourceMappingURL=ObservacionesEndpoint.js.map
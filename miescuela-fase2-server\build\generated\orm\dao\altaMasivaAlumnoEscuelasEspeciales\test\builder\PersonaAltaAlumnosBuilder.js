"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonaAltaAlumnosBuilder = void 0;
class PersonaAltaAlumnosBuilder {
    constructor() {
        this.persona = {
            CUEAnexo: 0,
            Nombre: '',
            <PERSON><PERSON><PERSON><PERSON>: '',
            Pa<PERSON>: '',
            <PERSON><PERSON>: '',
            <PERSON>chaNacimiento: '',
            TipoDocumento: '',
            Documento: '0',
            CicloLectivo: '',
            <PERSON><PERSON>: '',
            <PERSON><PERSON>: '',
            <PERSON><PERSON>: '',
        };
    }
    withCUEAnexo(cueAnexo) {
        this.persona.CUEAnexo = cueAnexo;
        return this;
    }
    withNombre(nombre) {
        this.persona.Nombre = nombre;
        return this;
    }
    with<PERSON><PERSON>lido(apellido) {
        this.persona.Apellido = apellido;
        return this;
    }
    withPais(pais) {
        this.persona.Pais = pais;
        return this;
    }
    withGenero(genero) {
        this.persona.Genero = genero;
        return this;
    }
    withFechaNacimiento(fecha) {
        this.persona.FechaNacimiento = fecha;
        return this;
    }
    withTipoDocumento(tipo) {
        this.persona.TipoDocumento = tipo;
        return this;
    }
    withDocumento(documento) {
        this.persona.Documento = documento.toString();
        return this;
    }
    withCicloLectivo(ciclo) {
        this.persona.CicloLectivo = ciclo;
        return this;
    }
    withTurno(turno) {
        this.persona.Turno = turno;
        return this;
    }
    withNivel(nivel) {
        this.persona.Nivel = nivel;
        return this;
    }
    withAnio(anio) {
        this.persona.Anio = anio;
        return this;
    }
    withObservacionesError() {
        this.persona.Observaciones = 'Esta fila no se puede procesar';
        return this;
    }
    build() {
        return { ...this.persona };
    }
}
exports.PersonaAltaAlumnosBuilder = PersonaAltaAlumnosBuilder;
//# sourceMappingURL=PersonaAltaAlumnosBuilder.js.map
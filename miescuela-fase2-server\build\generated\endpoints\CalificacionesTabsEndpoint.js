"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalificacionesTabsEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class CalificacionesTabsEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.CalificacionesTabsDAO.entity.toLowerCase(), '/calificaciones/' + dao_1.CalificacionesTabsDAO.entity.toLowerCase(), dao_1.CalificacionesTabsDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.CalificacionesTabsEndpoint = CalificacionesTabsEndpoint;
//# sourceMappingURL=CalificacionesTabsEndpoint.js.map
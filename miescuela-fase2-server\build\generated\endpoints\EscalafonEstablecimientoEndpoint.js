"use strict";
/*
import { MiEscuelaEndpoint } from '../../app/config/endpoint/MiEscuelaEndpoints';
import { EscalafonEstablecimientoDAO } from '../orm/dao';
import { EscalafonEstablecimiento } from '../orm/entities';

export class EscalafonEstablecimientoEndpoint extends MiEscuelaEndpoint<EscalafonEstablecimiento> {
  constructor() {
    super(
      EscalafonEstablecimientoDAO.entity.toLowerCase(),
      '/public/' + EscalafonEstablecimientoDAO.entity.toLowerCase(),
      EscalafonEstablecimientoDAO,
    );
  }

  getAllowGuest(): boolean {
    return false;
  }
}
*/
//# sourceMappingURL=EscalafonEstablecimientoEndpoint.js.map
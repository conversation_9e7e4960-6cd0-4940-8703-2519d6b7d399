"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeccionDocenteEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const SeccionDocenteDAO_1 = require("../../orm/dao/custom/SeccionDocenteDAO");
class SeccionDocenteEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(SeccionDocenteDAO_1.SeccionDocenteDAO.entity.toLowerCase(), `/custom/${SeccionDocenteDAO_1.SeccionDocenteDAO.entity.toLowerCase()}`, SeccionDocenteDAO_1.SeccionDocenteDAO);
    }
    async preDeleteAction(req, session) {
        var _a;
        await super.preDeleteAction(req, session);
        const context = new chino_sdk_1.ChinoContext();
        context.session = session;
        const seccion = await SeccionDocenteDAO_1.SeccionDocenteDAO.findByPK(req.body.idSeccion, {
            isSelect: true,
            context,
        });
        if ((_a = seccion.anio) === null || _a === void 0 ? void 0 : _a.idAnio) {
            const espacios = await dao_1.EspacioCurricularSeccionDAO.query()
                .setContext(context)
                .equals('seccion', req.body.idSeccion)
                .run();
            await Promise.all(espacios.map((espacio) => dao_1.EspacioCurricularSeccionDAO.remove(espacio, undefined, {
                isRemove: true,
                context,
            })));
        }
    }
    getAllowGuest() {
        return false;
    }
}
exports.SeccionDocenteEndpoint = SeccionDocenteEndpoint;
//# sourceMappingURL=SeccionDocenteEndpoint.js.map
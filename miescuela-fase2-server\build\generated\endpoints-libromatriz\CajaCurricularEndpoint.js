"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CajaCurricularEndpoint = void 0;
const just_rpc_1 = require("@phinxlab/just-rpc");
const config_1 = require("../../app/config");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const business_1 = require("../../app/business");
const const_1 = require("../../app/const");
const Log = new chino_sdk_1.ChinoLogManager('POST CAJA CURRICULAR FLOW');
class CajaCurricularEndpoint extends config_1.MiEscuelaEndpointCustom {
    constructor(name = 'lm_caja_curricular', path = '/caja-curricular') {
        super(name, path);
    }
    configure() {
        this.allowGuest = false;
        this.addMethod(just_rpc_1.JRMethod.GET);
        this.addMethod(just_rpc_1.JRMethod.PUT);
        this.addMethod(just_rpc_1.JRMethod.POST);
    }
    async exec(req, tx) {
        try {
            switch (req.method) {
                case 'GET':
                    return await (0, business_1.FlowManager)({
                        flow: const_1.FLOWS.GET_CAJA_CURRICULAR,
                        tx,
                        req,
                    }).run();
                case 'PUT':
                case 'POST':
                    return await (0, business_1.FlowManager)({
                        flow: const_1.FLOWS.POST_CAJA_CURRICULAR,
                        tx,
                        req,
                    }).run();
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('error', e);
            const err = e;
            // TODO NOT CHANGE, LIBBY NOT RECONGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(err.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
}
exports.CajaCurricularEndpoint = CajaCurricularEndpoint;
//# sourceMappingURL=CajaCurricularEndpoint.js.map
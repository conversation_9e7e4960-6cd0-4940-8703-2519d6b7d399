"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ cicloLectivo, anio, tipoPeriodo, acuerdosGrupales, situacionesDeJuego, resuelveConflictos, repetuosoConAdultos, repetuosoConSusPares, }) => `
<table class="tabla">
        <tr class="title-table">
            <td style="background-color: #e2eedb; font-weight: bold; font-size: 12px;">
                ${cicloLectivo} - ${anio}
            </td>
            <td style="background-color: #e2eedb; border-bottom: 0px; padding-top: 42px; font-weight: bold; font-size: 12px;">
                ${tipoPeriodo}
            </td>
        </tr>
        <tr>
            <div>
                <td style=" background-color: #ececec; font-weight: bold;">CONVIVENCIA</td>
            </div>
            <td style="background-color: #e2eedb;"></td>
        </tr>
        <tr>
            <td>Acepta acuerdos grupales y normas institucionales.</td>
            <td> ${acuerdosGrupales}</td>
        </tr>
        <tr>
            <td>Participa en situaciones de juego.</td>
            <td> ${situacionesDeJuego}</td>
        </tr>
        <tr>
            <td>Resuelve conflictos a través del diálogo.</td>
            <td> ${resuelveConflictos}</td>
        </tr>
        <tr>
            <td>Se vincula de manera respetuosa con los adultos.</td>
            <td> ${repetuosoConAdultos}</td>
        </tr>
        <tr>
            <td>Se vincula de manera respetuosa con sus pares.</td>
            <td> ${repetuosoConSusPares}</td>
        </tr>
    </table> `;
//# sourceMappingURL=getConvivenciaTable.js.map
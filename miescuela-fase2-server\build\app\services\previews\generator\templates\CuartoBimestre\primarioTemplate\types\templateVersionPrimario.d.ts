import { PromocionBoletin } from 'app/business/flows/boletines/types';
import { PresentismoTotal } from 'app/business/flows/reports/types';
import { AspectosGeneralesData, Convivencia, InformesValorativosData, ObservacionesBoletin, ObservacionesData, Quehaceres, Seccion, SintesisConceptualData } from 'generated/orm/entities';
import { AreasConocimientos } from '../tables/getAreaConocimientosTablesCB';
import { JornadaExtendidaData } from '../tables/getJornadaExtendidaTableCB';
export type Params = {
    localizacion: string;
    distritoEscolar: string;
    ciclo: string;
    tipoPeriodo: string;
    apellido: string;
    nombre: string;
    anio: string;
    anioCicloLectivo: string;
    aspectosGenerales: boolean | AspectosGeneralesData;
    queHaceres: boolean | Quehaceres;
    convivencia: boolean | Convivencia;
    observaciones: boolean | ObservacionesData;
    JeIsExist: boolean;
    division: string;
    codigoVinculo: string;
    primario: AreasConocimientos;
    jornadaExtendida: JornadaExtendidaData;
    sintesisConceptual: SintesisConceptualData;
    promocion: PromocionBoletin;
    presentismo: PresentismoTotal;
    presentismoJe: PresentismoTotal;
    observacionBoletin: ObservacionesBoletin[];
    informeValorativo: boolean | InformesValorativosData;
    seccion: Seccion;
    escudo: string;
    turno: string;
    tipoJornada: string;
    dependenciaFuncional: string;
    tipoDocumento: string;
    documento: string;
};

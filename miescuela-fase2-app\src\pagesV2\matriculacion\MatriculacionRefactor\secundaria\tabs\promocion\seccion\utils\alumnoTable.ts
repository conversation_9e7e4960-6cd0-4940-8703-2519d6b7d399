import { type Seccion, type AlumnoMovimiento, type CombinacionesPase, type EstadoPaseAnio, type Proyeccion, type DependenciaFuncional, type Anio } from '@miesc/lib/database/models'
import { type AccionPase } from '@miesc/lib/database/models/AccionPase'
import { type EstadoProyeccionMotivoCombinacion } from '@miesc/lib/database/models/matriculacion'
import { capitalizarPalabras } from '@miesc/pagesV2/presentismo/ReporteAlumno/util/functions'
import { type AlumnoTableRow } from '../../../promocion/types/types'
import { type DefaultSelectsValues } from '../hooks/useSeccionSecundariaDetalle/types'
import { getSelectOptionsBasedOnCombinations } from './combinaciones'
import { ESTADO_PASE_ANIO } from '@miesc/const/estadosPase'
import { ESTADO_SECCION_PROYECTADA_CODES } from '@miesc/const/estadoSeccionProyectada'
import {
  getEstadoPaseAnioPorDefectoConMateriasPendientes,
  type MateriasPendientesInfo
} from './materiasPendientesHelper'

export function getAllEstadoPaseAnioOptions(combinaciones: CombinacionesPase[]): EstadoPaseAnio[] {
  const map = new Map<number, EstadoPaseAnio>()
  for (const item of combinaciones) {
    if (item.estadoPaseAnio) {
      map.set(item.estadoPaseAnio.idEstadoPaseAnio, item.estadoPaseAnio)
    }
  }
  return Array.from(map.values())
}

export function getAllAccionPaseOptions(combinaciones: CombinacionesPase[]): AccionPase[] {
  const map = new Map<number, AccionPase>()
  for (const item of combinaciones) {
    if (item.accionPase) {
      map.set(item.accionPase.idAccionPase, item.accionPase)
    }
  }
  return Array.from(map.values())
}

export function getAllMotivosOptions(combinaciones: CombinacionesPase[]): EstadoProyeccionMotivoCombinacion[] {
  const map = new Map<string, EstadoProyeccionMotivoCombinacion>()
  for (const item of combinaciones) {
    if (item.estadoProyeccionMotivo) {
      map.set(item.estadoProyeccionMotivo.idEstadoProyeccionMotivo, item.estadoProyeccionMotivo)
    }
  }
  return Array.from(map.values())
}

export function getAlumnoRows(alumnos: AlumnoMovimiento[]): AlumnoTableRow[] {
  return alumnos.map(alumno => {
    return {
      id: alumno.idAlumnoMovimiento,
      alumnoId: alumno.alumno.idAlumno,
      apellidoYNombre: `${capitalizarPalabras(alumno.alumno.persona.apellido)}, ${capitalizarPalabras(alumno.alumno.persona.nombre)}`,
      documento: alumno.alumno.persona.documento,
      condicion: alumno.alumno.condicion?.descripcionCondicion || alumno.estadoAlumno?.descripcionEstadoPase || 'Regular',
      seccionActual: alumno.seccion.nombreSeccion,
      seccionDestino: null,
      isSelected: false,
      estadoPaseAnio: null,
      accionPase: null,
      motivo: null,
      observacion: null,
      pendientesCicloAnteriores: 0,
      pendientesCicloActual: 0,
      adjunto: null
    }
  })
}

export function getAccionPaseForTable(accionPase: AccionPase | null, seccionDestino: Seccion | null, combinaciones: CombinacionesPase[]): AccionPase | null {
  if (accionPase && !seccionDestino) return accionPase
  if ((!accionPase && seccionDestino) || (accionPase && seccionDestino)) {
    // El accion pase apunta a una seccion destino
    return combinaciones.find(combinacion => combinacion?.accionPase?.idAccionPase.toString() === seccionDestino.idSeccion.toString())?.accionPase ?? null
  }
  // La proyeccion no tiene ni accion pase ni seccion destino
  return null
}

export const getEstadoPaseAnioPorDefecto = (estadosPaseAnio: EstadoPaseAnio[]) => {
  return (estadosPaseAnio.find((estadoPaseAnio) => estadoPaseAnio.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCION_DIRECTA) ??
  estadosPaseAnio.find((estadoPaseAnio) => estadoPaseAnio.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCIONA) ??
   estadosPaseAnio.find((estadoPaseAnio) => estadoPaseAnio.idEstadoPaseAnio === ESTADO_PASE_ANIO.EGRESA) ??
   estadosPaseAnio.find((estadoPaseAnio) => estadoPaseAnio.idEstadoPaseAnio === ESTADO_PASE_ANIO.PERMANECE)) as EstadoPaseAnio
}

const getDefaultValues = (
  combinacionesPase: CombinacionesPase[],
  materiasPendientes?: MateriasPendientesInfo,
  esSegundoPaso?: boolean,
  dependenciaFuncional?: DependenciaFuncional,
  anio?: Anio,
  estadoPaseAnioAnterior?: EstadoPaseAnio | null
): DefaultSelectsValues => {
  const allEstados = getAllEstadoPaseAnioOptions(combinacionesPase)

  let estadoPaseAnioPorDefecto: EstadoPaseAnio
  if (materiasPendientes && esSegundoPaso !== undefined) {
    const estadoConMateriasPendientes = getEstadoPaseAnioPorDefectoConMateriasPendientes({
      materiasPendientes,
      esSegundoPaso,
      estadosPaseAnio: allEstados,
      combinacionesPase,
      dependenciaFuncional,
      anio,
      estadoPaseAnioAnterior
    })
    estadoPaseAnioPorDefecto = estadoConMateriasPendientes || getEstadoPaseAnioPorDefecto(allEstados)
  } else {
    estadoPaseAnioPorDefecto = getEstadoPaseAnioPorDefecto(allEstados)
  }

  const resultAccion =
        getSelectOptionsBasedOnCombinations({
          combinacionesPase,
          filtro: {
            campo: 'estadoPaseAnio',
            valor: Number(estadoPaseAnioPorDefecto.idEstadoPaseAnio)
          }
        })
  const accionesOptions = resultAccion.accionPase ?? []

  const accion = accionesOptions?.[0] ?? null

  return {
    estadoPaseAnioPorDefecto,
    accionPasePorDefecto: accion || null,
    motivoPorDefecto: null
  }
}

export const formatInitialValues = (
  alumnosRows: AlumnoTableRow[],
  proyecciones: Proyeccion[],
  combinacionesPase: CombinacionesPase[],
  estadoSeccionProyectadaCode: string,
  pendientesCicloAnterioresMap?: Record<string, number>,
  pendientesCicloActualMap?: Record<string, number>,
  esSegundoPaso?: boolean,
  dependenciaFuncional?: DependenciaFuncional,
  anio?: Anio
) => {
  const formatted: AlumnoTableRow[] = alumnosRows.map(alumno => {
    const proyeccionAlumno: Proyeccion | undefined = proyecciones.find((proyeccion: Proyeccion) => proyeccion.alumno.idAlumno === alumno.alumnoId)

    // Deudas anteriores usa la tabla deuda_academica, que no conoce alumnoMovimiento
    const pendientesCicloAnteriores = pendientesCicloAnterioresMap?.[alumno.alumnoId] || 0
    // Deudas actuales si conoce el alumnoMovimiento, el idAlumnoMovimiento es alumno.id
    const pendientesCicloActual = pendientesCicloActualMap?.[alumno.id] || 0

    const materiasPendientes: MateriasPendientesInfo = {
      pendientesCicloAnteriores,
      pendientesCicloActual
    }

    let estadoPaseAnio: EstadoPaseAnio | null = null
    let accionPase: AccionPase | null = null
    let motivo: EstadoProyeccionMotivoCombinacion | null = null
    let observacion: string | null = null
    let adjunto: string | null = null

    // Obtener el estado anterior de la proyección si existe
    const estadoPaseAnioAnterior = proyeccionAlumno?.estadoPaseAnio ?? null

    if (!proyeccionAlumno) {
      const { estadoPaseAnioPorDefecto, accionPasePorDefecto }: DefaultSelectsValues = getDefaultValues(
        combinacionesPase,
        materiasPendientes,
        esSegundoPaso,
        dependenciaFuncional,
        anio,
        estadoPaseAnioAnterior
      )

      estadoPaseAnio = estadoPaseAnioPorDefecto
      accionPase = accionPasePorDefecto
      observacion = null
      adjunto = null

      // Valores por defecto si es un alumno pendiente despues de reapertura
      if (estadoSeccionProyectadaCode === ESTADO_SECCION_PROYECTADA_CODES.PARCIAL) {
        estadoPaseAnio = null
        accionPase = null
      }
    }
    if (proyeccionAlumno) {
      // Para alumnos con proyección existente, usar las reglas para determinar el estado por defecto
      const { estadoPaseAnioPorDefecto, accionPasePorDefecto }: DefaultSelectsValues = getDefaultValues(
        combinacionesPase,
        materiasPendientes,
        esSegundoPaso,
        dependenciaFuncional,
        anio,
        estadoPaseAnioAnterior
      )

      // Si las reglas determinan un estado diferente al guardado, usar el de las reglas
      estadoPaseAnio = estadoPaseAnioPorDefecto || (proyeccionAlumno?.estadoPaseAnio ?? null)
      accionPase = accionPasePorDefecto || (proyeccionAlumno?.accionPase ?? null)
      motivo = proyeccionAlumno.estadoProyeccionMotivo
        ? {
          idEstadoProyeccionMotivo: proyeccionAlumno.estadoProyeccionMotivo.idEstadoProyeccionMotivo.toString(),
          descripcion: proyeccionAlumno?.estadoProyeccionMotivo.descripcion
        }
        : null
      observacion = proyeccionAlumno?.observacion ?? null
      adjunto = proyeccionAlumno?.adjunto ? JSON.stringify(proyeccionAlumno.adjunto) : null
    }
    const accionPaseForTable = getAccionPaseForTable(accionPase, proyeccionAlumno?.seccionDestino ?? null, combinacionesPase)

    return {
      ...alumno,
      estadoPaseAnio,
      seccionDestino: proyeccionAlumno?.seccionDestino ?? null,
      motivo,
      accionPase: accionPaseForTable,
      observacion,
      adjunto,
      pendientesCicloAnteriores,
      pendientesCicloActual
    }
  })
  return formatted
}

export const getInitialValues = (
  alumnosRows: AlumnoTableRow[],
  proyecciones: Proyeccion[],
  combinacionesPase: CombinacionesPase[],
  estadoSeccionProyectadaCode: string,
  pendientesCicloAnterioresMap?: Record<string, number>,
  pendientesCicloActualMap?: Record<string, number>,
  esSegundoPaso?: boolean,
  dependenciaFuncional?: DependenciaFuncional,
  anio?: Anio
) => {
  const formatted: AlumnoTableRow[] = formatInitialValues(
    alumnosRows,
    proyecciones,
    combinacionesPase,
    estadoSeccionProyectadaCode,
    pendientesCicloAnterioresMap,
    pendientesCicloActualMap,
    esSegundoPaso,
    dependenciaFuncional,
    anio
  )
  return formatted
}

import { DB } from './commons/database';
import { IExtractable } from './IExtractable';
import { ExtractorContext, LogManager } from './commons';
import { IInsertable } from './IInsertable';
export type Configs = {
    source: any;
    target: any;
    extractors: IExtractable[];
    insertors: IInsertable[];
};
export declare class Extractor {
    protected source: DB;
    protected target: DB;
    protected extractors: IExtractable[];
    protected insertors: IInsertable[];
    protected log: LogManager;
    constructor({ source, target, extractors, insertors }: Configs);
    run(): Promise<{
        source: ExtractorContext;
        target: ExtractorContext;
    } | void>;
}

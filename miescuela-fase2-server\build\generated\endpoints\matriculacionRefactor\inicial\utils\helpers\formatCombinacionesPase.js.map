{"version": 3, "file": "formatCombinacionesPase.js", "sourceRoot": "", "sources": ["../../../../../../../src/generated/endpoints/matriculacionRefactor/inicial/utils/helpers/formatCombinacionesPase.ts"], "names": [], "mappings": ";;;AAGA,oCAA0C;AAC1C,uCAMmB;AAEnB,SAAS,oCAAoC,CAK3C,GAAQ;IACR,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;IAC/B,MAAM,SAAS,GAAQ,EAAE,CAAC;IAE1B,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,MAAM,KAAK,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QAC5E,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACtB;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,MAAM,uBAAuB,GAAG,KAAK,EAC1C,MAA2B,EAC3B,SAAoB,EACpB,UAAkB,EAClB,WAAmB,EACO,EAAE;;IAC5B,MAAM,OAAO,GAAG,MAAM,IAAA,sBAAc,GAAE,CAAC;IAEvC,MAAM,OAAO,GAAkC,EAAE,CAAC;IAElD,MAAM,oBAAoB,GACxB,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAE/C,MAAM,0BAA0B,GAC9B,IAAA,+BAAqB,EAAC,oBAAoB,CAAC,CAAC;IAE9C,KAAK,MAAM,WAAW,IAAI,0BAA0B,EAAE;QACpD,MAAM,EACJ,IAAI,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,KAAK,EAAE,EACpD,cAAc,GACf,GAAG,WAAW,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACpB,OAAO,CAAC,MAAM,CAAC,GAAG;gBAChB,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE;gBAC/B,aAAa,EAAE,EAAE;aAClB,CAAC;SACH;QAED,MAAM,YAAY,GAAG,IAAA,yBAAe,EAClC,UAAU,EACV,cAAc,CAAC,gBAAgB,CAChC,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAA,8BAAoB,EAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACvE,MAAM,mBAAmB,GAAG,IAAA,gCAAsB,EAChD,cAAc,CAAC,gBAAgB,EAC/B,OAAO,CAC6B,CAAC;QACvC,MAAM,KAAK,GAAG,IAAA,0BAAgB,EAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAEhE,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;YACjC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,gBAAgB,EACd,cAAc,CAAC,gBAAoF;YACrG,yBAAyB,EAAE,MAAA,cAAc,CAAC,yBAAyB,mCAAI,EAAE;YACzE,KAAK;YACL,SAAS,EAAE,CAAC,GAAG,gBAAgB,EAAE,GAAG,mBAAmB,CAAC;SACzD,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAChC,CAAC,CAAC;AAvDW,QAAA,uBAAuB,2BAuDlC"}
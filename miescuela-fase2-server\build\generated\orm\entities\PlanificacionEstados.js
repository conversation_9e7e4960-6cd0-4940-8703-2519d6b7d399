"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanificacionEstados = void 0;
const typeorm_1 = require("typeorm");
let PlanificacionEstados = class PlanificacionEstados {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'integer', name: 'id_planificacion_estados' }),
    __metadata("design:type", Number)
], PlanificacionEstados.prototype, "idPlanificacionEstados", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { name: 'nombre', unique: true }),
    __metadata("design:type", String)
], PlanificacionEstados.prototype, "nombre", void 0);
PlanificacionEstados = __decorate([
    (0, typeorm_1.Index)('planificacion_estados_pkey', ['idPlanificacionEstados'], {
        unique: true,
    }),
    (0, typeorm_1.Entity)('planificacion_estados', { schema: 'public' })
], PlanificacionEstados);
exports.PlanificacionEstados = PlanificacionEstados;
//# sourceMappingURL=PlanificacionEstados.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HabilidadesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.HabilidadesDAO;
class HabilidadesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.habilidades', '/orientaciones/habilidades', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.HabilidadesEndpoint = HabilidadesEndpoint;
//# sourceMappingURL=HabilidadesEndpoint.js.map
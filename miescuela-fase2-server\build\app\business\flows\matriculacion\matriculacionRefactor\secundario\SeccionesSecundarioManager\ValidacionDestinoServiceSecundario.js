"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidacionDestinoServiceSecundario = void 0;
const SeccionesRepository_1 = require("../../repositories/SeccionesRepository");
const state_1 = require("../../../../../../../app/const/state");
const constants_1 = require("../../../../../../../generated/endpoints/matriculacionRefactor/inicial/utils/constants/constants");
class ValidacionDestinoServiceSecundario {
    constructor(seccionesRepository = new SeccionesRepository_1.SeccionesRepository()) {
        this.seccionesRepository = seccionesRepository;
    }
    async getSeccionesConDestinoCombinaciones(secciones, user, tx, context) {
        const seccionesActuales = this.getSeccionesActuales(secciones, user);
        if (!seccionesActuales.length)
            return [];
        const cicloSiguiente = await this.getCicloLectivoSiguiente(seccionesActuales, context, tx);
        if (!cicloSiguiente)
            return [];
        const seccionesDestino = this.getSeccionesDestino(secciones, cicloSiguiente.idCicloLectivo);
        const combinaciones = await this.getCombinacionesPorAnios(secciones, tx);
        return this.calcularSeccionesConDestino(seccionesActuales, seccionesDestino, combinaciones);
    }
    getSeccionesActuales(secciones, user) {
        const idCicloLectivoActual = user.groupSelected.localizacion.idCicloLectivo;
        return secciones.filter((s) => s.cicloLectivo.idCicloLectivo === idCicloLectivoActual);
    }
    async getCicloLectivoSiguiente(seccionesActuales, context, tx) {
        const anioActual = seccionesActuales[0].cicloLectivo.anio;
        const siguiente = await this.seccionesRepository.getCicloSiguiente(context, tx, anioActual);
        return (siguiente === null || siguiente === void 0 ? void 0 : siguiente[0]) || null;
    }
    getSeccionesDestino(secciones, idCicloLectivoSiguiente) {
        return secciones.filter((s) => s.cicloLectivo.idCicloLectivo === idCicloLectivoSiguiente);
    }
    async getCombinacionesPorAnios(secciones, tx) {
        const aniosIds = [...new Set(secciones.map((s) => s.anio.idAnio))];
        return this.seccionesRepository.getCombinacionesByAnios(tx, aniosIds);
    }
    calcularSeccionesConDestino(seccionesActuales, seccionesDestino, combinaciones) {
        return seccionesActuales
            .filter((seccion) => this.tieneDestinoValido(seccion, seccionesDestino, combinaciones))
            .map((s) => s.idSeccion);
    }
    tieneDestinoValido(seccion, seccionesDestino, combinaciones) {
        const combinacionesSeccion = combinaciones.filter((c) => c.anio.idAnio === seccion.anio.idAnio);
        const isSextoAnio = seccion.anio.idAnio === constants_1.ANIO_IDS_SECUNDARIO.SEXTO_ANIO;
        const aniosDestino = this.obtenerAniosDestino(combinacionesSeccion, isSextoAnio);
        return this.existeSeccionDestinoCompatible(seccion, seccionesDestino, aniosDestino);
    }
    obtenerAniosDestino(combinaciones, isSextoAnio) {
        return combinaciones
            .map((c) => {
            var _a, _b;
            return isSextoAnio &&
                this.isEstadoPaseSextoAnio(c.estadoPaseAnio.idEstadoPaseAnio)
                ? c.anio.idAnio
                : (_b = (_a = c.accionPase) === null || _a === void 0 ? void 0 : _a.anio) === null || _b === void 0 ? void 0 : _b.idAnio;
        })
            .filter((id) => Boolean(id));
    }
    isEstadoPaseSextoAnio(estadoPaseAnio) {
        return [
            state_1.ESTADOS_PASES.COMPLETA_ESTUDIOS,
            state_1.ESTADOS_PASES.NO_COMPLETO_PLAN_DE_ESTUDIO,
        ].includes(estadoPaseAnio);
    }
    existeSeccionDestinoCompatible(seccion, seccionesDestino, aniosDestino) {
        return seccionesDestino.some((dest) => aniosDestino.includes(dest.anio.idAnio) &&
            dest.nivel.idNivel === seccion.nivel.idNivel);
    }
}
exports.ValidacionDestinoServiceSecundario = ValidacionDestinoServiceSecundario;
//# sourceMappingURL=ValidacionDestinoServiceSecundario.js.map
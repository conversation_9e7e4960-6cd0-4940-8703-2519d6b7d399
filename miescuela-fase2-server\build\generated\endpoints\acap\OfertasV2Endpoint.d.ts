import { MiEscuelaEndpointV2 } from '../../../app/config/endpoint';
import { Oferta } from '../../orm/entities';
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
export declare class OfertasEndpointV2 extends MiEscuelaEndpointV2<Oferta> {
    constructor();
    getAllowGuest(): boolean;
    postSelectAction: (values: Array<Oferta>, session: ChinoSessionDTO, req: JRRequest) => Promise<Oferta[]>;
}

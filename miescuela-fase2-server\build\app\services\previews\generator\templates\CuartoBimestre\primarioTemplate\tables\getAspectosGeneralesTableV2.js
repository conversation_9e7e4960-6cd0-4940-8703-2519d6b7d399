"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAspectosGeneralesTableV2 = void 0;
const getApoyo = (apoyo, otrosApoyos) => {
    if (apoyo.length === 0)
        return '-';
    const all = apoyo.filter((ap) => ap !== 'OTROS').join(', ');
    const others = (apoyo === null || apoyo === void 0 ? void 0 : apoyo.length) === 1 && apoyo.includes('OTROS')
        ? `OTROS - ${otrosApoyos}`
        : apoyo.includes('OTROS')
            ? `, OTROS - ${otrosApoyos}`
            : '';
    return `${all} ${others}`;
};
const getAspectosGeneralesTableV2 = ({ ciclo, anio, tipoPeriodo, organizaParticipa = '', apoyo = [], otrosApoyos = '', comprometeReconoce = '', promocionAcompanada = '', }) => `
  <table style="margin-bottom: 29px" class="table-bimestres" cellspacing="0" cellpadding="0">
      <thead>
          <tr>
              <th class="cell bt-1 tac bl-1 br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold" style="width:680px" colspan="3">
                  ${ciclo} - ${anio}
              </th>
              <th class="cell bt-1 tac br-1 ff-calibri fs-11 va-middle ws-normal bb-1 bgc-gray bold" dir="ltr" style="width:443px" rowspan="2"
                  colspan="3">
                  ${tipoPeriodo}
              </th>
          </tr>
          <tr>
              <td class="cell va-middle bl-1 br-1 bold ff-arial fs-10 ws-normal tal bb-1 bgc-gray" colspan="3">
                  ASPECTOS GENERALES
              </td>
          </tr>
      </thead>
      <tbody>
          <tr>
              <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                  ¿Promocionó con acompañamiento?
              </td>
              <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
                  ${promocionAcompanada}
              </td>
          </tr>
          <tr>
              <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                  ¿Posee apoyos/acompañamiento?
              </td>
              <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
                  ${apoyo.length > 0 ? 'SI' : 'NO'}
              </td>
          </tr>
          <tr>
              <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                  ¿Cuáles?
              </td>
              <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                  ${getApoyo(apoyo, otrosApoyos)}
              </td>
          </tr>
          <tr>
              <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                  ¿Se organiza y participa en actividades propuestas a través de los diversos formatos ajustándose a la
                  pautas de trabajo ?
              </td>
              <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                  ${organizaParticipa}
              </td>
          </tr>
          <tr>
              <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                  ¿Se compromete con su aprendizaje reconociendo logros y dificultades ?
              </td>
              <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                  ${comprometeReconoce}
              </td>
          </tr>
      </tbody>
  </table>
`;
exports.getAspectosGeneralesTableV2 = getAspectosGeneralesTableV2;
//# sourceMappingURL=getAspectosGeneralesTableV2.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjetivosDeAprendizajeEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ObjetivosDeAprendizajeEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ObjetivosDeAprendizajeDAO.entity.toLowerCase(), '/planificacion/' + dao_1.ObjetivosDeAprendizajeDAO.entity.toLowerCase(), dao_1.ObjetivosDeAprendizajeDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ObjetivosDeAprendizajeEndpoint = ObjetivosDeAprendizajeEndpoint;
//# sourceMappingURL=ObjetivosDeAprendizajeEndpoint.js.map
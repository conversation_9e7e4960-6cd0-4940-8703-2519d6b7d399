"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccionesOrientacionesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const helpers_1 = require("../../../utils/helpers");
class AccionesOrientacionesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.accionesorientaciones', '/acap/accionesorientaciones', dao_1.AccionesOrientacionesDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.AccionesOrientacionesEndpoint = AccionesOrientacionesEndpoint;
//# sourceMappingURL=AccionesOrientaciones.js.map
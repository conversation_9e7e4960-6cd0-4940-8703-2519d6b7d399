"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQuehaceresTableV2 = void 0;
const getQuehaceresTableV2 = ({ ciclo, anio, tipoPeriodo, organizaTareas = '', expresaIdeas = '', asumeResponsabilidad = '', realizaTareas = '', manifiestaInteres = '', logrosDificultades = '', }) => {
    return organizaTareas
        ? `<table style="width: 100%; margin-bottom: 29px;" class="table-bimestres" cellspacing="0" cellpadding="0">
    <thead>
        <tr>
            <th class="cell bt-1 tac bl-1 br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold" style="width:680px" colspan="3">
                   ${ciclo} - ${anio}
            </th>
            <th class="cell bt-1 tac br-1 ff-calibri fs-11 va-middle ws-normal bb-1 bgc-gray bold" dir="ltr" style="width:443px" rowspan="2"
                colspan="3">
                 ${tipoPeriodo}
            </th>
        </tr>
        <tr>
            <td class="cell va-middle bl-1 br-1 bold ff-arial fs-10 ws-normal tal bb-1 bgc-gray" colspan="3">
                QUEHACERES DEL/A ESTUDIANTE
            </td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                Se organiza para realizar sus tareas en clase.
            </td>
            <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
                ${organizaTareas}
            </td>
        </tr>
        <tr>
            <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                Expresa sus ideas en clase.
            </td>
            <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
                ${expresaIdeas}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                Asume con responsabilidad su participación en tareas de equipo
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${asumeResponsabilidad}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                Realiza tareas por sí mismo/a.
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${realizaTareas}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                Manifiesta interés por aprender y pregunta cuando no comprende
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${manifiestaInteres}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                Identifica lo aprendido: sus logros y dificultades.
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${logrosDificultades}
            </td>
        </tr>
    </tbody>
</table>
`
        : ``;
};
exports.getQuehaceresTableV2 = getQuehaceresTableV2;
//# sourceMappingURL=getQuehaceresTableV2.js.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const tables_1 = require("./tables");
const getAreaConocimientoTablesTB_1 = __importDefault(require("./tables/getAreaConocimientoTablesTB"));
exports.default = ({ aspectosGenerales, localizacion, distritoEscolar, cicloLectivo, tipoPeriodo, anioLectivo, apellido, nombre, anio, division, primario, observacionesc, }) => `
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    
    <style>
  
      h1 {
          font-size: 30px;
      }

      h2 {
          font-weight: 100;
      }
      hr{
          border: none;
          height: 3px; 
          background-color: #000;
      }
      .container {
          text-align: center;
      }
      .title{
          color: #61615a;
          padding-top: 35px;
      }
      .margin-title{
          padding-top: 30px;
      }
      .ciclo-title{
          padding-top: 80px;
          padding-bottom: 45px;
      }
      .alumno-container{
          padding-top: 55px;
          padding-bottom: 30px;
      }
      .grado-division-container{
          display: grid;
          grid-template-columns: repeat(2, 50%);
          padding-top: 80px
      }
      .grado-division-center h1{
          display:inline
      }
      .page-container{
          margin: auto;
          line-height: 17px;
          font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
      }
      .page-container-tables{
          margin: auto;
          line-height: 17px;
          padding-left: 15px;
          font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
          padding-left: 15px;
      }
      .page-header{
        padding: 10px;
        border-top: 10px solid #fcda59;
      }
      .portada-footer{
          height: 35vh;
          border: 2px solid black;
          flex-direction: column;
          display: flex;
          margin-top: 20px;
      }
      .text-title{
          line-height: 34px;
          padding-top: 10px;
          width: 100%;
          background-color: #e2eedb;
          text-align: center;
          border-bottom: 2px solid black;
          font-weight: bold;
      }
      .table-title{
        margin-top:16px;
      }
      .table-title-container{
          line-height: 34px;
          padding-top: 10px;
          padding-bottom: 10px;
          text-align: center;
          font-weight: bold;
          border-top: 2px solid black;
          border-left: 2px solid black;
          border-right: 2px solid black;
          background-color: #e2eedb;
      }
      .practicas-container{
          padding-left: 10px;
          border-top: 1px solid black;
          border-left: 2px solid black;
          border-right: 2px solid black;
      }
      .text-subtitle{
          line-height: 34px;
          width: 100%;
          background-color: #e2eedb;
          text-align: center;
          border-bottom: 2px solid black;
          font-weight: bold;
      }
      .firma-grid{
          display: grid;
          grid-template-columns: repeat(2, 42%);
          align-items: flex-end;
          justify-content: center;
          height: 100%;
          text-align: center;
          padding-bottom: 40px;
          background-color: #e2eedb;
      }
      td, th{
          border: 1px solid black;
          border-left: 0px;
          padding-left: 20px;
          padding-top: 10px;
          padding-bottom: 10px;
          width: 50%;
          font-size: 9px;
          word-wrap: break-word;
      }
      .tabla{
          caption-side: bottom;
          margin-left: auto;
          margin-right: auto;
          border-collapse: collapse;
          min-height: 24%;
          width: 100%;
          flex-flow: column;
          align-items: center;
          border: 2px solid black;
          border-right: 2px solid black;
          border-bottom: 2px solid black;
          border-top: 0px;
          table-layout: fixed;
      }
      @page {
        size: A4;
        margin: 0;
      }
      @media print {
        .page-container
        {
          page-break-after: always;
        }
        .page-container-tables {
          page-break-inside: avoid;
        }
        .portada-footer {
          page-break-inside: avoid;
        }
      }
      </style>
  </head>
  
  <body>
      <div class="page-container">
      <div class="page-header">
          <div class="container">
              <img src="https://buenosaires.gob.ar/sites/default/files/2023-02/ESCUDO%20BUENOS%20AIRES.jpg" width="50px" height="55px">
              <div class="title">
                <h2>Gobierno de la Ciudad de Buenos Aires</h2>
                <h2>Ministerio de Educación</h2>
                <h2 style="padding-top: 35px;">${localizacion} D.E. ${distritoEscolar}</h2>
              </div>
              <div class="margin-title">
                <h1>Educación Primaria</h1>
                <h2>Documento de Evaluación y Calificación</h2>
              </div>
          <div class="ciclo-title">
              <h1>${cicloLectivo}</h1>
              <h2>${tipoPeriodo} - ${anioLectivo}</h2>
          </div>
          
          <div class="alumno-container">
              <h1>${apellido} ${nombre}</h1>
          </div>
          <div class="grado-division-container">
              <div class="grado-division-center">
                  <h1>Grado: </h1> ${anio}
              </div>
              <div class="grado-division-center">
                  <h1>División: </h1> ${division}
              </div>
          </div>
          </div>
    </div>
    </div>
    <div class="page-container-tables">
    ${(() => {
    let tables = '';
    if (aspectosGenerales) {
        tables += (0, tables_1.getAspectosGeneralesTableTB)(aspectosGenerales);
    }
    return tables;
})()}
    </div>
    <div class="page-container-tables">
        ${(() => {
    let tables = '';
    if (primario) {
        tables += (0, getAreaConocimientoTablesTB_1.default)(primario);
    }
    return tables;
})()}
    </div>
    <div class="page-container-tables">
    ${(() => {
    let tables = '';
    if (observacionesc) {
        tables += (0, tables_1.getObservacionesTableTB)(observacionesc);
    }
    return tables;
})()}
      <div class="portada-footer">
        <div class="text-title">
          <div>
            ${division}
          </div>
          <div>
            FIRMAS
          </div>
        </div>
        <div class="text-subtitle">
          <div>
            ${tipoPeriodo}
          </div>          
        </div>
        <div class="firma-grid">
          <div>
            FIRMA MAESTRO/A
          </div>
          <div>
            FIRMA DIRECTOR/A
          </div>
        </div>
      </div>
      </div>
</body>
</html>
`;
//# sourceMappingURL=boletinPrimariaTB.js.map
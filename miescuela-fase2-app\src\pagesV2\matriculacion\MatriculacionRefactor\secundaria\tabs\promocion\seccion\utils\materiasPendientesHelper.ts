import { type EstadoPaseAnio, type CombinacionesPase, type DependenciaFuncional, type Anio } from '@miesc/lib/database/models'
import { ESTADO_PASE_ANIO } from '@miesc/const/estadosPase'
import { aplicarReglasSecundariaPorDependencia } from './reglasSecundariaHelper'
import { type ReglasSecundariaParams } from '../../types/types'
import { aplicaReglasSecundaria } from '@miesc/utils/nivelEstablecimiento'

export interface MateriasPendientesInfo {
  pendientesCicloAnteriores: number
  pendientesCicloActual: number
}

export interface FiltroMateriasPendientesParams {
  materiasPendientes: MateriasPendientesInfo
  esSegundoPaso: boolean
  tipoEscuelaSecundaria?: string
  estadosPaseAnio: EstadoPaseAnio[]
  combinacionesPase: CombinacionesPase[]
  dependenciaFuncional?: DependenciaFuncional
  anio?: Anio
  estadoPaseAnioAnterior?: EstadoPaseAnio | null
  rolId?: number
}

export const getEstadoPaseAnioPorDefectoConMateriasPendientes = (
  params: FiltroMateriasPendientesParams
): EstadoPaseAnio | null => {
  const { materiasPendientes, esSegundoPaso, estadosPaseAnio, dependenciaFuncional, anio, estadoPaseAnioAnterior, rolId } = params

  if (aplicaReglasSecundaria(dependenciaFuncional)) {
    const reglasParams: ReglasSecundariaParams = {
      materiasPendientes,
      dependenciaFuncional,
      anio,
      esSegundoPaso,
      estadosPaseAnio,
      combinacionesPase: params.combinacionesPase,
      estadoPaseAnioAnterior,
      rolId
    }

    const resultado = aplicarReglasSecundariaPorDependencia(reglasParams)
    return resultado.estadoPorDefecto
  }

  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  const promocionDirecta = estadosPaseAnio.find(
    estado => estado.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCION_DIRECTA
  )
  const promociona = estadosPaseAnio.find(
    estado => estado.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCIONA
  )
  const permanece = estadosPaseAnio.find(
    estado => estado.idEstadoPaseAnio === ESTADO_PASE_ANIO.PERMANECE
  )
  const egresa = estadosPaseAnio.find(
    estado => estado.idEstadoPaseAnio === ESTADO_PASE_ANIO.EGRESA
  )

  if (pendientesCicloAnteriores > 0) {
    return permanece || promociona || egresa || null
  }

  if (pendientesCicloActual > 0) {
    if (esSegundoPaso) {
      return promociona || permanece || egresa || null
    }
    return promocionDirecta || promociona || permanece || egresa || null
  }

  if (esSegundoPaso) {
    return promociona || egresa || permanece || null
  }

  return promocionDirecta || promociona || egresa || permanece || null
}

export const filtrarOpcionesEstadoPaseAnio = (
  params: FiltroMateriasPendientesParams
): EstadoPaseAnio[] => {
  const { materiasPendientes, esSegundoPaso, estadosPaseAnio, dependenciaFuncional, anio, estadoPaseAnioAnterior, rolId } = params

  if (aplicaReglasSecundaria(dependenciaFuncional)) {
    const reglasParams: ReglasSecundariaParams = {
      materiasPendientes,
      dependenciaFuncional,
      anio,
      esSegundoPaso,
      estadosPaseAnio,
      combinacionesPase: params.combinacionesPase,
      estadoPaseAnioAnterior,
      rolId
    }

    const resultado = aplicarReglasSecundariaPorDependencia(reglasParams)
    return resultado.estadosDisponibles
  }

  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  if (pendientesCicloAnteriores > 3) {
    return estadosPaseAnio.filter(estado =>
      estado.idEstadoPaseAnio === ESTADO_PASE_ANIO.PERMANECE
    )
  }

  if (pendientesCicloAnteriores > 0) {
    return estadosPaseAnio.filter(estado =>
      estado.idEstadoPaseAnio !== ESTADO_PASE_ANIO.PROMOCION_DIRECTA
    )
  }

  if (pendientesCicloActual > 4) {
    return estadosPaseAnio.filter(estado =>
      [ESTADO_PASE_ANIO.PERMANECE, ESTADO_PASE_ANIO.PROMOCIONA].includes(estado.idEstadoPaseAnio)
    )
  }

  if (pendientesCicloActual > 0 && esSegundoPaso) {
    return estadosPaseAnio.filter(estado =>
      estado.idEstadoPaseAnio !== ESTADO_PASE_ANIO.PROMOCION_DIRECTA
    )
  }

  return estadosPaseAnio
}

export const puedePromocionDirecta = (materiasPendientes: MateriasPendientesInfo): boolean => {
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  if (pendientesCicloAnteriores > 0) {
    return false
  }

  return pendientesCicloActual <= 2
}

export const getMensajeRestriccionesMateriasPendientes = (
  materiasPendientes: MateriasPendientesInfo
): string | null => {
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  if (pendientesCicloAnteriores > 3) {
    return `El alumno tiene ${pendientesCicloAnteriores} materias pendientes de ciclos anteriores. Debe permanecer para regularizar.`
  }

  if (pendientesCicloAnteriores > 0) {
    return `El alumno tiene ${pendientesCicloAnteriores} materias pendientes de ciclos anteriores. No puede acceder a promoción directa.`
  }

  if (pendientesCicloActual > 4) {
    return `El alumno tiene ${pendientesCicloActual} materias pendientes del ciclo actual. Opciones limitadas.`
  }

  if (pendientesCicloActual > 2) {
    return `El alumno tiene ${pendientesCicloActual} materias pendientes del ciclo actual. Revisar posibilidad de recuperación.`
  }

  return null
}

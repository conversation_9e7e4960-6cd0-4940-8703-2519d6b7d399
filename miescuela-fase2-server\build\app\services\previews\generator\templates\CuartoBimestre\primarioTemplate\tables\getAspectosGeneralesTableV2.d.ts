type AspectosGeneralesTableV2 = {
    acompaniamientoPromocion?: string;
    ciclo: string;
    anio: string;
    tipoPeriodo: string;
    organizaParticipa?: string;
    espaciosConsolidando?: string;
    compromisoAprendizaje?: string;
    apoyoPregunta?: string;
    espaciosConsolidandoPregunta?: string;
    vinculoPedagogico?: string;
    apoyo?: string[];
    otrosApoyos?: string;
    promocionAcompanada?: string;
    efectivizado?: boolean;
    idTipoPeriodo?: number | null;
    acompaniamientoPregunta?: string;
    comprometeReconoce?: string;
};
export declare const getAspectosGeneralesTableV2: ({ ciclo, anio, tipoPeriodo, organizaParticipa, apoyo, otrosApoyos, comprometeReconoce, promocionAcompanada, }: AspectosGeneralesTableV2) => string;
export {};

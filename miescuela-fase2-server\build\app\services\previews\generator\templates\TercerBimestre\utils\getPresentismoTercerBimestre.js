"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPresentismoTercerBimestre = void 0;
const util_1 = require("../../../../util");
const getPresentismoTercerBimestre = (tipoPeriodo, presentismo, observacionBoletin) => {
    var _a, _b, _c, _d;
    return `
  <div class="page-container pageBrealAvoid">
  <table class="tabla">
    <tr class="title-table">
        <th style="background-color: #e2eedb; font-weight: bold; font-size: 12px;">
            Presentismo
        </th>
        <th style="background-color: #e2eedb; font-weight: bold; font-size: 12px; text-align: center; vertical-align: middle;">
            ${tipoPeriodo}
        </th>
    </tr>
    <tr>
        <div>
            <td style="  font-weight: bold;text-align: justify;">Ausentes</td>
            <td style="text-align: center;">${(0, util_1.decimalToFractions)(presentismo === null || presentismo === void 0 ? void 0 : presentismo.totalAusentes) || ''}</td>
        </div>
    </tr>
    <tr>
        <div>
        <td style=" font-weight: bold; padding: 0 40px;text-align: justify;">Justificados</td>
        <td style="text-align: center;">${(0, util_1.decimalToFractions)(presentismo === null || presentismo === void 0 ? void 0 : presentismo.justificados) || ''}</td>
        </div>
    </tr>
    <tr>
        <div>
            <td style=" font-weight: bold; padding: 0 40px; text-align: justify;">Injustificados</td>
        </div>
        <td style="text-align: center;">${(0, util_1.decimalToFractions)(presentismo === null || presentismo === void 0 ? void 0 : presentismo.injustificados) || ''}</td>
    </tr>
    <tr>
        <div>
            <td style="  font-weight: bold; text-align: justify;">Ingresos tardíos / Retiros Anticipados</td>
        </div>
        <td style="text-align: center;">${((_a = presentismo === null || presentismo === void 0 ? void 0 : presentismo.countIngresoTardio) !== null && _a !== void 0 ? _a : 0) +
        ((_b = presentismo === null || presentismo === void 0 ? void 0 : presentismo.countRetiroAnticipado) !== null && _b !== void 0 ? _b : 0) !==
        0
        ? (0, util_1.decimalToFractions)(((_c = presentismo === null || presentismo === void 0 ? void 0 : presentismo.countIngresoTardio) !== null && _c !== void 0 ? _c : 0) +
            ((_d = presentismo === null || presentismo === void 0 ? void 0 : presentismo.countRetiroAnticipado) !== null && _d !== void 0 ? _d : 0))
        : '-'}</td>
    </tr>
    <tr>
        <div>
            <td style="  font-weight: bold; text-align: justify;">Presentes</td>
        </div>
        <td style="text-align: center;">${(0, util_1.decimalToFractions)(presentismo === null || presentismo === void 0 ? void 0 : presentismo.totalPresentes) || ''}</td>
    </tr>
    <tr>
        <div>
            <td style="  font-weight: bold; text-align: justify;">Observaciones de Presentismo:</td>
        </div>
        <td style="text-align: center white-space: normal;">${(observacionBoletin === null || observacionBoletin === void 0 ? void 0 : observacionBoletin.observacion) || ''}</td>
    </tr>
</table>
</div>`;
};
exports.getPresentismoTercerBimestre = getPresentismoTercerBimestre;
//# sourceMappingURL=getPresentismoTercerBimestre.js.map
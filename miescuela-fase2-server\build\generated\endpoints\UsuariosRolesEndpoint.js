"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsuariosRolesEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const just_rpc_1 = require("@phinxlab/just-rpc");
const MiEscuelaEndpointCustom_1 = require("../../app/config/endpoint/MiEscuelaEndpointCustom");
const const_1 = require("../../app/const");
const dateUtils_1 = require("../../utils/dateUtils");
const defaulPermisosUi_1 = require("../../utils/defaulPermisosUi");
const especialesPermisosUi_1 = require("../../utils/especialesPermisosUi");
const NoSerializable_1 = require("../../utils/NoSerializable");
const dao_1 = require("../orm/dao");
const getLevel_1 = require("./../../app/business/bootstrap/helper/getLevel");
class UsuariosRolesEndpoint extends MiEscuelaEndpointCustom_1.MiEscuelaEndpointCustom {
    constructor(name = 'usuariosroles', path = '/public/usuariosroles') {
        super(name, path);
    }
    configure() {
        this.allowGuest = false;
        this.addMethod(just_rpc_1.JRMethod.POST);
    }
    async exec(req, tx) {
        try {
            const context = chino_sdk_1.ChinoContext.fromSession(just_rpc_1.JRProtocolManager.get().recoverSession(req));
            if (req.method === 'POST' && req.body.type === 'addUser') {
                console.log('------------------ addUser');
                return await this.addUser(req, tx, context);
            }
            else if (req.method === 'POST' && req.body.type === 'addRol') {
                console.log('------------------ addRol');
                return await this.switchInsertRol(req, tx, context);
            }
            else {
                throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('e', e);
            // TODO NOT CHANGE, LIBBY NOT RECONGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(e.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
    async selectPersona(tx, cuil, documento, lastName, context, idUsuario) {
        if (idUsuario) {
            return dao_1.PersonaDAO.query('empty')
                .setContext(context)
                .groupStart()
                .equals('id_cuentas', idUsuario)
                .or()
                .equals('cuil', cuil)
                .or()
                .equals('documento', documento)
                .groupEnd()
                .includes('apellido', lastName.toLowerCase())
                .run(tx);
        }
        return dao_1.PersonaDAO.query('empty')
            .setContext(context)
            .groupStart()
            .equals('cuil', cuil)
            .or()
            .equals('documento', documento)
            .groupEnd()
            .includes('apellido', lastName.toLowerCase())
            .run(tx);
    }
    async updatePersona(tx, idUsuario, email, cuil, documento, idPersona, context) {
        return dao_1.PersonaDAO.save({
            idPersona: idPersona.toString(),
            cuentas: { idUsuario: idUsuario },
            email,
            cuil,
            documento,
        }, tx, {
            isUpdate: true,
            context,
        });
    }
    async insertPersona(tx, name, lastName, documento, idUsuario, email, cuil, idTipoDocumento, context) {
        return dao_1.PersonaDAO.save({
            nombre: name,
            apellido: lastName,
            fechaNacimiento: new Date().toISOString(),
            tipoDocumento: { idTipoDocumento },
            documento,
            nacionalidad: { idPais: 64 },
            genero: { idGenero: -1 },
            paisNacimiento: { idPais: 64 },
            grupoSanguineo: { idGrupoSanguineo: 1 },
            cuentas: { idUsuario },
            email,
            cuil,
        }, // la entidad Persona tiene varias propiedades opcionales que no estan marcadas como tal en la clase
        tx, { isInsert: true, context });
    }
    async insertUsuarioRolEstablecimiento(tx, idUsuario, nivel, idRol, context, permissionMap = []) {
        return dao_1.UsuarioRolEstablecimientoV2DAO.save({
            usuario: { idUsuario },
            localizacion: { idLocalizacion: -1 },
            rolUsuario: { idRolUsuario: idRol },
            nivel: { idNivel: Number(nivel) },
            permissionMap: [...defaulPermisosUi_1.defaultPermissions, ...permissionMap],
        }, tx, {
            isInsert: true,
            context,
            aspect: 'minimal',
        });
    }
    async insertUsuarioRolDependenciaFuncional(tx, idRolEstablecimiento, idDependenciaFuncional, context) {
        return dao_1.UsuarioRolDependenciaFuncionalDAO.save({
            dependenciaFuncional: {
                idDependenciaFuncional: Number(idDependenciaFuncional),
            },
            rolUsuario: { idRolEstablecimiento },
        }, tx, {
            isInsert: true,
            context,
        });
    }
    async insertUsuarioRolTraverseAction(tx, idRolEstablecimiento, idTraverseAction, idDistritoEscolar, context) {
        return dao_1.UsuarioRolTraverseActionDAO.save({
            value: idDistritoEscolar,
            traverseAction: {
                traverseActionId: idTraverseAction,
            },
            usuarioRolEstablecimiento: {
                idRolEstablecimiento,
            },
        }, tx, { isInsert: true, context });
    }
    async insertUsuarioRolTraverseActionEspeciales(tx, idRolEstablecimiento, idTraverseAction, value, context) {
        return dao_1.UsuarioRolTraverseActionDAO.save({
            value,
            traverseAction: {
                traverseActionId: idTraverseAction,
            },
            usuarioRolEstablecimiento: {
                idRolEstablecimiento,
            },
        }, tx, { isInsert: true, context });
    }
    async selectLocalizacionesByNivel(tx, nivel, context) {
        const localizaciones = await dao_1.LocalizacionPlanEstudioNivelDAO.query('usuario-roles-endpoint')
            .equals('planEstudioNivel.modalidadNivel.id_nivel', nivel)
            .equals('localizacion.establecimiento.id_dependencia_funcional', 41)
            .setContext(context)
            .run(tx);
        return localizaciones.map((l) => l.localizacion.idLocalizacion);
    }
    async addUser(req, tx, context) {
        const user = req.body;
        const userRepeated = [];
        if (user) {
            const cuentaUser = await dao_1.CuentasDAO.query()
                .setContext(context)
                .equals('username', user.email)
                .run(tx);
            if (cuentaUser.length) {
                throw chino_sdk_1.ChinoError.CustomException('El usuario ya existe', chino_sdk_1.STATUS_CODE.FORBIDDEN);
            }
            const document = await dao_1.PersonaDAO.query('empty')
                .setContext(context)
                .groupStart()
                .equals('documento', user.documento)
                .or()
                .equals('cuil', user.cuil)
                .groupEnd()
                .limit(1)
                .run(tx);
            if (document.length) {
                throw chino_sdk_1.ChinoError.CustomException('El Documento/CUIL ya esta en uso', chino_sdk_1.STATUS_CODE.FORBIDDEN);
            }
            console.log('NO EXISTE CUENTA', user);
            // CREO CUENTA
            const passwordEncrypted = await chino_sdk_1.ChinoEncryptManager.encrypt(`${user.name}${user.lastName}`);
            const passwordNoSerializable = new NoSerializable_1.NoSerializable(passwordEncrypted);
            const cuenta = (await dao_1.CuentasDAO.save({
                username: user.email,
                nombreUsuario: user.name,
                apellidoUsuario: user.lastName,
                password: passwordNoSerializable.value,
                createdAt: dateUtils_1.dateUtils.toUTC(new Date()),
            }, // la entidad Cuentas tiene varias propiedades opcionales que no estan marcadas como tal
            tx, {
                isInsert: true,
                context,
            }));
            // BUSCO PERSONA
            const persona = await this.selectPersona(tx, user.cuil, user.documento, user.lastName, context);
            if (persona.length) {
                if (persona.length === 1) {
                    // EXISTE UNA PERSONA, HAGO UN UPDATE CON LA CUENTA CREADA
                    console.log('EXISTE PERSONA -2 ', persona);
                    const personaUpdated = await this.updatePersona(tx, Number(cuenta.idUsuario), user.email, user.cuil, user.documento, persona[0].id_persona, context);
                    console.log('PERSONA UPDATED', personaUpdated);
                }
                else if (persona.length > 1) {
                    console.log('SE ENCONTRARON MAS DE UNA PERSONA', persona);
                    userRepeated.push({
                        id_persona: persona.map((p) => p.id_persona),
                        email: user.email,
                    });
                    throw chino_sdk_1.ChinoError.CustomException(`Persona duplicada ${userRepeated}`, chino_sdk_1.STATUS_CODE.FORBIDDEN);
                }
            }
            else {
                console.log('NO EXISTE PERSONA, CREO PERSONA CON CUENTA CREADA -> ', cuenta);
                // NO EXISTE PERSONA, CREO PERSONA Y LE ASIGNO EL ID DE LA CUENTA NUEVA
                const personaInserted = await this.insertPersona(tx, user.name, user.lastName, user.documento, Number(cuenta.idUsuario), user.email, user.cuil, Number(user.typeDocument), context);
                console.log('PERSONA CREADA', personaInserted);
            }
            return {
                status: 200,
                message: `El usuario fue guardado con éxito`,
            };
        }
    }
    async insertRolEquipoACAP(tx, idUsuario, req, context) {
        const res = await dao_1.UsuarioRolEstablecimientoV2DAO.save({
            usuario: { idUsuario: Number(idUsuario) },
            rolUsuario: { idRolUsuario: const_1.ROLES.ID_ROL_EQUIPO_ACAP },
            localizacion: { idLocalizacion: -1 },
            nivel: { idNivel: -1 },
            permissionMap: defaulPermisosUi_1.defaultPermissions,
        }, tx, {
            isInsert: true,
            context,
            aspect: 'minimal',
        });
        return {
            status: 200,
            message: 'Éxito!',
            el: res,
        };
    }
    async switchInsertRol(req, tx, context) {
        const { insertType, idUsuario, user } = req.body;
        if (insertType) {
            try {
                switch (insertType) {
                    case 'equipoACAP':
                        console.log('insertRolEquipoACAP');
                        return await this.insertRolEquipoACAP(tx, idUsuario, req, context);
                    case 'supTecEspNorm':
                        console.log('insertRolTecEspNorm');
                        return await this.insertRolTecEspNorm(tx, idUsuario, user.nivel, user.DF, insertType, context, user.localizaciones);
                    case 'supMed':
                        console.log('insertSupMed');
                        // Supervisor de escuela media con lozacalizaciones por region
                        return await this.insertRolTecEspNorm(tx, idUsuario, user.nivel, user.DF, insertType, context, user.localizaciones);
                    case 'direccionArea':
                        console.log('insertRolDirecArea');
                        return await this.insertRolDirecArea(tx, idUsuario, user.DF, context);
                    case 'direccionDeACAP':
                        console.log('insertRolDirecArea');
                        return await this.insertRolDireccionAcap(tx, idUsuario, user.DF, context);
                    case 'supArt':
                    case 'supInicMedPri':
                        console.log('insertRol para supInicMedPri');
                        return await this.insertRol(tx, idUsuario, user.DE, user.DF, user.nivel, context, insertType);
                    case 'supPriv':
                        console.log('insertRol supervisor privado');
                        return await this.insertRol(tx, idUsuario, [], user.DF, user.nivel, context, insertType);
                    case 'serviciosAEscuelas':
                        console.log('insertRolServicioAEscuelas');
                        return await this.insertRolServicioAEscuelas(tx, idUsuario, context);
                    case 'rolLocalizacion': {
                        return await this.insertRolPorLocalizacion(tx, idUsuario, user, context);
                    }
                    case 'inclusionEscolar':
                        console.log('insertRolInclusionEscolar');
                        return await this.insertRolInclusionEscolar(tx, idUsuario, context);
                    case 'impersonador':
                        return await this.insertImpersonador(tx, idUsuario, context);
                }
            }
            catch (error) {
                console.log(error);
                throw new Error((error === null || error === void 0 ? void 0 : error.message) || 'Ocurrió un error al guardar el nuevo usuario');
            }
        }
    }
    async insertRol(tx, idUsuario, idDistritoEscolar, idDependenciaFuncional, nivel, context, insertType) {
        try {
            const usuarioRolEstDepFun = await dao_1.UsuarioRolDependenciaFuncionalDAO.query('only_dependency')
                .equals('rolUsuario.id_usuario', idUsuario)
                .equals('rolUsuario.id_nivel', nivel)
                .equals('dependenciaFuncional.idDependenciaFuncional', idDependenciaFuncional)
                .equals('rolUsuario.id_rol_usuario', 11)
                .setContext(context)
                .run(tx);
            if (!usuarioRolEstDepFun.length) {
                console.log('CREO USUARIO ROL ESTABLECIMIENTO SUPERVISOR.');
                const userROL = await this.insertUsuarioRolEstablecimiento(tx, idUsuario, nivel, 11, context);
                console.log('INSERTED USUARIO ROL ESTABLECIMIENTO', userROL);
                if (insertType !== 'supArt' && insertType !== 'supPriv') {
                    console.log('INSERT USUARIO ROL TRAVERSE ACTION');
                    const usuarioRolTraverseAction = await this.insertUsuarioRolTraverseAction(tx, userROL.idRolEstablecimiento, 10, idDistritoEscolar, context);
                    console.log('USUARIO ROL TRAVERSE ACTION INSERTED: ', usuarioRolTraverseAction);
                }
                console.log('INSERT USUARIO ROL DEPENDENCIA FUNCIONAL');
                const usuarioRolDependenciaFuncional = await this.insertUsuarioRolDependenciaFuncional(tx, userROL.idRolEstablecimiento, idDependenciaFuncional, context);
                console.log('USUARIO ROL DEPENDENCIA FUNCIONAL INSERTED: ', usuarioRolDependenciaFuncional);
                return {
                    status: 200,
                    message: `El rol fue asignado con éxito insertRol`,
                };
            }
            else {
                throw new Error(`Ya existe el rol para esta dependencia funcional`);
            }
        }
        catch (error) {
            console.log(error);
            throw new Error('Ocurrió un error al asignar el rol');
        }
    }
    async insertRolTecEspNorm(tx, idUsuario, nivel, idDependenciaFuncional, rolNomenclatura, context, localizaciones) {
        try {
            const usuarioRolEstDepFun = await dao_1.UsuarioRolDependenciaFuncionalDAO.query('only_dependency')
                .equals('rolUsuario.id_usuario', idUsuario)
                .equals('rolUsuario.id_nivel', nivel)
                .equals('dependenciaFuncional.idDependenciaFuncional', idDependenciaFuncional)
                .equals('rolUsuario.id_rol_usuario', 11)
                .setContext(context)
                .run(tx);
            if (!usuarioRolEstDepFun.length) {
                console.log('CREO USUARIO ROL ESTABLECIMIENTO SUPERVISOR.');
                const permissions = Number(idDependenciaFuncional) === const_1.DEPENDENCIA_FUNCIONAL_IDS.ESPECIAL
                    ? especialesPermisosUi_1.escEspDirAreaYSupervPermissions
                    : undefined;
                const userROL = await this.insertUsuarioRolEstablecimiento(tx, idUsuario, nivel, 11, context, permissions);
                console.log('INSERTED USUARIO ROL ESTABLECIMIENTO', userROL);
                if (localizaciones) {
                    const usuarioRolTraverseAction = await this.insertUsuarioRolTraverseAction(tx, userROL.idRolEstablecimiento, 1, localizaciones, context);
                    console.log('INSERT USUARIO ROL TRAVERSE ACTION', usuarioRolTraverseAction);
                }
                else {
                    const localizacionesByNivel = await this.selectLocalizacionesByNivel(tx, nivel, context);
                    const usuarioRolTraverseAction = await this.insertUsuarioRolTraverseAction(tx, userROL.idRolEstablecimiento, 1, localizacionesByNivel, context);
                    console.log('INSERT USUARIO ROL TRAVERSE ACTION', usuarioRolTraverseAction);
                }
                console.log('INSERT USUARIO ROL DEPENDENCIA FUNCIONAL');
                const usuarioRolDependenciaFuncional = await this.insertUsuarioRolDependenciaFuncional(tx, userROL.idRolEstablecimiento, idDependenciaFuncional, context);
                console.log('USUARIO ROL DEPENDENCIA FUNCIONAL INSERTED: ', usuarioRolDependenciaFuncional);
                if (Number(idDependenciaFuncional) === const_1.DEPENDENCIA_FUNCIONAL_IDS.ESPECIAL) {
                    let localizacion = undefined;
                    let nivelesEspeciales = [];
                    if (localizaciones) {
                        localizacion = await dao_1.LocalizacionDAO.findByPK(localizaciones[0], { context }, tx);
                    }
                    if (localizacion) {
                        const escDescripcion = localizacion.establecimiento.escalafon.descripcion.toUpperCase();
                        // No se tiene en cuenta el escalafón A, ya que no es tenido en cuenta en la primera etapa de Escuelas Especiales API
                        const escB = escDescripcion === 'B';
                        const escC = escDescripcion === 'C';
                        if (escB) {
                            nivelesEspeciales = await dao_1.NivelDAO.query('default')
                                .setContext(context)
                                .in('descripcionNivel', ['Primario'])
                                .run(tx);
                        }
                        else if (escC) {
                            nivelesEspeciales = await dao_1.NivelDAO.query('default')
                                .setContext(context)
                                .in('descripcionNivel', [
                                'Inicial',
                                'Primario',
                                'Atención Temprana',
                                'Formación Integral',
                            ])
                                .run(tx);
                        }
                    }
                    else {
                        nivelesEspeciales = await dao_1.NivelDAO.query('default')
                            .setContext(context)
                            .in('descripcionNivel', [
                            'Inicial',
                            'Primario',
                            'Atención Temprana',
                            'Formación Integral',
                        ])
                            .run(tx);
                    }
                    if (nivelesEspeciales.length === 0) {
                        console.log('No se encontraron niveles para escuelas especiales. Validar localización encontrada por escalafón');
                        throw chino_sdk_1.ChinoCustomError.CustomException('No se encontraron niveles para escuelas especiales.', 500);
                    }
                    const nivelEspecialesId = nivelesEspeciales.map((n) => n.idNivel.toString());
                    const usuarioRolTraverseAction = await this.insertUsuarioRolTraverseActionEspeciales(tx, userROL.idRolEstablecimiento, 2, nivelEspecialesId, context);
                    console.log('USUARIO ROL TRAVERSE ACTION ESPECIALES INSERTED: ', usuarioRolTraverseAction);
                }
                return {
                    status: 200,
                    message: `El rol fue asignado con éxito: ${JSON.stringify(rolNomenclatura)}`,
                };
            }
            else {
                return {
                    status: 406,
                    message: 'Ya existe un rol de supervisor para esta dependencia funcional',
                };
            }
        }
        catch (error) {
            console.log(error);
            throw new Error('Ocurrió un error al asignar el rol');
        }
    }
    async insertRolDirecArea(tx, idUsuario, DF, context) {
        try {
            for (const df of DF) {
                const usuarioRolEstDepFun = await dao_1.UsuarioRolDependenciaFuncionalDAO.query('only_dependency')
                    .equals('rolUsuario.id_usuario', idUsuario)
                    .equals('rolUsuario.id_nivel', df[1])
                    .equals('dependenciaFuncional.idDependenciaFuncional', df[0])
                    .equals('rolUsuario.id_rol_usuario', 10)
                    .setContext(context)
                    .run(tx);
                if (!usuarioRolEstDepFun.length) {
                    console.log('CREO USUARIO ROL ESTABLECIMIENTO PARA DIRECCION DE AREA.');
                    const permissions = Number(df[0]) === const_1.DEPENDENCIA_FUNCIONAL_IDS.ESPECIAL
                        ? especialesPermisosUi_1.escEspDirAreaYSupervPermissions
                        : undefined;
                    const userROL = await this.insertUsuarioRolEstablecimiento(tx, idUsuario, df[1], 10, context, permissions);
                    console.log('INSERTED USUARIO ROL ESTABLECIMIENTO', userROL);
                    console.log('INSERT USUARIO ROL DEPENDENCIA FUNCIONAL');
                    const usuarioRolDependenciaFuncional = await this.insertUsuarioRolDependenciaFuncional(tx, userROL.idRolEstablecimiento, df[0], context);
                    console.log('USUARIO ROL DEPENDENCIA FUNCIONAL INSERTED: ', usuarioRolDependenciaFuncional);
                    if (df[0] === const_1.DEPENDENCIA_FUNCIONAL_IDS.ESPECIAL) {
                        const nivelesEspeciales = await dao_1.NivelDAO.query('default')
                            .setContext(context)
                            .in('descripcionNivel', [
                            'Inicial',
                            'Primario',
                            'Atención Temprana',
                            'Formación Integral',
                        ])
                            .run(tx);
                        const nivelEspecialesId = nivelesEspeciales.map((n) => n.idNivel.toString());
                        const usuarioRolTraverseAction = await this.insertUsuarioRolTraverseActionEspeciales(tx, userROL.idRolEstablecimiento, 2, nivelEspecialesId, context);
                        console.log('USUARIO ROL TRAVERSE ACTION ESPECIALES INSERTED: ', usuarioRolTraverseAction);
                    }
                    return {
                        status: 200,
                        message: `El rol fue asignado con éxito insertRolDirecArea`,
                    };
                }
                else {
                    throw new Error(`Ya existe el rol solicitado`);
                }
            }
        }
        catch (error) {
            console.log(error);
            throw new Error('Ocurrió un error al asignar el rol');
        }
    }
    async insertRolDireccionAcap(tx, idUsuario, DF, context) {
        try {
            const existeRol = DF.map(async (df) => {
                const rolExistente = await dao_1.UsuarioRolDependenciaFuncionalDAO.query('only_dependency')
                    .equals('rolUsuario.id_usuario', idUsuario)
                    .equals('rolUsuario.id_nivel', df[1])
                    .equals('dependenciaFuncional.idDependenciaFuncional', df[0])
                    .equals('rolUsuario.id_rol_usuario', 15)
                    .setContext(context)
                    .run(tx);
                return rolExistente.length > 0;
            });
            if (await Promise.all(existeRol)) {
                const userROL = await this.insertUsuarioRolEstablecimiento(tx, idUsuario, '3', 15, context, ['uiv1_headertable_titleMainButton', 'uiv1_table_crudseccion']);
                console.log('INSERTED USUARIO ROL ESTABLECIMIENTO', userROL);
                console.log('INSERT USUARIO ROL DEPENDENCIA FUNCIONAL');
                const usuarioRolDependenciaFuncional = await this.insertUsuarioRolDependenciaFuncional(tx, userROL.idRolEstablecimiento, DF[0][0].toString(), context);
                console.log('USUARIO ROL DEPENDENCIA FUNCIONAL INSERTED: ', usuarioRolDependenciaFuncional);
                return {
                    status: 200,
                    message: `El rol fue asignado con éxito el insert de Dirrecion ACAP`,
                };
            }
            else {
                throw new Error('El rol ya está asignado a este usuario');
            }
        }
        catch (error) {
            throw new Error('Ocurrió un error al asignar el rol');
        }
    }
    async insertRolServicioAEscuelas(tx, idUsuario, context) {
        try {
            const rolExistente = await dao_1.UsuarioRolEstablecimientoV2DAO.query('true_minimal')
                .equals('id_usuario', idUsuario)
                .equals('id_rol_usuario', 40)
                .setContext(context)
                .run(tx);
            if (rolExistente.length > 0) {
                return {
                    status: 406,
                    message: 'El rol Servicio a Escuelas ya está asignado a este usuario',
                };
            }
            // Inserto rol para cards
            await this.insertUsuarioRolEstablecimiento(tx, idUsuario, getLevel_1.NIVEL_ID.INICIAL.toString(), //Por defecto, luego se irá actualizando
            40, context);
            // Inserto rol para módulo Reportes
            const rolEstablecimientosExistente = await dao_1.UsuarioRolEstablecimientoV2DAO.query('minimal')
                .equals('id_usuario', idUsuario)
                .equals('rolUsuario.id_rol_usuario', 55)
                .setContext(context)
                .run(tx);
            if (rolEstablecimientosExistente.length === 0) {
                await this.insertUsuarioRolEstablecimiento(tx, idUsuario, '-1', 55, context);
                return {
                    status: 200,
                    message: `El rol de Servicios a Escuela fue asignado con éxito`,
                };
            }
        }
        catch (error) {
            throw new Error(error.message ||
                'Ocurrió un error al asignar el rol Servicio a escuelas');
        }
    }
    async insertRolPorLocalizacion(tx, idUsuario, user, context) {
        var _a, _b;
        try {
            const idRolUsuario = Number(user === null || user === void 0 ? void 0 : user.idRolUsuario);
            const df = Number(user === null || user === void 0 ? void 0 : user.DF);
            const locId = Number((_a = user === null || user === void 0 ? void 0 : user.localizaciones) === null || _a === void 0 ? void 0 : _a[0]);
            const nivel = (_b = user === null || user === void 0 ? void 0 : user.nivel) !== null && _b !== void 0 ? _b : -1;
            if (!Number.isFinite(idRolUsuario) ||
                !Number.isFinite(df) ||
                !Number.isFinite(locId)) {
                throw chino_sdk_1.ChinoError.CustomException('Faltan datos para rolLocalizacion', chino_sdk_1.STATUS_CODE.BAD_REQUEST);
            }
            const existente = await dao_1.UsuarioRolEstablecimientoV2DAO.query('minimal')
                .equals('id_usuario', idUsuario)
                .equals('rolUsuario.id_rol_usuario', idRolUsuario)
                .equals('localizacion.id_localizacion', locId)
                .setContext(context)
                .run(tx);
            if (existente === null || existente === void 0 ? void 0 : existente.length) {
                return {
                    status: 406,
                    message: 'Ya existe este rol en la misma localización para el usuario',
                };
            }
            // 1) Insert UsuarioRolEstablecimiento
            const userROL = (await dao_1.UsuarioRolEstablecimientoV2DAO.save({
                usuario: { idUsuario: Number(idUsuario) },
                rolUsuario: { idRolUsuario },
                localizacion: { idLocalizacion: locId },
                nivel: { idNivel: Number(nivel) },
                permissionMap: defaulPermisosUi_1.defaultPermissions,
            }, tx, { isInsert: true, context, aspect: 'minimal' }));
            // 2) Vincular Dependencia Funcional (si aplica)
            if (Number.isFinite(df) && df > 0) {
                await this.insertUsuarioRolDependenciaFuncional(tx, userROL.idRolEstablecimiento, String(df), context);
            }
            return {
                status: 200,
                message: 'El rol por localización fue asignado con éxito',
            };
        }
        catch (error) {
            console.log(error);
            throw new Error((error === null || error === void 0 ? void 0 : error.message) || 'Ocurrió un error al asignar el rol por localización');
        }
    }
    async insertRolInclusionEscolar(tx, idUsuario, context) {
        const rolExistente = await dao_1.UsuarioRolEstablecimientoV2DAO.query('minimal')
            .equals('id_usuario', idUsuario)
            .equals('rolUsuario.idRolUsuario', 54)
            .setContext(context)
            .run(tx);
        if (rolExistente.length > 0) {
            throw new Error('El rol ya está asignado a este usuario');
        }
        await this.insertUsuarioRolEstablecimiento(tx, idUsuario, '-1', 54, context);
        return {
            status: 200,
            message: `El rol fue Inclusión Escolar asignado con éxito`,
        };
    }
    async insertImpersonador(tx, idUsuario, context) {
        const rolExistente = await dao_1.UsuarioRolEstablecimientoV2DAO.query('minimal')
            .equals('id_usuario', idUsuario)
            .equals('rolUsuario.idRolUsuario', const_1.ROLES.ID_ROL_IMPERSONADOR)
            .setContext(context)
            .run(tx);
        if (rolExistente.length > 0) {
            throw new Error('El rol ya está asignado a este usuario');
        }
        await this.insertUsuarioRolEstablecimiento(tx, idUsuario, '-1', const_1.ROLES.ID_ROL_IMPERSONADOR, context);
        return {
            status: 200,
            message: `El rol Impersonador fue asignado con éxito`,
        };
    }
}
exports.UsuariosRolesEndpoint = UsuariosRolesEndpoint;
//# sourceMappingURL=UsuariosRolesEndpoint.js.map
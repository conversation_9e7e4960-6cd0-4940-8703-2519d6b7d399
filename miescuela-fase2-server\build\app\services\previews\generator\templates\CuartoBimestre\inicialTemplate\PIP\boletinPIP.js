"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tables_1 = require("./tables");
exports.default = ({ nombre, apellido, distritoEscolar, localizacion, data, }) => /*html*/ `
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    
    <style>

    h1 {
        font-size: 30px;
    }

    h2 {
        font-weight: 100;
    }
    hr{
        border: none;
        height: 3px; 
        background-color: #000;
    }
    .root {
      margin-left: 20px;
    }
    .container {
        text-align: center;
    }
    .title{
        color: #61615a;
        line-height: 5px;
    }
    .margin-title{
        padding-top: 20px;
    }
    .localizacion-container{
        line-height: 20px;
        padding-top: 10px;
        color: #61615a;
    }
    .alumno-container{
        padding-top: 10px
    }
    .page-container{
        margin: auto;
        line-height: 17px;
        font-family: 'Gotham Rounded', sans-serif;
    }
    .table-container{
        margin: auto;
        line-height: 20px;
        font-family: 'Gotham Rounded', sans-serif;
    }
    .page-header{
      padding: 10px;
      line-height : 20px;
    }
    td, th{
        border: 1px solid black;
        border-top: 0px;
        border-left: 0px;
        padding-left: 20px;
        padding-right: 20px;
        font-size: 9px;
        word-wrap: break-word;
    }
    .tabla{
        margin-top:16px;
        caption-side: bottom;
        margin-left: auto;
        margin-right: auto;
        border-collapse: collapse;
        min-height: 24%;
        width: 100%;
        flex-flow: column;
        align-items: center;
        border: 1px solid #000;
        table-layout: fixed;
    }
    .tabla-aprendizaje{
        margin-top:16px;
        caption-side: bottom;
        margin-left: auto;
        margin-right: auto;
        border-collapse: collapse;
        min-height: 24%;
        width: 100%;
        flex-flow: column;
        align-items: center;
        border: 1px solid #000;
        table-layout: fixed;
    }
    .aprendizaje-header{
        background-color: #e2eedb;
        font-weight: bold;
        font-size: 12px;
        white-space: nowrap;
        width: 32%;
    }
    .observaciones-header{
        text-align: center;
        background-color: #e2eedb;
        border-bottom: 1px solid #000;
        font-weight: bold;
        font-size: 12px; 
    }
    tbody :nth-child(even) td { background: #F1F1F1; }
    .sello-container{
        text-align: center;
        line-height: 40px;
        padding-top: 30px;
    }
    .border-bottom{
      border-bottom: 1px solid black;
    }
    .informe-row{
        text-align: center;
    }
    .portada-form-firma-responsable{
      text-align: center;
      width: 80%;
      margin-top: 40px;
      display: inline-flex;
    }
    .firma-responsable-border{
      min-height: '1px';
      width: 70%;
      padding-top: 40px;
      display: inline-block;
      flex-flow: row;
      align-items: center;
      border: 1px solid black;
      border-radius: 10px;
    }
    .firma-sello{
      margin-top: 62px;
      margin-bottom: auto;
      font-family: 'Gotham Rounded', sans-serif;
    }
    .marca-agua{
        position: fixed;
        top: 0%;
        left: 0%;
        margin: -1.5%;
        width: 100vw;
        height: 100vh;
        filter: opacity(0.0) brightness(1.1);
        pointer-events: none;
    }
    @media print {
      @page {
        size: A4;
    }
      .marca-agua{
        margin: 0px;
        position: fixed;
        filter: opacity(0.1);
    }
    .page-header {
              page-break-after: always;
              margin-top: 50px;
              margin-left: -30px;
            }

      .table-container table tr{
        page-break-inside: avoid;
      } 
      .sello-container{
          page-break-inside: avoid;
      }
      .cuerpo {
              page-break-inside: avoid;
              margin-left: -30px
            }
    }
    .marginPDF{
      margin-left: -30px;      
    }


    </style>
</head>

<body>
<div class="root">
  <div class="marca-agua">
    <img src="https://buenosaires.gob.ar/sites/default/files/2023-02/ESCUDO%20BUENOS%20AIRES.jpg" width="100%">
  </div>
    <div class="page-container">
    <div class="page-header">
        <div class="container">
            <img src="https://buenosaires.gob.ar/sites/default/files/2023-02/ESCUDO%20BUENOS%20AIRES.jpg" width="60px" height="70px" style="filter: brightness(1.1); mix-blend-mode: multiply;">
            <div class="title">
              <h2>Gobierno de la Ciudad de Buenos Aires</h2>
              <h2>Ministerio de Educación</h2>
            </div>
            <div class="localizacion-container">
                <h2>${localizacion}</h2>
                <h2>D.E. ${distritoEscolar}</h2>
            </div>
            <div class="margin-title">
              <h2><b>Puente Inicial Primaria</b></h2>
            </div>
        
        <div class="alumno-container">
            <h1>${apellido} ${nombre}</h1>
        </div>
        
        </div>
  </div>
  </div>
    
  <div class='cuerpo'>
  ${(() => {
    let tables = '';
    if (data) {
        tables += (0, tables_1.getActitudFrenteAprendizajeTablePIP)(data);
    }
    return tables;
})()}
  </div>

     <div class="sello-container">
       
        <div div class="portada-form-firma-responsable">
          <div class="firma-responsable-border">
              <div class="firma-container">
                  <p class="firma-sello">FIRMA Y SELLO DIRECTOR/A</p>
              </div>
          </div>
          <div class="firma-responsable-border" style="margin-left: 50px;">
            <div class="firma-container">
                <p class="firma-sello">FIRMA Y SELLO DOCENTE</p>
            </div>
        </div>
        </div>
        <h3 style="text-align: center; line-height: 25px; font-family: 'Gotham Rounded', sans-serif;"> Este es un documento oficial emitido por ${localizacion} a través del sistema miEscuela (RES N° 5788-GCABA-MEIGC/19) conforme el Reglamento Escolar de la Ciudad Autónoma de Buenos Aires.</h3>
     </div>
     
</div>
<div class='marginPDF' >

${data.adjuntoDibujo
    ? `<div style="display:none" class="drive-attachment" data-id="${data.adjuntoDibujo.url}" data-filename="${data.adjuntoDibujo.filename}"></div>`
    : ''}
${data.adjuntoInforme
    ? `<div style="display:none" class="drive-attachment" data-id="${data.adjuntoInforme.url}" data-filename="${data.adjuntoInforme.filename}"></div>`
    : ''}
</div>
</body>
</html>

  `;
//# sourceMappingURL=boletinPIP.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContenidosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.ContenidosDAO;
class ContenidosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.contenidos', '/orientaciones/contenidos', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ContenidosEndpoint = ContenidosEndpoint;
//# sourceMappingURL=ContenidosEndpoint.js.map
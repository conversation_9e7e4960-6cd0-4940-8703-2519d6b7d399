"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropositosDeEnsenanza = void 0;
const typeorm_1 = require("typeorm");
let PropositosDeEnsenanza = class PropositosDeEnsenanza {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'integer', name: 'id_proposito' }),
    __metadata("design:type", String)
], PropositosDeEnsenanza.prototype, "idProposito", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { name: 'descripcion' }),
    __metadata("design:type", String)
], PropositosDeEnsenanza.prototype, "descripcion", void 0);
__decorate([
    (0, typeorm_1.Column)('integer', { name: 'id_materia' }),
    __metadata("design:type", Number)
], PropositosDeEnsenanza.prototype, "idMateria", void 0);
PropositosDeEnsenanza = __decorate([
    (0, typeorm_1.Index)('propositos_de_ensenanza_pkey', ['idProposito'], { unique: true }),
    (0, typeorm_1.Entity)('propositos_de_ensenanza', { schema: 'public' })
], PropositosDeEnsenanza);
exports.PropositosDeEnsenanza = PropositosDeEnsenanza;
//# sourceMappingURL=PropositosDeEnsenanza.js.map
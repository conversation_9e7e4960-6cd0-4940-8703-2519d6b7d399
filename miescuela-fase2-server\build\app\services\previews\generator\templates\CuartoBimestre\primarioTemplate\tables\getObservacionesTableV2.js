"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getObservacionesTableV2 = void 0;
const getObservacionesTableV2 = ({ ciclo, anio, observaciones, tipoPeriodo, }) => `
${(() => {
    if ((observaciones === null || observaciones === void 0 ? void 0 : observaciones.length) > 2500) {
        return `<style>
        @media print {
          @page {
            size: A4;
            margin-top: 10mm;
          }
          .portada-header {
            page-break-after: always;
          }
        }
      </style>`;
    }
    else {
        return '';
    }
})()}
<table style="width: 100%; margin-bottom: 29px;" class="table-bimestres" cellspacing="0" cellpadding="0">
    <thead>
        <tr>
            <th class="cell bt-1 tac bl-1 br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold" style="width:680px" colspan="3">
                ${ciclo} - ${anio}
            </th>
            <th class="cell bt-1 tac br-1 ff-calibri fs-11 va-middle ws-normal bb-1 bgc-gray bold" dir="ltr" style="width:443px" rowspan="2"
                colspan="3">
                ${tipoPeriodo}
            </th>
        </tr>
        <tr>
            <td class="cell va-middle bl-1 br-1 bold ff-arial fs-10 ws-normal tal bb-1 bgc-gray" colspan="3">
                OBSERVACIONES
            </td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                Observaciones
            </td>
            <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal wb-all" dir="ltr" colspan="3">
                ${observaciones !== null && observaciones !== void 0 ? observaciones : '-'}
            </td>
        </tr>
    </tbody>
</table>
`;
exports.getObservacionesTableV2 = getObservacionesTableV2;
//# sourceMappingURL=getObservacionesTableV2.js.map
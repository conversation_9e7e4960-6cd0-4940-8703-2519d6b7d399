"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjetivosAdicionalesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ObjetivosAdicionalesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ObjetivosAdicionalesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.ObjetivosAdicionalesDAO.entity.toLowerCase(), dao_1.ObjetivosAdicionalesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ObjetivosAdicionalesEndpoint = ObjetivosAdicionalesEndpoint;
//# sourceMappingURL=ObjetivosAdicionalesEndpoint.js.map
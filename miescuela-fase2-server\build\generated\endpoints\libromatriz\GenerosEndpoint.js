"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneroLMEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class GeneroLMEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.GeneroDAO.entity.toLowerCase(), '/' + dao_1.GeneroDAO.entity.toLowerCase(), dao_1.GeneroDAO);
    }
    postSelectAction(values, session, req) {
        return Promise.resolve(values.map((genero) => ({
            id: genero.idGenero,
            descripcion: genero.descripcionGenero,
        })));
    }
    getAllowGuest() {
        return false;
    }
}
exports.GeneroLMEndpoint = GeneroLMEndpoint;
//# sourceMappingURL=GenerosEndpoint.js.map
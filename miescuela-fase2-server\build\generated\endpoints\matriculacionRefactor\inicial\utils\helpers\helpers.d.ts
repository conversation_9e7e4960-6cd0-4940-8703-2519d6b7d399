import { CombinacionesPase, EstadoProyeccionMotivo, Seccion } from '../../../../../orm/entities';
import { FormattedCombinacion } from '../../types';
export declare const getColorByEstado: (estadoId: number) => string;
export declare const getAniosDestino: (anioOrigen: number, estadoId: number) => number[];
export declare const getSeccionesParaAnio: (secciones: Seccion[], anios: number[]) => FormattedCombinacion['secciones'];
export declare const getSeccionesEspeciales: (estadoId: number, motivos: EstadoProyeccionMotivo[]) => {
    idSeccion: null;
    nombreSeccion: string;
    motivos: EstadoProyeccionMotivo[];
}[];
export declare const sortCombinacionesPase: (combinaciones: CombinacionesPase[]) => CombinacionesPase[];

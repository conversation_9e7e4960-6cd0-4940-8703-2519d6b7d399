"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProsesosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class ProsesosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ProcesosV2DAO.entity.toLowerCase(), '/public/' + dao_1.ProcesosV2DAO.entity.toLowerCase(), dao_1.ProcesosV2DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ProsesosEndpoint = ProsesosEndpoint;
//# sourceMappingURL=ProcesosEndpoint.js.map
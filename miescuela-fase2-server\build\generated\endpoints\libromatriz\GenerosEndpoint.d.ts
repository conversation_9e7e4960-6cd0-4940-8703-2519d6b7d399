import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { Genero } from '../../orm/entities';
export declare class GeneroLMEndpoint extends MiEscuelaEndpoint<Genero> {
    constructor();
    postSelectAction(values: Genero[], session: ChinoSessionDTO, req: JRRequest): Promise<any>;
    getAllowGuest(): boolean;
}

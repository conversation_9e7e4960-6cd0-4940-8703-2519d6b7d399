import { <PERSON>o<PERSON>rror, ChinoLogManager } from '@phinxlab/chino-sdk';
import { Flow } from '../Flow';
import { FlowOptions } from '../FlowOptions';
import {
  DependenciaFuncional,
  Proyeccion,
  Seccion,
} from '../../../../generated/orm/entities';
import {
  ProyeccionDAO,
  ProyeccionEstadoDAO,
  ProyeccionIELDAO,
  SeccionV2DAO,
  CicloLectivoDAO,
  LocalizacionDAO,
} from '../../../../generated/orm/dao';
import {
  HistoricoProyeccionEstadoService,
  CicloLectivoService,
  ProyeccionService,
  SeccionesService,
} from '../matriculacion/matriculacionRefactor/inicial/promocionEstadoInicialService/services';
import { VacantesInformadasValidator } from '../matriculacion/matriculacionRefactor/inicial/promocionEstadoInicialService/validations';
import { CicloLectivoHelper } from '../matriculacion/matriculacionRefactor/helpers/CicloLectivoHelper';
import { ProyeccionEstadoIELManager } from './services/ProyeccionEstadoIELManager';
import { ProyeccionEstadoTipoEnum } from '../../../../app/const/proyeccionEstados';
import { LEVEL_TYPES } from '../../../const/levels';
import { DEPENDENCIA_FUNCIONAL } from '../../../const/dependenciaFuncional';

const Log: ChinoLogManager = new ChinoLogManager(
  'PROYECCION_IEL_BULK_SAVE FLOW',
);

export class ProyeccionIELBulkSave extends Flow {
  private request: {
    nivel: number;
    localizacion: string;
    cicloLectivo: number;
  };
  private seccions: Seccion[];
  private localizacion: string;
  private nivel: number;
  private dependenciaFuncional: DependenciaFuncional;
  private cicloLectivo: number;
  private cantidadPorseccion: { [seccionOrigen: string]: number };

  constructor(options: FlowOptions) {
    super(options.tx, options.req);
    this.request = options.req.body.data;
    this.localizacion =
      this.request?.localizacion !== undefined
        ? this.request?.localizacion
        : this.user.groupSelected.localizacion.id;
    this.nivel =
      this.request?.nivel !== undefined
        ? this.request?.nivel
        : this.user.groupSelected.nivel.idNivel;
    this.cicloLectivo =
      this.request?.cicloLectivo !== undefined
        ? this.request?.cicloLectivo
        : this.user.groupSelected.localizacion.idCicloLectivo;
  }

  private get isInitialNoDen(): boolean {
    return (
      this.nivel === LEVEL_TYPES.INITIAL &&
      this.dependenciaFuncional.descripcion !==
        DEPENDENCIA_FUNCIONAL.ESCUELAS_NORMALES
    );
  }

  private async getCurrentCicloLectivoId(): Promise<number> {
    if (
      this.nivel !== LEVEL_TYPES.INITIAL &&
      this.nivel !== LEVEL_TYPES.SECONDARY
    ) {
      return this.cicloLectivo;
    }
    return await CicloLectivoHelper.getCurrentCicloLectivoId(
      this.nivel,
      this.cicloLectivo,
      this.entityManager,
    );
  }

  private async getNextCicloLectivoId(): Promise<number> {
    try {
      if (
        this.nivel === LEVEL_TYPES.PRIMARY ||
        this.nivel === LEVEL_TYPES.SECONDARY ||
        (this.nivel === LEVEL_TYPES.INITIAL &&
          this.dependenciaFuncional.descripcion ===
            DEPENDENCIA_FUNCIONAL.ESCUELAS_NORMALES)
      ) {
        const currentCiclo = await CicloLectivoDAO.query('empty')
          .equals('idCicloLectivo', this.cicloLectivo)
          .run(this.entityManager);

        if (currentCiclo.length === 0) {
          throw ChinoError.CustomException(
            `No se encontró el ciclo lectivo actual con ID ${this.cicloLectivo}`,
            404,
          );
        }

        const nextYear = currentCiclo[0].anio + 1;
        const nextCiclos = await CicloLectivoDAO.query('empty')
          .equals('anio', nextYear)
          .run(this.entityManager);

        if (nextCiclos.length === 0) {
          throw ChinoError.CustomException(
            `No se encontró el ciclo lectivo para el año ${nextYear}`,
            404,
          );
        }

        return nextCiclos[0].idCicloLectivo;
      } else {
        return await CicloLectivoHelper.getNextCicloLectivoId(
          this.nivel,
          this.cicloLectivo,
          this.entityManager,
        );
      }
    } catch (error) {
      Log.error(`Error in getNextCicloLectivoId: ${error.message}`);
      throw error;
    }
  }

  async run(): Promise<any> {
    Log.info('INIT PROYECCION_IEL_BULK_SAVE FLOW ##########');

    const localizacion = await LocalizacionDAO.findByPK(
      this.localizacion,
      {
        aspect: 'proyeccion-iel',
        context: this.context,
      },
      this.entityManager,
    );
    this.dependenciaFuncional =
      localizacion.establecimiento.dependenciaFuncional;

    if (this.isInitialNoDen) {
      const validator = new VacantesInformadasValidator(
        new ChinoLogManager('VacantesInformadasValidator'),
        new HistoricoProyeccionEstadoService(),
        new CicloLectivoService(),
        new ProyeccionService(),
        new SeccionesService(),
      );

      await validator.validate(this.entityManager, this.context, {
        idLocalizacion: this.localizacion,
        idCicloLectivo: this.cicloLectivo,
        idNivel: this.nivel,
        userId: this.context.session.user.id,
      });
    } else if (this.nivel === LEVEL_TYPES.SECONDARY) {
      // Deprecar con el refactor para secundaria.
      // Validar contra el nuevo flujo
      await this.checkProyeccion();
    }

    await this.getSeccions();
    if (this.seccions.length === 0)
      throw ChinoError.CustomException('no se encontraron secciones', 400);

    await this.matriculados();
    await this.bulkSaveProyeccionIEL();

    await this.managePromocionEstadoPrimario();

    return {
      ok: this.isInitialNoDen
        ? 'Vacantes informadas correctamente para nivel INICIAL.'
        : 'Se informo correctamente.',
    };
  }

  private async checkProyeccion(): Promise<void> {
    try {
      const query = ProyeccionEstadoDAO.query('default')
        .setContext(this.context)
        .equals('localizacion', this.localizacion)
        .equals('nivel', this.nivel);

      if (this.isInitialNoDen) {
        query.equals('cicloLectivo.anio', this.cicloLectivo);
      } else {
        query.equals('cicloLectivo', this.cicloLectivo);
      }

      const result = await query.run(this.entityManager);
      if (!result || result.length === 0) {
        throw ChinoError.CustomException(
          'No se encontró estado de proyección para los parámetros dados',
          400,
        );
      }
    } catch (error) {
      Log.error(`Error in checkProyeccion: ${error.message}`);
      throw ChinoError.CustomException(
        `Error en checkProyeccion: ${error.message}`,
        400,
      );
    }
  }

  async getSeccions() {
    try {
      const nextCicloLectivoId = await this.getNextCicloLectivoId();
      this.seccions = await SeccionV2DAO.query('informe_iel')
        .setContext(this.context)
        .equals('id_ciclo_lectivo', nextCicloLectivoId)
        .equals('id_localizacion', this.localizacion)
        .equals('id_nivel', this.nivel)
        .run(this.entityManager);
    } catch (error) {
      Log.error(`Error in getSeccions: ${error.message}`);
      throw ChinoError.CustomException(
        `Error al obtener secciones: ${error.message}`,
        400,
      );
    }
  }

  async matriculados() {
    try {
      const seccionesIds = this.seccions.map((sec) => sec.idSeccion);
      const currentCicloLectivoId = await this.getCurrentCicloLectivoId();
      const result = await ProyeccionDAO.query('default')
        .setContext(this.context)
        .in('id_seccion_destino', seccionesIds)
        .equals('cicloLectivo', currentCicloLectivoId)
        .run(this.entityManager);

      const resultadoPorSeccion: {
        [seccionDestino: string]: Proyeccion[];
      } = {};
      result.forEach((resultado) => {
        const seccionDestino = resultado.seccionDestino!.idSeccion;

        if (!resultadoPorSeccion[seccionDestino]) {
          resultadoPorSeccion[seccionDestino] = [];
        }

        resultadoPorSeccion[seccionDestino].push(resultado);
      });

      const cantidadPorseccion: { [seccionDestino: string]: number } = {};

      for (const seccion in resultadoPorSeccion) {
        cantidadPorseccion[seccion] = resultadoPorSeccion[seccion].length;
      }

      this.cantidadPorseccion = cantidadPorseccion;
    } catch (error) {
      Log.error(`Error in matriculados: ${error.message}`);
      throw ChinoError.CustomException(
        `Error al obtener matriculados: ${error.message}`,
        400,
      );
    }
  }

  async bulkSaveProyeccionIEL() {
    try {
      const objIel = [];
      for (const iel of this.seccions) {
        const idSeccion = iel.idSeccion;

        const createObjIel = async (matriculado: number) => ({
          jornada: iel.jornada,
          matriculado,
          capacidadMaxima: iel.capacidadMaxima,
          acompaniante: 0,
          cue: Number(iel.localizacion.establecimiento?.cue),
          cueAnexo: Number(iel.localizacion.cueAnexo),
          anio: {
            idAnio: Number(iel.anio.idAnio),
          },
          turno: {
            idTurno: Number(iel.turno.idTurno),
          },
          distrito: {
            idDistritoEscolar: Number(
              iel.localizacion.establecimiento.distritoEscolar
                ?.idDistritoEscolar,
            ),
          },
          dependenciaFuncional: {
            idDependenciaFuncional: Number(
              iel.localizacion.establecimiento.dependenciaFuncional
                .idDependenciaFuncional,
            ),
          },
          cicloLectivo: {
            idCicloLectivo: await this.getCurrentCicloLectivoId(),
          },
          establecimiento: {
            idEstablecimiento: Number(
              iel.localizacion.establecimiento.idEstablecimiento,
            ),
          },
        });

        let matriculados = 0;
        if (idSeccion in this.cantidadPorseccion) {
          matriculados = this.cantidadPorseccion[idSeccion];
          objIel.push(await createObjIel(matriculados));
        } else {
          objIel.push(await createObjIel(0));
        }
      }

      for (const iel of objIel) {
        await ProyeccionIELDAO.save(iel as any, this.entityManager, {
          isInsert: true,
          context: this.context,
        });
      }

      return objIel;
    } catch (error) {
      Log.error(`Error in bulkSaveProyeccionIEL: ${error.message}`);
      throw ChinoError.CustomException(
        `Error al guardar proyección IEL: ${error.message}`,
        400,
      );
    }
  }

  async managePromocionEstadoPrimario(): Promise<void> {
    const promocionEstadoManager = new ProyeccionEstadoIELManager(
      this.context,
      this.entityManager,
    );

    try {
      await promocionEstadoManager.gestionarEstado();
    } catch (error) {
      throw ChinoError.CustomException(
        `Error al gestionar el estado de la proyección primaria: ${error.message}`,
        400,
      );
    }
  }
}

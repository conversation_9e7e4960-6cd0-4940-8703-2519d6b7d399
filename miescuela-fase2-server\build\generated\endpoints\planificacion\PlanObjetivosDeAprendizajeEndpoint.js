"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanObjetivosDeAprendizajeEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PlanObjetivosDeAprendizajeEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PlanObjetivosDeAprendizajeDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PlanObjetivosDeAprendizajeDAO.entity.toLowerCase(), dao_1.PlanObjetivosDeAprendizajeDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PlanObjetivosDeAprendizajeEndpoint = PlanObjetivosDeAprendizajeEndpoint;
//# sourceMappingURL=PlanObjetivosDeAprendizajeEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccionesMinimizadasEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const lodash_1 = require("lodash");
class AccionesMinimizadasEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.accionesMinimizadas', '/acap/acciones_minimizadas', dao_1.AccionesMinimizadasDAO);
    }
    async postSelectAction(values, session, req) {
        const accionOrientacionesFilter = {};
        Object.entries(req.query).forEach(([key, item]) => {
            if (key.includes('accionOrientaciones')) {
                const newKey = key
                    .split('.')
                    .filter((valorKey) => {
                    return valorKey !== 'accionOrientaciones';
                })
                    .join('.');
                accionOrientacionesFilter[newKey] = item.equals || '';
            }
        });
        if (Object.entries(accionOrientacionesFilter).length > 0) {
            const cloneValue = (0, lodash_1.cloneDeep)(values);
            const accionFiltrada = cloneValue.filter((value) => {
                return value.accionOrientaciones.some((element) => {
                    let checker = true;
                    Object.entries(accionOrientacionesFilter).forEach(([key, id]) => {
                        const valor = (0, lodash_1.get)(element, key);
                        if (typeof valor === 'string' || typeof valor === 'number') {
                            checker = valor.toString() === id.toString();
                        }
                    });
                    return checker;
                });
            });
            return accionFiltrada;
        }
        return values;
    }
    getAllowGuest() {
        return false;
    }
}
exports.AccionesMinimizadasEndpoint = AccionesMinimizadasEndpoint;
//# sourceMappingURL=AccionesMinimizadasEndpoint.js.map
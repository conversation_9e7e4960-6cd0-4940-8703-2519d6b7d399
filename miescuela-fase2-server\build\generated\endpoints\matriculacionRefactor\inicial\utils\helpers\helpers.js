"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sortCombinacionesPase = exports.getSeccionesEspeciales = exports.getSeccionesParaAnio = exports.getAniosDestino = exports.getColorByEstado = void 0;
const combinacionesPaseInicial_1 = require("../constants/combinacionesPaseInicial");
const constants_1 = require("../constants/constants");
const getColorByEstado = (estadoId) => {
    switch (estadoId) {
        case constants_1.ESTADO_PASE_ANIO_IDS.PERMANECE:
            return '#f44336'; // Rojo
        case constants_1.ESTADO_PASE_ANIO_IDS.PROMOCIONA:
            return '#4caf50'; // Verde
        case constants_1.ESTADO_PASE_ANIO_IDS.EGRESA:
            return '#00bcd4'; // Celeste
        default:
            return '#9e9e9e'; // Gris
    }
};
exports.getColorByEstado = getColorByEstado;
const getAniosDestino = (anioOrigen, estadoId) => {
    const combinacion = combinacionesPaseInicial_1.COMBINACIONES_PASE_INICIAL.find((c) => c.desde === anioOrigen && c.estado === estadoId);
    if (!combinacion)
        return [anioOrigen];
    return combinacion.estado === constants_1.ESTADO_PASE_ANIO_IDS.EGRESA
        ? []
        : combinacion.hacia;
};
exports.getAniosDestino = getAniosDestino;
const getSeccionesParaAnio = (secciones, anios) => secciones
    .filter((s) => anios.includes(s.anio.idAnio))
    .map((s) => {
    var _a;
    return ({
        idSeccion: s.idSeccion,
        nombreSeccion: (_a = s.nombreSeccion) !== null && _a !== void 0 ? _a : '',
        motivos: [],
        turno: s.turno
            ? {
                idTurno: s.turno.idTurno,
            }
            : undefined,
        idAnio: s.anio.idAnio,
    });
});
exports.getSeccionesParaAnio = getSeccionesParaAnio;
const getSeccionesEspeciales = (estadoId, motivos) => estadoId === constants_1.ESTADO_PASE_ANIO_IDS.EGRESA
    ? [
        {
            idSeccion: null,
            nombreSeccion: '-',
            motivos: [],
        },
    ]
    : [
        {
            idSeccion: null,
            nombreSeccion: 'No Concurrirá',
            motivos,
        },
        {
            idSeccion: null,
            nombreSeccion: 'Articula',
            motivos: [],
        },
    ];
exports.getSeccionesEspeciales = getSeccionesEspeciales;
const combinacionesBusinessOrderMap = {
    [constants_1.ESTADO_PASE_ANIO_IDS.PERMANECE]: 1,
    [constants_1.ESTADO_PASE_ANIO_IDS.PROMOCIONA]: 2,
    [constants_1.ESTADO_PASE_ANIO_IDS.EGRESA]: 3,
};
const sortCombinacionesPase = (combinaciones) => {
    return combinaciones.sort((a, b) => {
        const orderA = combinacionesBusinessOrderMap[a.estadoPaseAnio.idEstadoPaseAnio];
        const orderB = combinacionesBusinessOrderMap[b.estadoPaseAnio.idEstadoPaseAnio];
        return orderA - orderB;
    });
};
exports.sortCombinacionesPase = sortCombinacionesPase;
//# sourceMappingURL=helpers.js.map
import { type EstadoPaseAnio, type DependenciaFuncional, type Anio } from '@miesc/lib/database/models'
import { ESTADO_PASE_ANIO } from '@miesc/const/estadosPase'
import { ROL } from '@miesc/const'
import { getTipoDependenciaFuncional, aplicaReglasSecundaria } from '@miesc/utils/nivelEstablecimiento'

import { type MateriasPendientesInfo } from './materiasPendientesHelper'
import {
  type ReglasSecundariaParams,
  type OpcionesEstadoPase,
  type EstadosAniosIntermedios,
  type ContextoResolucion
} from '../../types/types'

const filterEstados = (estados: Array<EstadoPaseAnio | undefined>): EstadoPaseAnio[] => {
  return estados.filter((estado): estado is EstadoPaseAnio => Boolean(estado))
}
const esAnioEgreso = (anio: Anio | undefined, tipoDependencia: string): boolean => {
  if (!anio) return false

  const descripcionAnio = anio.descripcionAnio?.toLowerCase()

  if (tipoDependencia === 'DET') {
    return descripcionAnio === '6to año' || descripcionAnio === '6°'
  }

  return descripcionAnio === '5to año' || descripcionAnio === '5°'
}

const getNumeroAnio = (anio: Anio | undefined): number => {
  if (!anio) return 0
  return anio.numeroAnio ?? 0
}

const aplicarReglasPrimerAnio = (params: ReglasSecundariaParams): OpcionesEstadoPase => {
  const { materiasPendientes, esSegundoPaso, estadosPaseAnio, rolId } = params
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  const esSupervisor = rolId === ROL.SUPERVISOR
  const totalPendientes = pendientesCicloAnteriores + pendientesCicloActual

  const promocionDirecta = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCION_DIRECTA)
  const promocionAcompanada = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.CON_PROMOCION_ACOMPANADA)
  const permanece = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PERMANECE)

  if (totalPendientes <= 2) {
    return {
      estadoPorDefecto: promocionDirecta || null,
      estadosDisponibles: filterEstados([promocionDirecta, promocionAcompanada])
    }
  }

  if (!esSegundoPaso) {
    return {
      estadoPorDefecto: promocionAcompanada || null,
      estadosDisponibles: filterEstados([promocionAcompanada])
    }
  }

  const estadosDisponibles = esSupervisor
    ? filterEstados([promocionAcompanada, permanece])
    : filterEstados([promocionAcompanada])

  return {
    estadoPorDefecto: promocionAcompanada || null,
    estadosDisponibles
  }
}

const esAnioIntermedio = (tipoDependencia: string, numeroAnio: number): boolean => {
  if (tipoDependencia === 'DET') {
    return numeroAnio >= 2 && numeroAnio <= 5
  }
  return ['DEA', 'DENs', 'DEM', 'DEP'].includes(tipoDependencia) && numeroAnio >= 2 && numeroAnio <= 4
}

const obtenerEstadosAniosIntermedios = (estadosPaseAnio: EstadoPaseAnio[]): EstadosAniosIntermedios => {
  return {
    promocionDirecta: estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCION_DIRECTA),
    promocionAcompanada: estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.CON_PROMOCION_ACOMPANADA),
    enProceso: estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.EN_PROCESO),
    permanece: estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PERMANECE)
  }
}

const resolverPendientesHasta2 = (contexto: ContextoResolucion): OpcionesEstadoPase => {
  const { esSegundoPaso, esDireccionArea, vinoDeEnProceso, vinoDePromocionAcompanada, estados } = contexto
  const { promocionDirecta, promocionAcompanada, permanece } = estados

  if (!esSegundoPaso) {
    return {
      estadoPorDefecto: promocionDirecta || null,
      estadosDisponibles: filterEstados([promocionDirecta])
    }
  }

  if (vinoDeEnProceso || vinoDePromocionAcompanada) {
    const estadosBase = filterEstados([promocionAcompanada])
    const estadosConDireccion = esDireccionArea
      ? filterEstados([promocionAcompanada, permanece])
      : estadosBase

    const estadoPorDefecto = vinoDeEnProceso ? promocionAcompanada : promocionAcompanada

    return {
      estadoPorDefecto: estadoPorDefecto || null,
      estadosDisponibles: estadosConDireccion
    }
  }

  return {
    estadoPorDefecto: promocionDirecta || null,
    estadosDisponibles: filterEstados([promocionDirecta, promocionAcompanada])
  }
}

const resolverPendientesHasta4 = (contexto: ContextoResolucion): OpcionesEstadoPase => {
  const { esSegundoPaso, vinoDeEnProceso, estados } = contexto
  const { promocionAcompanada, permanece } = estados

  if (!esSegundoPaso) {
    return {
      estadoPorDefecto: promocionAcompanada || null,
      estadosDisponibles: filterEstados([promocionAcompanada])
    }
  }

  const estadoPorDefecto = vinoDeEnProceso ? promocionAcompanada : promocionAcompanada

  return {
    estadoPorDefecto: estadoPorDefecto || null,
    estadosDisponibles: filterEstados([promocionAcompanada, permanece])
  }
}

const resolverPendientesMasDe4 = (contexto: ContextoResolucion): OpcionesEstadoPase => {
  const { esSegundoPaso, esDireccionArea, estados } = contexto
  const { promocionAcompanada, enProceso, permanece } = estados

  if (!esSegundoPaso) {
    return {
      estadoPorDefecto: enProceso || null,
      estadosDisponibles: filterEstados([enProceso])
    }
  }

  const estadosBase = filterEstados([permanece])
  const estadosConDireccion = esDireccionArea
    ? filterEstados([permanece, promocionAcompanada])
    : estadosBase

  return {
    estadoPorDefecto: permanece || null,
    estadosDisponibles: estadosConDireccion
  }
}

const aplicarReglasAniosIntermedios = (params: ReglasSecundariaParams): OpcionesEstadoPase => {
  const { materiasPendientes, esSegundoPaso, estadosPaseAnio, estadoPaseAnioAnterior, dependenciaFuncional, anio, rolId } = params
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  const tipoDependencia = getTipoDependenciaFuncional(dependenciaFuncional)
  const numeroAnio = getNumeroAnio(anio)

  if (!esAnioIntermedio(tipoDependencia, numeroAnio)) {
    return aplicarReglasGenerales(params)
  }

  const totalPendientes = pendientesCicloAnteriores + pendientesCicloActual

  const contexto: ContextoResolucion = {
    esSegundoPaso,
    esDireccionArea: rolId === ROL.DIRECCION_AREA,
    vinoDeEnProceso: estadoPaseAnioAnterior?.idEstadoPaseAnio === ESTADO_PASE_ANIO.EN_PROCESO,
    vinoDePromocionAcompanada: estadoPaseAnioAnterior?.idEstadoPaseAnio === ESTADO_PASE_ANIO.CON_PROMOCION_ACOMPANADA,
    estados: obtenerEstadosAniosIntermedios(estadosPaseAnio)
  }

  if (totalPendientes <= 2) {
    return resolverPendientesHasta2(contexto)
  }

  if (totalPendientes <= 4) {
    return resolverPendientesHasta4(contexto)
  }

  return resolverPendientesMasDe4(contexto)
}

const aplicarReglasAnioEgreso = (params: ReglasSecundariaParams): OpcionesEstadoPase => {
  const { materiasPendientes, estadosPaseAnio } = params
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  const totalPendientes = pendientesCicloAnteriores + pendientesCicloActual

  const completoPlan = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.COMPLETA_ESTUDIOS)
  const noCompletoPlan = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.NO_COMPLETO_PLAN_ESTUDIOS)

  if (totalPendientes === 0) {
    return {
      estadoPorDefecto: completoPlan || null,
      estadosDisponibles: filterEstados([completoPlan])
    }
  }

  return {
    estadoPorDefecto: noCompletoPlan || null,
    estadosDisponibles: filterEstados([noCompletoPlan])
  }
}

const aplicarReglasGenerales = (params: ReglasSecundariaParams): OpcionesEstadoPase => {
  const { materiasPendientes, esSegundoPaso, estadosPaseAnio } = params
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  const promocionDirecta = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCION_DIRECTA)
  const promociona = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PROMOCIONA)
  const permanece = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.PERMANECE)
  const egresa = estadosPaseAnio.find(e => e.idEstadoPaseAnio === ESTADO_PASE_ANIO.EGRESA)

  if (pendientesCicloAnteriores > 3) {
    return {
      estadosDisponibles: filterEstados([permanece]),
      estadoPorDefecto: permanece || null
    }
  }

  if (pendientesCicloAnteriores > 0) {
    return {
      estadosDisponibles: filterEstados([promociona, permanece, egresa]),
      estadoPorDefecto: permanece || promociona || null
    }
  }

  if (pendientesCicloActual > 4) {
    return {
      estadosDisponibles: filterEstados([promociona, permanece]),
      estadoPorDefecto: promociona || null
    }
  }

  if (pendientesCicloActual > 2 && esSegundoPaso) {
    return {
      estadosDisponibles: filterEstados([promociona, permanece, egresa]),
      estadoPorDefecto: promociona || null
    }
  }

  const estadosDisponibles = esSegundoPaso
    ? filterEstados([promociona, permanece, egresa])
    : filterEstados([promocionDirecta, promociona, permanece, egresa])

  return {
    estadosDisponibles,
    estadoPorDefecto: promocionDirecta || promociona || egresa || permanece || null
  }
}

export const aplicarReglasSecundariaPorDependencia = (params: ReglasSecundariaParams): OpcionesEstadoPase => {
  const { dependenciaFuncional, anio } = params

  if (!aplicaReglasSecundaria(dependenciaFuncional)) {
    return aplicarReglasGenerales(params)
  }

  const tipoDependencia = getTipoDependenciaFuncional(dependenciaFuncional)
  const numeroAnio = getNumeroAnio(anio)

  if (numeroAnio === 1) {
    return aplicarReglasPrimerAnio(params)
  } else if (esAnioEgreso(anio, tipoDependencia)) {
    return aplicarReglasAnioEgreso(params)
  } else {
    return aplicarReglasAniosIntermedios(params)
  }
}

export const getMensajeReglasSecundaria = (
  materiasPendientes: MateriasPendientesInfo,
  dependenciaFuncional: DependenciaFuncional | undefined,
  anio: Anio | undefined
): string | null => {
  const { pendientesCicloAnteriores, pendientesCicloActual } = materiasPendientes

  if (!aplicaReglasSecundaria(dependenciaFuncional)) {
    return null
  }

  const tipoDependencia = getTipoDependenciaFuncional(dependenciaFuncional)
  const numeroAnio = getNumeroAnio(anio)

  if (pendientesCicloAnteriores > 0) {
    return `Alumno con ${pendientesCicloAnteriores} materias de ciclos anteriores. Aplicando reglas ${tipoDependencia} para ${numeroAnio}° año.`
  }

  if (pendientesCicloActual > 0) {
    return `Alumno con ${pendientesCicloActual} materias pendientes del ciclo actual. Aplicando reglas ${tipoDependencia} para ${numeroAnio}° año.`
  }

  return null
}

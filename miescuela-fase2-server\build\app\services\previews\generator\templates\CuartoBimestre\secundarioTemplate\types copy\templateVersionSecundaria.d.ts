import { ObservacionesBoletin } from 'generated/orm/entities';
import { EspacioCurricularesCB } from '../tables/getEspaciosCurricularesTableCB';
import getJornadaExtendidaTable from '../tables/getJornadaExtendidaTable';
import { PendienteCB } from '../tables/getPendientesPorCambioPlanTableCB';
import { PreviaCB } from '../tables/getPendientesPreviasTableCB';
import { FortalecimientoCB } from '../tables/getPlanFortalecimientoTableCB';
import { PresentismoTotal } from 'app/business/flows/reports/types';
export type Params = {
    nombre: string;
    apellido: string;
    documento: string;
    distritoEscolar: string;
    localizacion: string;
    anioLectivo: string;
    anio: string;
    division: string;
    tipoPeriodo: string;
    turno: string;
    cambioDePlanCB: PendienteCB[];
    espaciosCurricularesCB: EspacioCurricularesCB[];
    planFortalecimientCB: FortalecimientoCB[];
    pendientesPreviasCB: PreviaCB[];
    codigo?: string;
    ciclo: string;
    jornadaExtendida: boolean | Parameters<typeof getJornadaExtendidaTable>[0]['jornadaExtendida'];
    observacionBoletin?: ObservacionesBoletin[];
    presentismo?: PresentismoTotal;
};

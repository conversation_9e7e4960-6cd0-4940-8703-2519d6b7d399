import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { AlumnoMovimientoCustom } from '../../orm/entities';
export declare class AlumnoMovimientoCustomEndpoint extends MiEscuelaEndpoint<AlumnoMovimientoCustom> {
    constructor();
    alumnoMovimientoUserId(req: any, session: ChinoSessionDTO): Promise<void>;
    preInsertAction(req: any, session: ChinoSessionDTO): Promise<any>;
    preUpdateAction(req: any, session: ChinoSessionDTO): Promise<any>;
    getAllowGuest(): boolean;
}

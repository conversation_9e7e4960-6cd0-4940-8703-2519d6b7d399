"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Step2SectionInsertor = void 0;
const lib_1 = require("../lib");
const utils_1 = require("../utils");
const tables = {
    section: 'seccion',
    location: 'localizacion',
    address: 'domicilio',
    province: 'provincia',
    turn: 'turno',
    plan_study: 'planestudio',
    orientation: 'orientacion',
    cycle_school: 'ciclolectivo',
    status_cycle_school: 'estadociclolectivo',
    educational_unit: 'unidadeducativa',
    level: 'nivel',
};
const Logger = new lib_1.LogManager(`Insertor 2`);
class Step2SectionInsertor {
    async run(context, target, targetContext) {
        Logger.info('provincia');
        for (const value of context.province) {
            const { rows, } = await target.query(`INSERT INTO provincia (id_provincia, nombre_provincia) VALUES ($1, $2) RETURNING *;`, [value.idprovincia, value.descripcion]);
            targetContext.province = (0, utils_1.addToArray)(targetContext.province, rows);
        }
        Logger.info('domicilio');
        for (const value of context.address) {
            const { rows, } = await target.query(`INSERT INTO domicilio_establecimiento (id_domicilio_establecimiento, calle, altura, piso, departamento, ciudad, id_provincia, codigo_postal, asentamiento, manzana, casa, latlon, descripcion) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) RETURNING *;`, [
                value.iddomicilio,
                value.calle,
                value.altura,
                value.piso,
                value.departamento,
                value.ciudad,
                value.idprovincia,
                value.codigopostal,
                value.asentamiento,
                value.manzana,
                value.casa,
                !value.latlon ? null : `(${value.latlon.x},${value.latlon.y})`,
                value.descripcion,
            ]);
            targetContext.address = (0, utils_1.addToArray)(targetContext.address, rows);
        }
        Logger.info('location');
        for (const value of context.location) {
            const { rows, } = await target.query(`INSERT INTO localizacion (id_localizacion, descripcion, anexo, cue_anexo, sede, imagen_fachada, id_establecimiento, id_domicilio_establecimiento) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *;`, [
                value.idlocalizacion,
                'descripcion',
                12,
                12,
                1,
                'imagenfachada',
                value.idestablecimiento,
                value.iddomicilio,
            ]);
            targetContext.location = (0, utils_1.addToArray)(targetContext.location, rows);
        }
        Logger.info('anio');
        const anios = context.section.reduce((acum, section) => {
            if (!acum[section.anio]) {
                acum[section.anio] = {
                    anio: +section.anio || 0,
                    descripcion_anio: section.anio,
                };
            }
            return acum;
        }, {});
        for (const value of Object.values(anios)) {
            const { rows, } = await target.query(`INSERT INTO anio (numero_anio, descripcion_anio) VALUES ($1, $2) RETURNING *;`, [value.anio, value.descripcion_anio]);
            targetContext.anios = (0, utils_1.addToArray)(targetContext.anios, rows);
        }
        Logger.info('level');
        for (const value of context.level) {
            const { rows, } = await target.query(`INSERT INTO nivel (id_nivel, descripcion_nivel) VALUES ($1, $2) RETURNING *;`, [value.idnivel, value.descripcion]);
            targetContext.level = (0, utils_1.addToArray)(targetContext.level, rows);
        }
        Logger.info('educational_unit');
        for (const value of context.educational_unit) {
            const { rows, } = await target.query(`INSERT INTO unidad_educativa (id_unidad_educativa, nombre, nombre_abreviado, id_modalidad, id_nivel) VALUES ($1, $2, $3, $4, $5) RETURNING *;`, [
                value.idunidadeducativa,
                value.nombre,
                value.nombreabreviado,
                value.idmodalidad,
                value.idnivel,
            ]);
            targetContext.educational_unit = (0, utils_1.addToArray)(targetContext.educational_unit, rows);
        }
        Logger.info('orientation');
        for (const value of context.orientation) {
            const { rows, } = await target.query(`INSERT INTO orientacion (id_orientacion, descripcion_orientacion) VALUES ($1, $2) RETURNING *;`, [value.idorientacion, value.descripcion]);
            targetContext.orientation = (0, utils_1.addToArray)(targetContext.orientation, rows);
        }
        Logger.info('plan estudio');
        for (const value of context.plan_study) {
            const { rows, } = await target.query(`INSERT INTO plan_estudio (id_plan_estudio, id_orientacion, descripcion_plan_estudio) VALUES ($1, $2, $3) RETURNING *;`, [
                value.idplanestudio,
                value.idorientacion,
                `descripcion ${value.idorientacion}`,
            ]);
            targetContext.plan_study = (0, utils_1.addToArray)(targetContext.plan_study, rows);
        }
        Logger.info('modality level');
        for (const modality of targetContext.modality) {
            for (const level of targetContext.level) {
                const { rows, } = await target.query(`INSERT INTO modalidad_nivel (id_nivel, id_modalidad) VALUES ($1, $2) RETURNING *;`, [level.id_nivel, modality.id_modalidad]);
                targetContext.modality_level = (0, utils_1.addToArray)(targetContext.modality_level, rows);
            }
        }
        Logger.info('plan de estudio nivel');
        for (const plan of targetContext.plan_study) {
            for (const modalityLevel of targetContext.modality_level) {
                const { rows, } = await target.query(`INSERT INTO plan_estudio_nivel (id_modalidad_nivel, id_plan_estudio) VALUES ($1, $2) RETURNING *;`, [modalityLevel.id_modalidad_nivel, plan.id_plan_estudio]);
                targetContext.plan_study_level = (0, utils_1.addToArray)(targetContext.plan_study_level, rows);
            }
        }
        Logger.info('status_cycle_school');
        for (const value of context.status_cycle_school) {
            const { rows, } = await target.query(`INSERT INTO estado_ciclo_lectivo (id_estado_ciclo_lectivo, descripcion_estado_ciclo_lectivo) VALUES ($1, $2) RETURNING *;`, [value.idestadociclolectivo, value.nombre]);
            targetContext.status_cycle_school = (0, utils_1.addToArray)(targetContext.status_cycle_school, rows);
        }
        Logger.info('cycle_school');
        for (const value of context.cycle_school) {
            const { rows, } = await target.query(`INSERT INTO ciclo_lectivo (id_ciclo_lectivo, anio, fecha_inicio, fecha_fin, id_estado_ciclo_lectivo) VALUES ($1, $2, $3, $4, $5) RETURNING *;`, [
                value.idciclolectivo,
                value.anio,
                value.fechainicio,
                value.fechafin,
                value.idestadociclolectivo,
            ]);
            targetContext.cycle_school = (0, utils_1.addToArray)(targetContext.cycle_school, rows);
        }
        Logger.info('turn');
        for (const value of context.turn) {
            const { rows, } = await target.query(`INSERT INTO turno (id_turno, descripcion_turno) VALUES ($1, $2) RETURNING *;`, [value.idturno, value.descripcion]);
            targetContext.turn = (0, utils_1.addToArray)(targetContext.turn, rows);
        }
        for (const plan_study_level of targetContext.plan_study_level) {
            const plan_study = targetContext.plan_study.find((study) => study.id_plan_estudio == plan_study_level.id_plan_estudio);
            const { rows, } = await target.query(`INSERT INTO ciclo (descripcion_ciclo, id_plan_estudio_nivel, id_orientacion) VALUES ($1, $2, $3) RETURNING *;`, [
                `description ${plan_study_level.id_plan_estudio_nivel}`,
                plan_study_level.id_plan_estudio_nivel,
                plan_study.id_orientacion,
            ]);
            targetContext.cycle = (0, utils_1.addToArray)(targetContext.cycle, rows);
        }
        Logger.info('section');
        for (const [index, value] of context.section.entries()) {
            const anio = targetContext.anios.find((anio) => anio.descripcion_anio === value.anio) || targetContext.anios[0];
            const planestudionivel = (0, utils_1.getRandomElement)(targetContext.plan_study_level);
            const ciclo = (0, utils_1.getRandomElement)(targetContext.cycle);
            if (value.idciclolectivo === null)
                continue;
            const { rows } = await target.query(`INSERT INTO seccion (id_seccion,
                                      nombre_seccion,
                                      jornada,
                                      capacidad_recomendada,
                                      capacidad_maxima,
                                      id_anio,
                                      id_localizacion,
                                      id_plan_estudio_nivel,
                                      id_ciclo_lectivo,
                                      id_unidad_educativa,
                                      id_turno,
                                      id_ciclo) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) RETURNING *;`, [
                value.idseccion,
                `${value.nombre} ${index}`,
                value.jornada,
                value.capacidad_recom || 0,
                value.capacidad_max || 0,
                anio.id_anio,
                value.idlocalizacion,
                planestudionivel.id_plan_estudio_nivel,
                value.idciclolectivo,
                value.idunidadeducativa,
                value.idturno,
                ciclo.id_ciclo,
            ]);
            targetContext.section = (0, utils_1.addToArray)(targetContext.section, rows);
        }
        return targetContext;
    }
}
exports.Step2SectionInsertor = Step2SectionInsertor;
//# sourceMappingURL=Step2SectionInsertor.js.map
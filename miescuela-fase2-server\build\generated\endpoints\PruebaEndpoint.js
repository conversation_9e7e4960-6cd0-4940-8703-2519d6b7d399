"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PruebaEndpoint = void 0;
const just_rpc_1 = require("@phinxlab/just-rpc");
const MiEscuelaEndpointCustom_1 = require("../../app/config/endpoint/MiEscuelaEndpointCustom");
const entities_1 = require("../orm/entities");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
class PruebaEndpoint extends MiEscuelaEndpointCustom_1.MiEscuelaEndpointCustom {
    constructor(name = 'pruebaindi', path = '/public/pruebaindi') {
        super(name, path);
    }
    async exec(req, manager, res) {
        try {
            switch (req.method) {
                case 'GET': {
                    const indicadores = await manager.getRepository(entities_1.Indicadores).find();
                    return indicadores;
                }
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('error', e);
            // TODO NOT CHANGE, LIBBY NOT RECOGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(e.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
    configure() {
        this.allowGuest = true;
        this.addMethod(just_rpc_1.JRMethod.GET);
    }
}
exports.PruebaEndpoint = PruebaEndpoint;
//# sourceMappingURL=PruebaEndpoint.js.map
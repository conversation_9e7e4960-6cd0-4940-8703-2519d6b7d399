"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQueryValues = void 0;
const getQueryValues = (query) => {
    return Object.entries(query).reduce((acum, [key, value]) => {
        if (typeof value === 'object' && !Array.isArray(value)) {
            acum[key] = Object.values(value).pop();
        }
        else {
            acum[key] = value;
        }
        return acum;
    }, {});
};
exports.getQueryValues = getQueryValues;
//# sourceMappingURL=queryValues.js.map
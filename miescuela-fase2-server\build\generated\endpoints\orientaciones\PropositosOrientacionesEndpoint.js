"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropositosOrientacionesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.PropositosOrientacionesDAO;
class PropositosOrientacionesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.propositosorientaciones', '/orientaciones/propositosorientaciones', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PropositosOrientacionesEndpoint = PropositosOrientacionesEndpoint;
//# sourceMappingURL=PropositosOrientacionesEndpoint.js.map
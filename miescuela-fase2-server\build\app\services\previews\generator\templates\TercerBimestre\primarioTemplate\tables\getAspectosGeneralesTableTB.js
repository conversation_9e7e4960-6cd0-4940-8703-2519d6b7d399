"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ vinculoPedagogico, contenidosPendientes, periodoDePromocion, espaciosConsolidando, apoyoAcompaniamiento, propuestasDeTrabajo, organizaParticipa, compromisoAprendizaje, cicloLectivo, anio, tipoPeriodo, }) => `<div class="table-aspectos-container">
        <div class="table-title-container">
          <div>
          ${cicloLectivo} - ${anio} ${tipoPeriodo}
          </div>
        </div>
    <table class="tabla">
      <tr>
          <div>
              <td style="padding-bottom: 15px; text-align: center; width: 25%; font-size: 15px; font-weight: bold; border-right: 0px; background-color: #e2eedb; border-top: 0px;">ASPECTOS GENERALES</td>
          </div>
          <td style="background-color: #e2eedb; border-top: 0px;"></td>
      </tr>
      <tr>
          <td>¿Mantuvo el vínculo pedagógico?</td>
          <td>${vinculoPedagogico}</td>
      </tr>
      <tr>
          <td>En relación a los contenidos pendientes del período 2020:</td>
          <td>${contenidosPendientes}</td>
      </tr>
      <tr>
          <td>En el caso de que continúe en Promoción Acompañada, especificar los espacios curriculares</td>
          <td>${espaciosConsolidando ? espaciosConsolidando : '-'}</td>
      </tr>
      <tr>
          <td>¿Ingresa al período de Promoción Acompañada 2021/2022?</td>
          <td>${periodoDePromocion}</td>
      </tr>
      <tr>
          <td>¿Posee apoyos / acompañamientos?</td>
          <td>${apoyoAcompaniamiento}</td>
      </tr>
      <tr>
          <td>¿Cuáles?</td>
          <td>${organizaParticipa ? organizaParticipa : '-'}</td>
      </tr>
      <tr>
          <td>¿Se organiza y participa en las actividades propuestas a través de los diversos formatos ajustándose a las pautas del trabajo?</td>
          <td> ${propuestasDeTrabajo}</td>
      </tr>
      <tr> 
          <td>¿Se compromete con su aprendizaje reconociendo logros y dificultades?</td>
          <td> ${compromisoAprendizaje}</td>
      </tr>
  </table>
  </div>`;
//# sourceMappingURL=getAspectosGeneralesTableTB.js.map
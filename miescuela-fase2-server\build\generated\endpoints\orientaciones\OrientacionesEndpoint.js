"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrientacionesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.OrientacionesDAO;
class OrientacionesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.orientaciones', '/orientaciones/orientaciones', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.OrientacionesEndpoint = OrientacionesEndpoint;
//# sourceMappingURL=OrientacionesEndpoint.js.map
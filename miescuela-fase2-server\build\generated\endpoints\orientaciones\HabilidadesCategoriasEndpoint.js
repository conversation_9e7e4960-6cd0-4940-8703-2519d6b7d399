"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HabilidadesCategoriasEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.HabilidadesCategoriasDAO;
class HabilidadesCategoriasEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.habilidadescategorias', '/orientaciones/habilidadescategorias', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.HabilidadesCategoriasEndpoint = HabilidadesCategoriasEndpoint;
//# sourceMappingURL=HabilidadesCategoriasEndpoint.js.map
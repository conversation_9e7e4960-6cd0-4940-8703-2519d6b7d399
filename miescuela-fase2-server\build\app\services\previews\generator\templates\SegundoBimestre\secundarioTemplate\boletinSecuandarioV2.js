"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBoletinSecundarioV2 = void 0;
const DeudaAcademicaTemplates_1 = require("./DeudaAcademicaTemplates");
const getPresentismoJESegundoBimestre_1 = require("./DeudaAcademicaTemplates/getPresentismoJESegundoBimestre");
const getPresentismoSegundoBimestre_1 = require("./DeudaAcademicaTemplates/getPresentismoSegundoBimestre");
const getJornadaExtendidaTableV2_1 = require("../../TercerBimestre/utils/getJornadaExtendidaTableV2");
const bkp_1 = require("./tables/bkp");
const createBoletinSecundarioV2 = ({ nombre, apellido, secundario, distritoEscolar, localizacion, anioLectivo, anio, documento, division, tipoPeriodo, previa = [], fortalecimiento, cambioPlan, totalCalificaciones, turno, totalDeudaAcademica, totalValoracion, jornadaExtendida = [], jeIndicadores = [], codigo, codigoVinculo, isJE, presentismo, presentismoJE, getEscudo, observacionBoletin, }) => /*html*/ `
<!doctype html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <link type="text/css" rel="stylesheet" href="resources/sheet.css">
  <style type="text/css">
    .ff-arial {
      font-family: Arial, Helvetica, sans-serif;
    }

    .ff-calibri {
      font-family: 'docs-Calibri', Arial;
    }

    .fs-9 {
      font-size: 9pt;
    }

    .fs-10 {
      font-size: 10pt;
    }

    .fs-11 {
      font-size: 11pt;
    }

    .fs-12 {
      font-size: 12pt;
    }

    .fs-13 {
      font-size: 13pt;
    }

    .fs-14 {
      font-size: 14pt;
    }

    .ws-normal {
      white-space: normal;
    }

    .ws-nowrap {
      white-space: nowrap;
    }

    .va-bottom {
      vertical-align: bottom;
    }

    .va-middle {
      vertical-align: middle;
    }

    .bold {
      font-weight: bold;
    }

    .bgc-gray {
      background-color: #cccccc !important;
    }

    .cell {
      background-color: #ffffff;
      color: #000000;
      padding: 0px 3px 0px 3px;
      overflow: hidden;
      direction: ltr;
      word-wrap: break-word;
    }

    .tal {
      text-align: left;
    }

    .tar {
      text-align: right;
    }

    .tac {
      text-align: center;
    }

    .bb-1 {
      border-bottom: 1px solid black;
    }

    .bb-2 {
      border-bottom: 2px solid black;
    }

    .bt-1 {
      border-top: 1px solid black;
    }

    .bt-2 {
      border-top: 2px solid black;
    }

    .bl-1 {
      border-left: 1px solid black;
    }

    .bl-2 {
      border-left: 2px solid black;
    }

    .br-1 {
      border-right: 1px solid black;
    }

    .br-2 {
      border-right: 2px solid black;
    }

    .wb-all {
      overflow-wrap: normal;
    }

    @page {
      size: A4;
      margin: auto;
    }

    .marca-agua {
      position: fixed;
      top: 0%;
      left: 2%;
      margin: 12%;
      width: 80vw;
      filter: opacity(0.1);
      pointer-events: none;
    }

    @media print {
      @page {
        size: A4; margin: 0 auto; margin-top: 5mm;
      }

      .marca-agua {
        position: fixed; top: 0%; left: 0%;
        margin: -1.5%; margin-top: 20%; width: 100vw; height: 100vh;
        filter: opacity(0.1) brightness(1.1); pointer-events: none; padding-left: 10px;
      }

      .table-bimestres {
        page-break-inside: avoid;
      }
    }
  </style>



</head>

<body>
  <div class="grid-container" dir="ltr">
  <div class="marca-agua">
    ${getEscudo && getEscudo({ isWatermark: true })}
  </div>
    <table class="table-bimesetre" cellspacing="0" cellpadding="0">
      <thead>
        <tr>
          <th id="209587639C0" style="width:303px;"></th>
          <th id="209587639C1" style="width:148px;"></th>
          <th id="209587639C2" style="width:148px;"></th>
          <th id="209587639C3" style="width:107px;"></th>
          <th id="209587639C4" style="width:107px;"></th>
          <th id="209587639C5" style="width:150px;"></th>
        </tr>
      </thead>
      <tbody>
        <tr style="height: 19px">
          <td class="cell tac ff-arial fs-9 ws-normal" colspan="6" rowspan="3">
            <div style="justify-content: center; display: flex; flex-direction: column;">
            <div style="justify-content: center; display: flex; padding-top: 10px; height: 45px; width: 39px; margin: 0 auto;">
              ${getEscudo && getEscudo({ isWatermark: false })}
            </div>
              <div class="bold" style="justify-content: center; display: flex; flex-direction: column;">
                <br>Gobierno de la Ciudad de Buenos Aires
                <br>Ministerio de Educación
                <br>D.E. ${distritoEscolar}
              </div>
            </div>
          </td>
        </tr>

        <tr style="height: 19px">
        </tr>
        <tr style="height: 19px">
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 18px">
          <td class="cell tac ff-arial fs-9 ws-normal bold" colspan="6">Establecimiento: ${localizacion}</td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell va-bottom ws-nowrap fs-10 ff-arial tac bold" dir="ltr" colspan="6">
            ${tipoPeriodo} - Ciclo Lectivo: ${anioLectivo}
          </td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell ws-nowrap" colspan="3">
            <span class="fs-10 ff-arial bold">Estudiante: </span>
            <span class="fs-10 ff-arial">${nombre} ${apellido}</span>
          </td>
          <td class="cell tal ff-arial fs-10 va-bottom ws-nowrap" colspan="3">
            <span class="bold">Año: </span>
            <span>${anio}</span>
          </td>
        </tr>
        <tr style="height: 19px">
          <td class="cell ff-calibri tal fs-10 va-bottom ws-nowrap" colspan="3">
            <span class="bold">Documento:</span>
            <span>${documento}</span>
          </td>
          <td class="cell tal ff-arial fs-10 va-bottom ws-nowrap" colspan="3">
            <span class="bold">Turno: </span>
            <span>${turno}</span>
          </td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="3"></td>
          <td class="cell tal ff-arial fs-10 ws-nowrap va-bottom" colspan="3">
            <span class="bold">División:</span>
            <span>${division}</span>
          </td>
        </tr>
        <tr style="height: 19px">
          <td class="cell tal ff-calibri fs-10 va-bottom ws-nowrap" colspan="3">
            <span class="bold">Código de vinculación (App Familias):</span>
            <span>${codigoVinculo || '-'}</span>
          </td>
          <td class="cell" colspan="3"></td>
        </tr>

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>

      <tr id="tablee uno">
          <td class="cell" colspan="6">
            ${(0, bkp_1.getCalificacionesSecundarioV2)(tipoPeriodo, secundario || [])}
          </td>
        </tr>

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>

        ${(jornadaExtendida === null || jornadaExtendida === void 0 ? void 0 : jornadaExtendida.length) > 0
    ? `
        <tr id="table dos y media">
          <td class="cell" colspan="6">
          ${(0, getJornadaExtendidaTableV2_1.getJornadaExtendidaTableV2)(jornadaExtendida, jeIndicadores, tipoPeriodo)}
          </td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>`
    : ``}

        <tr id="table dos">
          <td class="cell" colspan="6">
            ${(0, DeudaAcademicaTemplates_1.getEspaciosCurricularesPendientesV2)(previa)}
          </td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>

        <tr id="tablee trees">
          <td class="cell" colspan="6">
            ${(0, DeudaAcademicaTemplates_1.getFortalecimientoAprendizajesV2)(tipoPeriodo, fortalecimiento || [])}
          </td>
        </tr>

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>

      ${(cambioPlan || []).length > 0
    ? `<tr id="table cuatro">
          <td class="cell" colspan="6">
            ${(0, DeudaAcademicaTemplates_1.getEspaciosCurricularesPendientesCambioDePlanV2)(cambioPlan || [])}
          </td>
        </tr>

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>`
    : ``}

       ${tipoPeriodo !== 'Primer Bimestre' &&
    ` <tr id="table cinco">
          <td class="cell" colspan="6">
          ${(0, getPresentismoJESegundoBimestre_1.getPresentismoSegundoBimestre)(tipoPeriodo, presentismo, observacionBoletin.find((observacion) => !observacion.isJornadaExtendida))}
          </td>
        </tr>
           <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>`}

     ${tipoPeriodo !== 'Primer Bimestre' && (presentismoJE === null || presentismoJE === void 0 ? void 0 : presentismoJE.hasPresentismo)
    ? ` <tr id="table cinco">
          <td class="cell" colspan="6">
          ${(0, getPresentismoSegundoBimestre_1.getPresentismoJESegundoBimestre)(tipoPeriodo, presentismoJE, observacionBoletin.find((observacion) => observacion.isJornadaExtendida))}
          </td>
        </tr>
        `
    : ''}

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>

        <tr style="height: 19px" style="page-break-inside: avoid;">

          <td class="cell bl-2 br-2 bt-2 bb-2 ff-arial fs-10 tac" rowspan="3" style="vertical-align: top;">Firma y sello del Directivo</td>
          <td class="cell"></td>
          <td class="cell bl-2 br-2 bt-2 bb-2 ff-arial fs-10 tac" rowspan="3" style="vertical-align: top;">Firma Responsable</td>
          <td class="cell" colspan="2"></td>
          <td class="cell bl-2 br-2 bt-2 bb-2 ff-arial fs-10 tac" rowspan="3" style="vertical-align: top;">Firma del estudiante</td>
        </tr>

        <tr style="height: 19px">
          <td class="cell"></td>
          <td class="cell"></td>
          <td class="cell"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell"></td>
          <td class="cell"></td>
          <td class="cell"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell ff-arial bold fs-10 tac" dir="ltr" colspan="6">
            Este es un documento oficial emitido por ${localizacion} a través
            del sistema miEscuela (RES N° 5788-GCABA-MEIGC/19) conforme el Reglamento Escolar de la Ciudad Autónoma de
            Buenos Aires.</td>
          <td class="s1"></td>
        </tr>

        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
        <tr style="height: 19px">
          <td class="cell" colspan="6"></td>
        </tr>
      </tbody>
    </table>
  </div>
</body>

</html>
`;
exports.createBoletinSecundarioV2 = createBoletinSecundarioV2;
//# sourceMappingURL=boletinSecuandarioV2.js.map
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { SeccionDocente } from '../../orm/entities';
export declare class SeccionDocenteEndpoint extends MiEscuelaEndpoint<SeccionDocente> {
    constructor();
    preDeleteAction(req: any, session: ChinoSessionDTO): Promise<void>;
    getAllowGuest(): boolean;
}

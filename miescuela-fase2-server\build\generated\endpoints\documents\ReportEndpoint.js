"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ReportEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ReportDAO.entity.toLowerCase(), '/documents/' + dao_1.ReportDAO.entity.toLowerCase(), dao_1.ReportDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ReportEndpoint = ReportEndpoint;
//# sourceMappingURL=ReportEndpoint.js.map
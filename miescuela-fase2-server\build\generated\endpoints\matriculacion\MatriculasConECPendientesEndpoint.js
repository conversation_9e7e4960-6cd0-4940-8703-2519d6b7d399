"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificacionConfirmacionMatriculaEndpoint = void 0;
const config_1 = require("../../../app/config");
const dao_1 = require("../../orm/dao");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
class VerificacionConfirmacionMatriculaEndpoint extends config_1.MiEscuelaEndpointCustom {
    constructor(name = 'matriculas_ec_pendientes', path = '/public/proyecciones/matriculas-ec-pendientes') {
        super(name, path);
    }
    validate(params) {
        if (!('seccion' in params)) {
            throw chino_sdk_1.ChinoCustomError.CustomException('Debe enviar la sección', 400);
        }
    }
    async exec(req, manager, res) {
        var _a;
        const params = req.query;
        this.validate(params);
        const matriculas = await dao_1.AlumnoMovimientoDAO.query('ec-pendientes')
            .setContext(this.context)
            .equals('seccion', params.seccion)
            .run(manager);
        if (matriculas.length === 0) {
            throw chino_sdk_1.ChinoCustomError.CustomException('No hay alumnos en esta sección', 400);
        }
        const calfsPendientes = await dao_1.CalificacionesSecundariaAnualDAO.getPendientesOfSeccion(params.seccion, matriculas.map((mat) => mat.alumno.idAlumno));
        const calificacionesAlumnoMap = calfsPendientes.reduce((prev, curr) => {
            const pendientes = prev[curr.alumno.idAlumno];
            prev[curr.alumno.idAlumno] = [
                ...pendientes,
                curr.espacioCurricularSeccion,
            ];
            return prev;
        }, {});
        const response = [];
        for (const matricula of matriculas) {
            response.push({
                ...matricula,
                ecPendientes: (_a = calificacionesAlumnoMap[matricula.alumno.idAlumno]) !== null && _a !== void 0 ? _a : [],
            });
        }
        return response;
    }
    configure() {
        this.allowGuest = false;
    }
}
exports.VerificacionConfirmacionMatriculaEndpoint = VerificacionConfirmacionMatriculaEndpoint;
//# sourceMappingURL=MatriculasConECPendientesEndpoint.js.map
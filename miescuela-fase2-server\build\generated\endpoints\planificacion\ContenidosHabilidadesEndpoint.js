"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContenidosHabilidadesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ContenidosHabilidadesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ContenidosHabilidadesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.ContenidosHabilidadesDAO.entity.toLowerCase(), dao_1.ContenidosHabilidadesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ContenidosHabilidadesEndpoint = ContenidosHabilidadesEndpoint;
//# sourceMappingURL=ContenidosHabilidadesEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LMTipoDocumentoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class LMTipoDocumentoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.TipoDocumentoDAO.entity.toLowerCase(), '/' + dao_1.TipoDocumentoDAO.entity.toLowerCase(), dao_1.TipoDocumentoDAO);
    }
    postSelectAction(values, session, req) {
        return Promise.resolve(values.map((tipoDoc) => ({
            id: tipoDoc.idTipoDocumento,
            descripcion: tipoDoc.descripcionTipoDocumento,
        })));
    }
    getAllowGuest() {
        return false;
    }
}
exports.LMTipoDocumentoEndpoint = LMTipoDocumentoEndpoint;
//# sourceMappingURL=LMTiposDocumentosEndpoint.js.map
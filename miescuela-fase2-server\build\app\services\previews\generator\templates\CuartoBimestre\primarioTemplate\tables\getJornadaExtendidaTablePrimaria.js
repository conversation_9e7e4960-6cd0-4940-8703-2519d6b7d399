"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getJornadaExtendidaTablePrimaria = void 0;
const getJornadaExtendidaTablePrimaria = (idAlumnoActual, jornadaExtendida, jeIndicadores, tipoPeriodo, ciclo) => {
    let html = '<table class="table-bimestres" cellspacing="0" cellpadding="0">';
    jornadaExtendida
        .filter((je) => je.calificacion.alumno.idAlumno === idAlumnoActual)
        .forEach((je, index) => {
        if (Object.entries(je.data)
            .filter(([key, _]) => key !== 'observaciones')
            .every(([_, value]) => Boolean(value))) {
            const indicadorNota = jeIndicadores.filter((indi) => indi.jeMateria.idJEMateria === je.jemateria.idJEMateria &&
                je.data !== null &&
                indi.key in je.data);
            const indicadorNotaMapped = indicadorNota
                .map((indicador, indicadorIndex) => `
          <tr>
            <td class="cell to-left ff-calibri fs-11 bt-1 bl-1 br-1 ${indicadorIndex === indicadorNota.length - 1 ? 'bb-1' : ''}">${indicador.pregunta}</td>
            <td class="cell to-left ff-calibri fs-11 bt-1 br-1 ${indicadorIndex === indicadorNota.length - 1 ? 'bb-1' : ''}">${je.data ? je.data[indicador.key] : ''}</td>
          </tr>
          ${indicadorIndex === indicadorNota.length - 1
                ? `<tr style="height: 29px">
                  <td class="cell" colspan="6"></td>
              </tr>`
                : ``}
        `)
                .join('');
            const template = /*html*/ `
        <thead>
          ${index === 0
                ? `<tr class="heading">
            <th class="cell tac bold ff-calibri fs-11 bt-1 bgc-gray bl-1 br-1" rowspan="2">
              ${ciclo}
            </th>
            </tr>
            <tr>
              <th class="tac fs-11 ff-calibri va-middle ws-normal bold br-1 bt-1 bgc-gray" rowspan="2">
                <b>${tipoPeriodo}</b>
              </th>
            </tr>`
                : ''}
          <tr>
            <th class="cell tal bold ff-calibri fs-10 bt-1 bgc-gray bl-1 br-1">${je.jemateria.descripcion}</th>
            ${index !== 0
                ? `
            <th class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bgc-gray">
              <b>${tipoPeriodo}</b>
            </th>
            `
                : ''}
          </tr>
        </thead>
        <tbody>
          ${indicadorNotaMapped}
        </tbody>
  `;
            html += template;
        }
    });
    html += '</table>';
    return html;
};
exports.getJornadaExtendidaTablePrimaria = getJornadaExtendidaTablePrimaria;
//# sourceMappingURL=getJornadaExtendidaTablePrimaria.js.map
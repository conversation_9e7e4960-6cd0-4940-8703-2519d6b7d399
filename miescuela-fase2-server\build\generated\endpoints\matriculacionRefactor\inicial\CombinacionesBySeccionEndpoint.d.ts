import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { Seccion } from '../../../orm/entities';
import { MiEscuelaEndpointV2 } from '../../../../app/config/endpoint/MiEscuelaEndpointV2';
export declare class CombinacionesBySeccionEndpoint extends MiEscuelaEndpointV2<Seccion> {
    constructor();
    postSelectAction(values: Seccion[], session: ChinoSessionDTO, req: JRRequest): Promise<any>;
}

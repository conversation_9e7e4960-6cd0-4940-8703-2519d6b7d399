"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComedorEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class ComedorEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ComedorDAO.entity.toLowerCase(), '/public/' + dao_1.ComedorDAO.entity.toLowerCase(), dao_1.ComedorDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ComedorEndpoint = ComedorEndpoint;
//# sourceMappingURL=ComedorEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanIndicadoresDeEvaluacionEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PlanIndicadoresDeEvaluacionEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PlanIndicadoresDeEvaluacionDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PlanIndicadoresDeEvaluacionDAO.entity.toLowerCase(), dao_1.PlanIndicadoresDeEvaluacionDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PlanIndicadoresDeEvaluacionEndpoint = PlanIndicadoresDeEvaluacionEndpoint;
//# sourceMappingURL=PlanIndicadoresDeEvaluacionEndpoint.js.map
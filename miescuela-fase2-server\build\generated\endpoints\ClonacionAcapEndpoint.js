"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClonacionAcapEndpoint = void 0;
const moment_1 = __importDefault(require("moment"));
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const just_rpc_1 = require("@phinxlab/just-rpc");
const entities_1 = require("../orm/entities");
const Log = new chino_sdk_1.ChinoLogManager('CLONACION ACAP');
class ClonacionAcapEndpoint extends just_rpc_1.JREndpoint {
    constructor(name = 'clonacionacap', path = '/public/custom/clonacionacap') {
        super(name, path);
        this.accionesMap = new Map();
        this.ofertasMap = new Map();
        this.ofertasTurnosMap = new Map();
        this.ofertasGruposMap = new Map();
        this.accionesOrientacionesMap = new Map();
        this.tx = chino_sdk_1.ChinoManager.getManager();
        this.contenidosCount = 0;
        this.accionRepo = this.tx.getRepository(entities_1.Accion);
    }
    configure() {
        this.allowGuest = true;
        this.addMethod(just_rpc_1.JRMethod.POST);
    }
    async execute(req, res, next) {
        try {
            switch (req.method) {
                case 'POST':
                    await this.clonar();
                    return res.status(chino_sdk_1.STATUS_CODE.OK).send({
                        success: true,
                        message: 'Proceso ejecutado exitosamente.',
                    });
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (error) {
            console.log('error', error);
            // TODO NOT CHANGE, LIBBY NOT RECONGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(error.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
    async clonar() {
        await this.getCicloActual();
        await this.cloneAcciones();
        await this.cloneAccionesOrientaciones();
        await this.cloneAccionesOrientacionesContenidos();
        await this.cloneAccionesOrientacionesHabilidades();
        await this.cloneAccionesOrientacionesPerfiles();
        await this.cloneOfertas();
        await this.cloneOfertasTurnos();
        await this.cloneOfertasGrupos();
        await this.cloneGruposSelecciones();
        await this.cloneGrupoJornadas();
        await this.cloneGrupoInscripciones();
    }
    async getCicloActual() {
        const ciclo = await this.tx.getRepository(entities_1.CicloLectivo).findOne({
            where: {
                idCicloLectivo: ClonacionAcapEndpoint.CICLO_LECTIVO_CLONAR,
            },
        });
        if (!ciclo)
            throw chino_sdk_1.ChinoCustomError.CustomException('No existe el ciclo lectivo que se va a copiar', 500);
        this.cicloLectivo = ciclo;
    }
    async cloneAcciones() {
        const newCondition = {
            cicloLectivo: {
                idCicloLectivo: ClonacionAcapEndpoint.CICLO_LECTIVO_CLONAR,
            },
        };
        const originalCondition = {
            cicloLectivo: {
                idCicloLectivo: ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL,
            },
        };
        const total2023 = await this.accionRepo.count({
            where: newCondition,
        });
        // Eliminar los registros del ciclo
        // que se va clonar
        if (total2023 > 0) {
            await this.accionRepo.delete(newCondition);
        }
        // Las que se clonaran
        const accionesActuales = await this.accionRepo.find({
            where: originalCondition,
            relations: ['organizacion', 'tipoAccion', 'createdBy', 'deletedBy'],
        });
        // clonar registros
        for (const entity of accionesActuales) {
            const { idAccion, ...accion } = entity;
            accion.cicloLectivo = this.cicloLectivo;
            const saved = await this.accionRepo.save(accion);
            this.accionesMap.set(idAccion, saved);
        }
    }
    async cloneAccionesOrientaciones() {
        // Las que se clonaran
        const accionesOrientacionesOriginales = await this.tx.query(`
      SELECT * FROM acap.acciones_orientaciones ao JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of accionesOrientacionesOriginales) {
            const { id_accion_orientacion, ...accionOrientacion } = entity;
            const accion = this.accionesMap.get(String(accionOrientacion.id_accion));
            if (accion) {
                const saved = await this.tx.query(`INSERT INTO acap.acciones_orientaciones (id_accion, id_orientacion) VALUES($1,$2) RETURNING id_accion_orientacion`, [accion.idAccion, accionOrientacion.id_orientacion]);
                this.accionesOrientacionesMap.set(String(id_accion_orientacion), saved[0]);
            }
        }
    }
    async cloneAccionesOrientacionesContenidos() {
        // Las que se clonaran
        const accionesOrientacionesContenidosOriginales = await this.tx.query(`
      SELECT aoc.id_accion_orientacion, aoc.id_nodo FROM acap.acciones_orientaciones_contenidos aoc
      JOIN acap.acciones_orientaciones ao ON ao.id_accion_orientacion = aoc.id_accion_orientacion
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of accionesOrientacionesContenidosOriginales) {
            const accionOrientacion = this.accionesOrientacionesMap.get(String(entity.id_accion_orientacion));
            if (accionOrientacion) {
                await this.tx.query(`INSERT INTO acap.acciones_orientaciones_contenidos (id_accion_orientacion, id_nodo) VALUES($1,$2)`, [accionOrientacion.id_accion_orientacion, entity.id_nodo]);
                this.contenidosCount++;
            }
        }
    }
    async cloneAccionesOrientacionesHabilidades() {
        // Las que se clonaran
        const accionesOrientacionesHabilidadesOriginales = await this.tx.query(`
      SELECT aoh.id_habilidad, aoh.id_accion_orientacion FROM acap.acciones_orientaciones_habilidades aoh
      JOIN acap.acciones_orientaciones ao ON ao.id_accion_orientacion = aoh.id_accion_orientacion
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of accionesOrientacionesHabilidadesOriginales) {
            const accionOrientacion = this.accionesOrientacionesMap.get(String(entity.id_accion_orientacion));
            if (accionOrientacion) {
                await this.tx.query(`INSERT INTO acap.acciones_orientaciones_habilidades (id_accion_orientacion, id_habilidad) VALUES($1,$2)`, [accionOrientacion.id_accion_orientacion, entity.id_habilidad]);
            }
        }
    }
    async cloneAccionesOrientacionesPerfiles() {
        // Las que se clonaran
        const accionesOrientacionesPerfilesOriginales = await this.tx.query(`
      SELECT aop.id_accion_orientacion, aop.id_perfil FROM acap.acciones_orientaciones_perfiles aop
      JOIN acap.acciones_orientaciones ao ON ao.id_accion_orientacion = aop.id_accion_orientacion
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of accionesOrientacionesPerfilesOriginales) {
            const accionOrientacion = this.accionesOrientacionesMap.get(String(entity.id_accion_orientacion));
            if (accionOrientacion) {
                await this.tx.query(`INSERT INTO acap.acciones_orientaciones_perfiles (id_accion_orientacion, id_perfil) VALUES($1,$2)`, [accionOrientacion.id_accion_orientacion, entity.id_perfil]);
            }
        }
    }
    async cloneOfertas() {
        // Las que se clonaran
        const ofertasOriginales = await this.tx.query(`
      SELECT of.* FROM acap.ofertas of JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of ofertasOriginales) {
            const { id_oferta, ...oferta } = entity;
            const accion = this.accionesMap.get(String(oferta.id_accion));
            if (accion) {
                const newFechaInicio = (0, moment_1.default)(oferta.fecha_inicio)
                    .add(1, 'year')
                    .toISOString();
                const newFechaFin = (0, moment_1.default)(oferta.fecha_fin)
                    .add(1, 'year')
                    .toISOString();
                const saved = await this.tx.query(`INSERT INTO acap.ofertas (id_accion, fecha_inicio, fecha_fin, fecha_publicacion) VALUES($1,$2,$3,$4) RETURNING *;`, [
                    accion.idAccion,
                    newFechaInicio,
                    newFechaFin,
                    oferta.fecha_publicacion,
                ]);
                this.ofertasMap.set(id_oferta, saved[0]);
            }
        }
    }
    async cloneOfertasTurnos() {
        // Las que se clonaran
        const ofertasTurnosOriginales = await this.tx.query(`
      SELECT oft.* FROM acap.ofertas_turnos oft
      JOIN acap.ofertas of USING(id_oferta)
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of ofertasTurnosOriginales) {
            const { id_oferta_turno, ...ofertaTurno } = entity;
            const oferta = this.ofertasMap.get(String(ofertaTurno.id_oferta));
            const query = /*sql*/ `INSERT INTO acap.ofertas_turnos (id_oferta, id_turno, cupo, grupos) VALUES($1,$2,$3,$4) RETURNING *;`;
            if (oferta) {
                const saved = await this.tx.query(query, [
                    oferta.id_oferta,
                    ofertaTurno.id_turno,
                    ofertaTurno.cupo,
                    ofertaTurno.grupos,
                ]);
                this.ofertasTurnosMap.set(id_oferta_turno, saved[0]);
            }
        }
    }
    async cloneOfertasGrupos() {
        // Las que se clonaran
        const ofertasGruposOriginales = await this.tx.query(`
      SELECT ofg.* FROM acap.ofertas_grupos ofg
      JOIN acap.ofertas_turnos oft USING(id_oferta_turno)
      JOIN acap.ofertas of USING(id_oferta)
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of ofertasGruposOriginales) {
            const { id_oferta_grupo, ...ofertaGrupo } = entity;
            const ofertaTurno = this.ofertasTurnosMap.get(String(ofertaGrupo.id_oferta_turno));
            if (ofertaTurno) {
                const saved = await this.tx.query(`INSERT INTO acap.ofertas_grupos (
            id_oferta_turno,
            tipo_cupo,
            cupo_estatal,
            cupo_privado,
            establecimiento_privado,
            id_localizacion,
            id_referente
          ) VALUES($1,$2,$3,$4,$5,$6,$7) RETURNING *`, [
                    ofertaTurno.id_oferta_turno,
                    ofertaGrupo.tipo_cupo,
                    ofertaGrupo.cupo_estatal,
                    ofertaGrupo.cupo_privado,
                    ofertaGrupo.establecimiento_privado,
                    ofertaGrupo.id_localizacion,
                    ofertaGrupo.id_referente,
                ]);
                this.ofertasGruposMap.set(id_oferta_grupo, saved[0]);
            }
        }
    }
    async cloneGruposSelecciones() {
        // Las que se clonaran
        const gruposSeleccionesOriginales = await this.tx.query(`
      SELECT gs.* FROM acap.grupos_selecciones gs
      JOIN acap.ofertas_grupos ofg USING(id_oferta_grupo)
      JOIN acap.ofertas_turnos oft USING(id_oferta_turno)
      JOIN acap.ofertas of USING(id_oferta)
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of gruposSeleccionesOriginales) {
            const { id_grupo_seleccion, ...grupoSeleccion } = entity;
            const ofertaGrupo = this.ofertasGruposMap.get(String(grupoSeleccion.id_oferta_grupo));
            if (ofertaGrupo) {
                await this.tx.query(`INSERT INTO acap.grupos_selecciones (
            id_oferta_grupo,
            id_localizacion,
            choosen_by,
            choosen_at,
            deleted_by,
            deleted_at
          ) VALUES($1,$2,$3,$4,$5,$6)`, [
                    ofertaGrupo.id_oferta_grupo,
                    grupoSeleccion.id_localizacion,
                    grupoSeleccion.choosen_by,
                    grupoSeleccion.choosen_at,
                    grupoSeleccion.deleted_by,
                    grupoSeleccion.deleted_at,
                ]);
            }
        }
    }
    async cloneGrupoJornadas() {
        // Las que se clonaran
        const gruposJornadasOriginales = await this.tx.query(`
      SELECT gj.* FROM acap.grupos_jornadas gj
      JOIN acap.ofertas_grupos ofg USING(id_oferta_grupo)
      JOIN acap.ofertas_turnos oft USING(id_oferta_turno)
      JOIN acap.ofertas of USING(id_oferta)
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of gruposJornadasOriginales) {
            const { id_grupo_jornada, ...grupoJornada } = entity;
            const ofertaGrupo = this.ofertasGruposMap.get(String(grupoJornada.id_oferta_grupo));
            if (ofertaGrupo) {
                await this.tx.query(`INSERT INTO acap.grupos_jornadas (
            id_oferta_grupo,
            dia,
            hora_inicio,
            hora_fin,
            id_sede
          ) VALUES($1,$2,$3,$4,$5)`, [
                    ofertaGrupo.id_oferta_grupo,
                    grupoJornada.dia,
                    grupoJornada.hora_inicio,
                    grupoJornada.hora_fin,
                    grupoJornada.id_sede,
                ]);
            }
        }
    }
    async cloneGrupoInscripciones() {
        // Las que se clonaran
        const gruposInscripcionesOriginales = await this.tx.query(`
      SELECT gi.* FROM acap.grupos_inscripciones gi
      JOIN acap.ofertas_grupos ofg USING(id_oferta_grupo)
      JOIN acap.ofertas_turnos oft USING(id_oferta_turno)
      JOIN acap.ofertas of USING(id_oferta)
      JOIN acap.acciones ac USING(id_accion)
      WHERE id_ciclo_lectivo = $1;
    `, [ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL]);
        // clonar registros
        for (const entity of gruposInscripcionesOriginales) {
            const { id_grupo_inscripcion, ...grupoInscripcion } = entity;
            const ofertaGrupo = this.ofertasGruposMap.get(String(grupoInscripcion.id_oferta_grupo));
            if (ofertaGrupo) {
                await this.tx.query(`INSERT INTO acap.grupos_inscripciones (
            id_oferta_grupo,
            id_alumno_movimiento,
            created_by,
            created_at,
            deleted_by,
            deleted_at
          ) VALUES($1,$2,$3,$4,$5,$6)`, [
                    ofertaGrupo.id_oferta_grupo,
                    grupoInscripcion.id_alumno_movimiento,
                    grupoInscripcion.created_by,
                    grupoInscripcion.created_at,
                    grupoInscripcion.deleted_by,
                    grupoInscripcion.deleted_at,
                ]);
            }
        }
    }
}
exports.ClonacionAcapEndpoint = ClonacionAcapEndpoint;
ClonacionAcapEndpoint.CICLO_LECTIVO_ORIGINAL = 23;
ClonacionAcapEndpoint.CICLO_LECTIVO_CLONAR = 24;
//# sourceMappingURL=ClonacionAcapEndpoint.js.map
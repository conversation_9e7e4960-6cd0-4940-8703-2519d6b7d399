import { JRRequest, JRResponse } from '@phinxlab/just-rpc';
import { EntityManager } from 'typeorm';
import { MiEscuelaEndpointCustom } from '../../app/config/endpoint/MiEscuelaEndpointCustom';
export declare class PruebaEndpoint extends MiEscuelaEndpointCustom {
    constructor(name?: string, path?: string);
    exec(req: JRRequest, manager: EntityManager, res: JRResponse): Promise<any>;
    configure(): void;
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfertasEndpointV2 = void 0;
const endpoint_1 = require("../../../app/config/endpoint");
const dao_1 = require("../../orm/dao");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const Log = new chino_sdk_1.ChinoLogManager('OfertasEndpointV2');
class OfertasEndpointV2 extends endpoint_1.MiEscuelaEndpointV2 {
    constructor() {
        super('acap.ofertas_v2', '/acap/ofertas_v2', dao_1.OfertasV2DAO);
        this.postSelectAction = async (values, session, req) => {
            const tx = chino_sdk_1.ChinoManager.getManager();
            if (values.length &&
                req.headers['x-chino-aspect'] === 'oferta-has-alumnos') {
                const context = chino_sdk_1.ChinoContext.fromSession(session);
                return Promise.all(values.map(async (oferta) => ({
                    ...oferta,
                    hasInscriptos: await hasInscriptos(getIdOfertaGrupoList(oferta), context, tx),
                })));
            }
            return values;
        };
    }
    getAllowGuest() {
        return false;
    }
}
exports.OfertasEndpointV2 = OfertasEndpointV2;
const hasInscriptos = async (idOfertaGrupos, context, tx) => {
    if (!(idOfertaGrupos === null || idOfertaGrupos === void 0 ? void 0 : idOfertaGrupos.length))
        return false;
    const inscripciones = await dao_1.GruposInscripcionesV2DAO.query('inscripciones-con-alumnos')
        .setContext(context)
        .in('ofertaGrupo.idOfertaGrupo', idOfertaGrupos)
        .isnull('deletedAt', true)
        .limit(1)
        .run(tx);
    return (inscripciones === null || inscripciones === void 0 ? void 0 : inscripciones.length) > 0;
};
const getIdOfertaGrupoList = (oferta) => {
    var _a;
    return ((_a = oferta === null || oferta === void 0 ? void 0 : oferta.turnos) !== null && _a !== void 0 ? _a : [])
        .flatMap((t) => { var _a, _b; return (_b = (_a = t === null || t === void 0 ? void 0 : t.grupos) === null || _a === void 0 ? void 0 : _a.map((g) => g === null || g === void 0 ? void 0 : g.idOfertaGrupo)) !== null && _b !== void 0 ? _b : []; })
        .filter((id) => id != null);
};
//# sourceMappingURL=OfertasV2Endpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = (jornadaExtendida, jeIndicadores, tipoPeriodo) => {
    let html = '<div class="calificaciones-container">';
    jornadaExtendida.forEach((je, index) => {
        var _a, _b;
        if (Object.entries(je.data)
            .filter(([key, _]) => key !== 'observaciones')
            .every(([_, value]) => Boolean(value))) {
            const template = /*html*/ `
      <table cellpadding="0" cellspacing="0" ${index % 2 && index > 0 ? 'class="breaker-after"' : ''}>
          <thead>
          ${index === 0
                ? /*html*/ `
            <tr class="heading">
              <th class="darker center text-single-line" rowspan="2">
                  ESPACIOS EDUCATIVOS DE JORNADA EXTENDIDA
              </th>
              <th class="center">
                <p><b>PRIMER CUATRIMESTRE</b></p>
              </th>
            </tr>
            <tr class="heading">
              <th class="text-single-line darker center">
                <b>${tipoPeriodo}</b>
              </th>
            </tr>
          `
                : ''}
          <tr>
            <td class="center">${je.jemateria.descripcion}</td>
            <td class="medium-column center">Calificación</td>
          </tr>
        </thead>
        <tbody>
          ${jeIndicadores
                .filter((indi) => indi.jeMateria.idJEMateria === je.jemateria.idJEMateria &&
                je.data !== null &&
                indi.key in je.data)
                .map((indicador) => `
              <tr>
                <td class="to-left">${indicador.pregunta}</td>
                <td class="center">${je.data ? je.data[indicador.key] : ''}</td>
              </tr>
          `)}
            <tr>
              <td class="to-left">Observaciones</td>
              <td class="center">${(_b = (_a = je.data) === null || _a === void 0 ? void 0 : _a.observaciones) !== null && _b !== void 0 ? _b : ''}</td>
            </tr>
        </tbody>
      </table>
  `;
            html += template;
        }
    });
    html += '</div>';
    return html;
};
//# sourceMappingURL=getJornadaExtendidaTable.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FechaBloqueadaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class FechaBloqueadaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.FechaBloqueadaDAO.entity.toLowerCase(), '/fechas_bloqueadas', dao_1.FechaBloqueadaDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.FechaBloqueadaEndpoint = FechaBloqueadaEndpoint;
//# sourceMappingURL=FechaBloqueadaEndpoint.js.map
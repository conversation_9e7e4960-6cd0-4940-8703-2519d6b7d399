"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewJoiErrorBuilder = void 0;
const joi_1 = require("joi");
class NewJoiErrorBuilder {
    constructor(personaAltaAlumno) {
        this.result = {
            value: [personaAltaAlumno],
            error: {},
        };
    }
    withInvalidData() {
        this.result = {
            value: this.result.value,
            error: {
                name: 'ValidationError',
                isJoi: true,
                message: 'Validation failed',
                details: [
                    {
                        message: 'El CUE Anexo debe tener un valor mayor a 10000000',
                        path: [0, 'CUEAnexo'],
                        type: 'number.min',
                        context: {
                            limit: 10000000,
                            value: 1234,
                            label: 'CUEAnexo',
                            key: 'CUEAnexo',
                        },
                    },
                    {
                        message: 'El Nombre es obligatorio.',
                        path: [0, 'Nombre'],
                        type: 'any.required',
                        context: {
                            label: 'Nombre',
                            key: 'Nombre',
                        },
                    },
                    {
                        message: 'La fecha de nacimiento no puede ser anterior al 1 de enero de 1930',
                        path: [0, 'FechaNacimiento'],
                        type: 'date.min',
                        context: {
                            limit: new Date('1930-01-01'),
                            label: 'FechaNacimiento',
                            key: 'FechaNacimiento',
                        },
                    },
                    {
                        message: 'El DNI debe ser solo numerico',
                        path: [0, 'Documento'],
                        type: 'number.base',
                        context: {
                            label: 'Documento',
                            key: 'Documento',
                        },
                    },
                    {
                        message: 'El Ciclo Lectivo debe ser mayor a 2024',
                        path: [0, 'CicloLectivo'],
                        type: 'number.min',
                        context: {
                            limit: 2024,
                            value: 2023,
                            label: 'CicloLectivo',
                            key: 'CicloLectivo',
                        },
                    },
                ],
                _original: [],
                annotate: () => '',
            },
        };
        return this;
    }
    withValidData() {
        this.result = {
            value: this.result.value,
            error: undefined,
        };
        return this;
    }
    withZeroErrors() {
        this.result = {
            value: this.result.value,
            error: new joi_1.ValidationError('', [], []),
        };
        return this;
    }
    build() {
        return this.result;
    }
}
exports.NewJoiErrorBuilder = NewJoiErrorBuilder;
//# sourceMappingURL=NewJoiErrorBuilder.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanificacionesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PlanificacionesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PlanificacionesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PlanificacionesDAO.entity.toLowerCase(), dao_1.PlanificacionesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PlanificacionesEndpoint = PlanificacionesEndpoint;
//# sourceMappingURL=PlanificacionesEndpoint.js.map
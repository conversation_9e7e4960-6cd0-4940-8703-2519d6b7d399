"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoPeriodoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class TipoPeriodoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.TipoPeriodoDAO.entity.toLowerCase(), '/public/' + dao_1.TipoPeriodoDAO.entity.toLowerCase(), dao_1.TipoPeriodoDAO);
    }
    getAllowGuest() {
        return true;
    }
}
exports.TipoPeriodoEndpoint = TipoPeriodoEndpoint;
//# sourceMappingURL=TipoPeriodoEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropositosBloquesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.PropositosBloquesDAO;
class PropositosBloquesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.propositosbloques', '/orientaciones/propositosbloques', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PropositosBloquesEndpoint = PropositosBloquesEndpoint;
//# sourceMappingURL=PropositosBloquesEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EspacioCurricularSeccionNoTraverseEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const Log = new chino_sdk_1.ChinoLogManager('EspacioCurricularSeccionNoTraverseEndpoint Logging');
class EspacioCurricularSeccionNoTraverseEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.EspacioCurricularSeccionNoTraverseDAO.entity.toLowerCase(), '/public/nt/' +
            dao_1.EspacioCurricularSeccionNoTraverseDAO.entity.toLowerCase(), dao_1.EspacioCurricularSeccionNoTraverseDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.EspacioCurricularSeccionNoTraverseEndpoint = EspacioCurricularSeccionNoTraverseEndpoint;
//# sourceMappingURL=EspacioCurricularSeccionNoTraverseEndpoint.js.map
export interface PersonaCustom {
    idPersona: string;
    nombre: string;
    apellido: string;
    fechaNacimiento: string;
    estadoCivil: string;
    documento: string;
    pbIndigenaHabla: string;
    obsIndigena: string;
    pbIndigenaPertenece: string;
    ultimaActualizacion: Date;
    email?: string;
    genero: number | null;
    grupoSanguineo: number | null;
    nacionalidad: number | null;
    paisNacimiento: number | null;
    tipoDocumento: number | null;
    cuentas: number | null;
    provinciaNacimiento: number | null;
    cuil: string;
}

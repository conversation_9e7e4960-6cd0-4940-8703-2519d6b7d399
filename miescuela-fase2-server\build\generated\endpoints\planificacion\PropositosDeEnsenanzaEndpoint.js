"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropositosDeEnsenanzaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PropositosDeEnsenanzaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PropositosDeEnsenanzaDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PropositosDeEnsenanzaDAO.entity.toLowerCase(), dao_1.PropositosDeEnsenanzaDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PropositosDeEnsenanzaEndpoint = PropositosDeEnsenanzaEndpoint;
//# sourceMappingURL=PropositosDeEnsenanzaEndpoint.js.map
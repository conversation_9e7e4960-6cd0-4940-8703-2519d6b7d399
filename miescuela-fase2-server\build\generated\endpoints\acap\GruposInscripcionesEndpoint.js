"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GruposInscripcionesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const helpers_1 = require("../../../utils/helpers");
class GruposInscripcionesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.gruposinscripciones', '/acap/gruposinscripciones', dao_1.GruposInscripcionesDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.GruposInscripcionesEndpoint = GruposInscripcionesEndpoint;
//# sourceMappingURL=GruposInscripcionesEndpoint.js.map
import { ChinoContext, ChinoSessionDTO, EntityManager, ObjectType } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpoint } from '../../app/config/endpoint/MiEscuelaEndpoints';
import { Alumno, AlumnoMovimiento, Calificacion, CalificacionesAsistenciaEc, CalificacionesSecundarioAnual, Cuentas, EspacioCurricularSeccion, IntencionCalificador, Periodo, Secundario, SecundarioData } from '../orm/entities';
interface QueryParams {
    periodo?: number;
    nota?: number;
    seccion?: number;
    close: '1' | '0';
}
export declare class BulkGeneratingCalificacionesEndpoint extends MiEscuelaEndpoint<Secundario> {
    private params;
    constructor();
    getNotasCalificaciones(periodo: number, seccion: number, matriculas: AlumnoMovimiento[], tx: EntityManager, ctx: ChinoContext): Promise<Calificacion[]>;
    getNotasSecundario(periodo: number, seccion: number, matriculas: AlumnoMovimiento[], tx: EntityManager, ctx: ChinoContext): Promise<Secundario[]>;
    saveIntencion(intencion: IntencionCalificador, tx: EntityManager, ctx: ChinoContext): Promise<void>;
    validateParams(params: QueryParams): Promise<void>;
    getMetadata(seccion: number, periodo: number, tx: EntityManager, ctx: ChinoContext): Promise<{
        matriculas: AlumnoMovimiento[];
        ecs: EspacioCurricularSeccion[];
        asistencias: CalificacionesAsistenciaEc[];
    }>;
    getSecundarioAnual(seccion: number, ctx: ChinoContext, tx: EntityManager): Promise<CalificacionesSecundarioAnual[]>;
    obtenerCalificacionesNuevas(matriculas: AlumnoMovimiento[], periodo: number, calificaciones: Calificacion[], tx: EntityManager, ctx: ChinoContext): Promise<Calificacion[]>;
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<any>;
    formatSecundarioCalificacionToSave(secundario: Secundario): {
        calificacion: {
            periodo: Periodo;
            alumno: Alumno;
            idCalificacion?: number | undefined;
        };
        alumno: number;
        idConocimiento: number;
        data: SecundarioData | null;
        abierto: boolean;
        nota: string;
        aprobado: boolean;
        espacioCurricularSeccion: EspacioCurricularSeccion;
        createdAt: Date;
        createdBy: Cuentas;
        updatedBy: Cuentas;
        updatedAt: Date | null;
        asistenciaEc?: boolean | undefined;
    };
    postInsertAction(value: Secundario | ObjectType<Secundario> | Secundario[], session: ChinoSessionDTO, req: JRRequest, tx: EntityManager): Promise<any>;
    getAllowGuest(): boolean;
}
export {};

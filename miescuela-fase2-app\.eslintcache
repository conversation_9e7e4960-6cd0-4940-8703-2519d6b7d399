[{"C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\Registered\\Initial\\hooks\\useMatriculacionColumns.tsx": "1", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\passManagement\\components\\GestionModalInicial\\GestionModalInicial.tsx": "2", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\inicial\\tabs\\promocion\\components\\PromocionActions.tsx": "3", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\components\\PromocionActions.tsx": "4", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\utils\\lowEnvironmentHelpers.ts": "5", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\inicial\\tabs\\secciones\\components\\SeccionModal\\index.tsx": "6", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\inicial\\tabs\\secciones\\components\\SeccionesFilterTable.tsx": "7", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionPage.tsx": "8", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\components\\OcupadoProyectadoTooltip.tsx": "9", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\helpers\\useProyeccionButtonStatesPrimaria.ts": "10", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\hooks\\useFinalizarProyeccion.ts": "11", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\utils\\constants.ts": "12", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\types\\types.ts": "13", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\secundaria\\tabs\\promocion\\seccion\\hooks\\useAlumnoRow\\useAlumnoRow.ts": "14", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\secundaria\\tabs\\promocion\\seccion\\utils\\alumnoTable.ts": "15", "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\secundaria\\tabs\\promocion\\seccion\\utils\\reglasSecundariaHelper.ts": "16"}, {"size": 22341, "mtime": 1761593962491, "results": "17", "hashOfConfig": "18"}, {"size": 12857, "mtime": 1761593628705}, {"size": 4180, "mtime": 1762360422093, "results": "19", "hashOfConfig": "18"}, {"size": 5616, "mtime": 1762261376538, "results": "20", "hashOfConfig": "18"}, {"size": 2510, "mtime": 1761651280172, "results": "21", "hashOfConfig": "18"}, {"size": 19272, "mtime": 1762179207720, "results": "22", "hashOfConfig": "18"}, {"size": 4921, "mtime": 1762179166488, "results": "23", "hashOfConfig": "18"}, {"size": 2505, "mtime": 1762183052024, "results": "24", "hashOfConfig": "18"}, {"size": 1824, "mtime": 1762179458085, "results": "25", "hashOfConfig": "18"}, {"size": 6698, "mtime": 1762268450542, "results": "26", "hashOfConfig": "18"}, {"size": 4201, "mtime": 1762261380727, "results": "27", "hashOfConfig": "18"}, {"size": 4431, "mtime": 1762261387078, "results": "28", "hashOfConfig": "18"}, {"size": 3766, "mtime": 1762268405235, "results": "29", "hashOfConfig": "18"}, {"size": 15335, "mtime": 1762369380531, "results": "30", "hashOfConfig": "18"}, {"size": 10395, "mtime": 1762373141939, "results": "31", "hashOfConfig": "18"}, {"size": 10973, "mtime": 1762369356036, "results": "32", "hashOfConfig": "18"}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yt467o", {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\Registered\\Initial\\hooks\\useMatriculacionColumns.tsx", [], ["78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89"], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\inicial\\tabs\\promocion\\components\\PromocionActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\components\\PromocionActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\utils\\lowEnvironmentHelpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\inicial\\tabs\\secciones\\components\\SeccionModal\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\inicial\\tabs\\secciones\\components\\SeccionesFilterTable.tsx", ["90"], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\components\\OcupadoProyectadoTooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\helpers\\useProyeccionButtonStatesPrimaria.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\hooks\\useFinalizarProyeccion.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\utils\\constants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\primaria\\tabs\\promocion\\types\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\secundaria\\tabs\\promocion\\seccion\\hooks\\useAlumnoRow\\useAlumnoRow.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\secundaria\\tabs\\promocion\\seccion\\utils\\alumnoTable.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AprendeBA\\miescuela-fase2-app\\src\\pagesV2\\matriculacion\\MatriculacionRefactor\\secundaria\\tabs\\promocion\\seccion\\utils\\reglasSecundariaHelper.ts", [], [], {"ruleId": "91", "severity": 2, "message": "92", "line": 3, "column": 1, "nodeType": "93", "messageId": "94", "endLine": 3, "endColumn": 15, "suppressions": "95"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 85, "column": 67, "nodeType": "98", "messageId": "99", "endLine": 85, "endColumn": 70, "suggestions": "100", "suppressions": "101"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 264, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 264, "endColumn": 24, "suggestions": "102", "suppressions": "103"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 291, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 291, "endColumn": 24, "suggestions": "104", "suppressions": "105"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 298, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 298, "endColumn": 24, "suggestions": "106", "suppressions": "107"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 414, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 414, "endColumn": 24, "suggestions": "108", "suppressions": "109"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 421, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 421, "endColumn": 24, "suggestions": "110", "suppressions": "111"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 428, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 428, "endColumn": 24, "suggestions": "112", "suppressions": "113"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 435, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 435, "endColumn": 24, "suggestions": "114", "suppressions": "115"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 443, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 443, "endColumn": 24, "suggestions": "116", "suppressions": "117"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 444, "column": 52, "nodeType": "98", "messageId": "99", "endLine": 444, "endColumn": 55, "suggestions": "118", "suppressions": "119"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 515, "column": 21, "nodeType": "98", "messageId": "99", "endLine": 515, "endColumn": 24, "suggestions": "120", "suppressions": "121"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 70, "column": 5, "nodeType": "124", "endLine": 70, "endColumn": 7, "suggestions": "125"}, "@typescript-eslint/ban-ts-comment", "Do not use \"@ts-nocheck\" because it alters compilation errors.", "Line", "tsDirectiveComment", ["126"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["127", "128"], ["129"], ["130", "131"], ["132"], ["133", "134"], ["135"], ["136", "137"], ["138"], ["139", "140"], ["141"], ["142", "143"], ["144"], ["145", "146"], ["147"], ["148", "149"], ["150"], ["151", "152"], ["153"], ["154", "155"], ["156"], ["157", "158"], ["159"], "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'anioSum'. Either include it or remove the dependency array.", "ArrayExpression", ["160"], {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "164", "desc": "165"}, {"messageId": "166", "fix": "167", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "169", "desc": "165"}, {"messageId": "166", "fix": "170", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "171", "desc": "165"}, {"messageId": "166", "fix": "172", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "173", "desc": "165"}, {"messageId": "166", "fix": "174", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "175", "desc": "165"}, {"messageId": "166", "fix": "176", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "177", "desc": "165"}, {"messageId": "166", "fix": "178", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "179", "desc": "165"}, {"messageId": "166", "fix": "180", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "181", "desc": "165"}, {"messageId": "166", "fix": "182", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "183", "desc": "165"}, {"messageId": "166", "fix": "184", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "185", "desc": "165"}, {"messageId": "166", "fix": "186", "desc": "168"}, {"kind": "161", "justification": "162"}, {"messageId": "163", "fix": "187", "desc": "165"}, {"messageId": "166", "fix": "188", "desc": "168"}, {"kind": "161", "justification": "162"}, {"desc": "189", "fix": "190"}, "directive", "", "suggestUnknown", {"range": "191", "text": "192"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "193", "text": "194"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "195", "text": "192"}, {"range": "196", "text": "194"}, {"range": "197", "text": "192"}, {"range": "198", "text": "194"}, {"range": "199", "text": "192"}, {"range": "200", "text": "194"}, {"range": "201", "text": "192"}, {"range": "202", "text": "194"}, {"range": "203", "text": "192"}, {"range": "204", "text": "194"}, {"range": "205", "text": "192"}, {"range": "206", "text": "194"}, {"range": "207", "text": "192"}, {"range": "208", "text": "194"}, {"range": "209", "text": "192"}, {"range": "210", "text": "194"}, {"range": "211", "text": "192"}, {"range": "212", "text": "194"}, {"range": "213", "text": "192"}, {"range": "214", "text": "194"}, "Update the dependencies array to be: [anioSum]", {"range": "215", "text": "216"}, [3427, 3430], "unknown", [3427, 3430], "never", [8740, 8743], [8740, 8743], [9598, 9601], [9598, 9601], [9867, 9870], [9867, 9870], [14433, 14436], [14433, 14436], [14673, 14676], [14673, 14676], [14894, 14897], [14894, 14897], [15102, 15105], [15102, 15105], [15338, 15341], [15338, 15341], [15423, 15426], [15423, 15426], [18491, 18494], [18491, 18494], [1690, 1692], "[anioSum]"]
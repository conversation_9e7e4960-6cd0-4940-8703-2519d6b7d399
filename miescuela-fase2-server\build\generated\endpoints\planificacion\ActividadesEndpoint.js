"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActividadesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ActividadesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ActividadesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.ActividadesDAO.entity.toLowerCase(), dao_1.ActividadesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ActividadesEndpoint = ActividadesEndpoint;
//# sourceMappingURL=ActividadesEndpoint.js.map
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSeccionSecundaria } from '../useSeccionSecundaria'
import { type AlumnoTableRow } from '../../../../promocion/types/types'
import { getAlumnoRows, getInitialValues } from '../../utils/alumnoTable'
import { useAlumnoTable } from '../useAlumnoTable'
import { usePromocionSecundariaContext } from '../../../../promocion/contexts/usePromocionSecundariaContext'
import { PROYECCION_ESTADOS, PROYECCION_ESTADO_TIPO } from '@miesc/const/proyeccionEstadoTipo'
import { filterAlumnosBySearch } from '../../utils/alumnoFilterHelpers'
import { useAcademicDebtData } from '../../../hooks/useAcademicDebtData'

export default function useSeccionSecundariaDetalle() {
  const {
    filters: { selectedSeccion, cicloLectivo },
    combinacionesPase,
    isLoadingCombinacionesPase: isLoadingCombinaciones,
    proyecciones,
    proyeccionesLoading,
    setAlumnosSelectedIds,
    refetchProyecciones,
    proyeccionEstado
  } = usePromocionSecundariaContext()

  const combinacionesParaSeccion = useMemo(() => {
    return combinacionesPase.filter(
      (combinacion) => combinacion.anio.idAnio === selectedSeccion?.anio.idAnio
    )
  }, [combinacionesPase, selectedSeccion])

  const { alumnos, isLoadingAlumnos } = useSeccionSecundaria({
    selectedSeccion
  })

  const {
    pendientesCicloAnterioresMap,
    pendientesCicloActualMap,
    isLoadingPendientesCicloAnteriores,
    isLoadingPendientesCicloActual
  } = useAcademicDebtData({
    selectedSeccion,
    cicloLectivo,
    alumnos,
    isLoadingAlumnos
  })

  const showProyectadaAlert = useMemo(
    () =>
      !!selectedSeccion?.proyeccionEstado &&
      selectedSeccion?.proyeccionEstado === PROYECCION_ESTADOS.PROYECTADA,
    [selectedSeccion]
  )
  const alumnosRows: AlumnoTableRow[] = useMemo(
    () => getAlumnoRows(alumnos),
    [alumnos]
  )

  const [alumnosProyectadosToSave, setAlumnosProyectadosToSave] = useState<
  AlumnoTableRow[] | null
  >(null)
  const [hasChanges, setHasChanges] = useState<boolean>(false)

  const { filteredRows, searchFilters, selectionState, updateSearchTerm } =
    useAlumnoTable(alumnosRows)

  const proyeccionesSeccion = useMemo(() => {
    return proyecciones.filter(
      (proyeccion) =>
        proyeccion.seccionOrigen.idSeccion === selectedSeccion?.idSeccion
    )
  }, [proyecciones, selectedSeccion?.idSeccion])

  const filteredAlumnosProyectados = useMemo(() => {
    if (!alumnosProyectadosToSave) return null
    return filterAlumnosBySearch(
      alumnosProyectadosToSave,
      searchFilters.searchTerm
    )
  }, [alumnosProyectadosToSave, searchFilters.searchTerm])

  const shouldSkip = useMemo(
    () => () => {
      return (
        proyeccionesLoading ||
        isLoadingCombinaciones ||
        isLoadingAlumnos ||
        isLoadingPendientesCicloAnteriores ||
        isLoadingPendientesCicloActual ||
        !combinacionesParaSeccion?.length ||
        !filteredRows?.length ||
        !!alumnosProyectadosToSave
      )
    },
    [
      proyeccionesLoading,
      isLoadingCombinaciones,
      isLoadingAlumnos,
      isLoadingPendientesCicloAnteriores,
      isLoadingPendientesCicloActual,
      combinacionesParaSeccion,
      filteredRows,
      alumnosProyectadosToSave
    ]
  )

  const esSegundoPaso = useMemo(() => {
    if (!proyeccionEstado || proyeccionEstado.length === 0) return false
    return (
      proyeccionEstado[0]?.proyeccionEstadoTipo?.idProyeccionEstadoTipo ===
        PROYECCION_ESTADO_TIPO.VACANTES_INFORMADAS ||
      proyeccionEstado[0]?.proyeccionEstadoTipo?.idProyeccionEstadoTipo ===
        PROYECCION_ESTADO_TIPO.PUEDE_EFECTIVIZAR
    )
  }, [proyeccionEstado])

  useEffect(() => {
    if (shouldSkip()) return

    const initialValues = getInitialValues({
      alumnosRows: filteredRows,
      proyecciones: proyeccionesSeccion,
      combinacionesPase: combinacionesParaSeccion,
      pendientesCicloAnterioresMap,
      pendientesCicloActualMap,
      esSegundoPaso,
      seccionSeleccionada: selectedSeccion!
    })

    setAlumnosProyectadosToSave(initialValues)
  }, [
    alumnosProyectadosToSave,
    filteredRows,
    combinacionesParaSeccion,
    proyeccionesLoading,
    isLoadingCombinaciones,
    isLoadingAlumnos,
    isLoadingPendientesCicloAnteriores,
    isLoadingPendientesCicloActual,
    proyeccionesSeccion,
    selectedSeccion,
    pendientesCicloAnterioresMap,
    pendientesCicloActualMap,
    esSegundoPaso,
    shouldSkip
  ])

  const handleResetValues = () => {
    setAlumnosProyectadosToSave(null)
    setHasChanges(false)
  }

  const updateAlumnosProyectadosToSave = useCallback(
    (alumnosIds: string[], updates: Partial<AlumnoTableRow>, setHasChangesValue?: boolean) => {
      setHasChanges(setHasChangesValue ?? true)
      setAlumnosProyectadosToSave((prev) => {
        if (!prev) return prev

        const alumnosToUpdate = prev.filter((alumnoProyectado) =>
          alumnosIds.includes(alumnoProyectado.alumnoId)
        )
        if (!alumnosToUpdate?.length) return prev

        return prev.map((alumno) =>
          !alumnosIds.includes(alumno.alumnoId)
            ? alumno
            : { ...alumno, ...updates }
        )
      })
    },
    [setHasChanges, setAlumnosProyectadosToSave]
  )

  const resetProyecciones = () => {
    refetchProyecciones()
    setHasChanges(false)
  }

  const isLoadingComplete =
    isLoadingAlumnos ||
    proyeccionesLoading ||
    isLoadingCombinaciones ||
    isLoadingPendientesCicloAnteriores ||
    isLoadingPendientesCicloActual ||
    !filteredRows

  useEffect(() => {
    setAlumnosSelectedIds(null)
  }, [setAlumnosSelectedIds])

  return {
    alumnosProyectadosToSave,
    filteredRows,
    filteredAlumnosProyectados,
    hasChanges,
    isLoadingComplete,
    showProyectadaAlert,
    proyecciones,
    isLoadingProyeccion: proyeccionesLoading,
    combinacionesPase: combinacionesParaSeccion,
    selectionState,
    searchFilters,
    selectedSeccion,
    setAlumnosSelectedIds,
    updateAlumnosProyectadosToSave,
    resetProyecciones,
    handleResetValues,
    updateSearchTerm
  }
}

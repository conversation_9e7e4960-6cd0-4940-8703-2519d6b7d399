"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const calificaciones_1 = require("../../orm/dao/calificaciones");
class DocumentoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(calificaciones_1.DocumentoDAO.entity.toLowerCase(), '/calificaciones/documentos', calificaciones_1.DocumentoDAO);
    }
    getAllowGuest() {
        return true;
    }
}
exports.DocumentoEndpoint = DocumentoEndpoint;
//# sourceMappingURL=DocumentoEndpoint.js.map
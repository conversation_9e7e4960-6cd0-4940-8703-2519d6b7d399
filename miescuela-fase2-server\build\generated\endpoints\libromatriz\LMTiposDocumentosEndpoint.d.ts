import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { TipoDocumento } from '../../orm/entities';
export declare class LMTipoDocumentoEndpoint extends MiEscuelaEndpoint<TipoDocumento> {
    constructor();
    postSelectAction(values: TipoDocumento[], session: ChinoSessionDTO, req: JRRequest): Promise<any>;
    getAllowGuest(): boolean;
}

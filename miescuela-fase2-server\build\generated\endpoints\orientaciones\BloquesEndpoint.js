"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BloquesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.BloquesDAO;
class BloquesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.bloques', '/orientaciones/bloques', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.BloquesEndpoint = BloquesEndpoint;
//# sourceMappingURL=BloquesEndpoint.js.map
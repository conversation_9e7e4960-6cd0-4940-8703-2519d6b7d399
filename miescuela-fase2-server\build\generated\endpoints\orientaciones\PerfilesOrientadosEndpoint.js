"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerfilesOrientadosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.PerfilesOrientadosDAO;
class PerfilesOrientadosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.perfilesorientados', '/orientaciones/perfilesorientados', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PerfilesOrientadosEndpoint = PerfilesOrientadosEndpoint;
//# sourceMappingURL=PerfilesOrientadosEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoPlanificacionEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class TipoPlanificacionEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.TipoPlanificacionDAO.entity.toLowerCase(), '/planificacion/' + dao_1.TipoPlanificacionDAO.entity.toLowerCase(), dao_1.TipoPlanificacionDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.TipoPlanificacionEndpoint = TipoPlanificacionEndpoint;
//# sourceMappingURL=TipoPlanificacionEndpoint.js.map
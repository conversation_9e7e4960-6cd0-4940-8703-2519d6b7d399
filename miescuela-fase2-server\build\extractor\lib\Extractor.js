"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Extractor = void 0;
const database_1 = require("./commons/database");
const commons_1 = require("./commons");
class Extractor {
    constructor({ source, target, extractors, insertors }) {
        this.log = new commons_1.LogManager('Extractor');
        this.source = new database_1.DB(source);
        this.target = new database_1.DB(target);
        this.extractors = extractors;
        this.insertors = insertors;
    }
    async run() {
        let context = {};
        for (const extractor of this.extractors) {
            context = await extractor.run(context, this.source);
        }
        console.log('LISTO EXTRACTOR');
        const targetConnection = await this.target.getConnection();
        await targetConnection.query('BEGIN TRANSACTION;');
        let targetContext = {};
        try {
            for (const insertor of this.insertors) {
                targetContext = await insertor.run(context, targetConnection, targetContext);
            }
            await targetConnection.query('COMMIT;');
            return { source: context, target: targetContext };
        }
        catch (e) {
            this.log.error(e.message);
            console.log(e);
            await targetConnection.query('ROLLBACK;');
        }
    }
}
exports.Extractor = Extractor;
//# sourceMappingURL=Extractor.js.map
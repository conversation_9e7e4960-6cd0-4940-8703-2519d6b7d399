"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfertasGruposEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const helpers_1 = require("../../../utils/helpers");
class OfertasGruposEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.ofertasgrupos', '/acap/ofertasgrupos', dao_1.OfertasGruposDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.OfertasGruposEndpoint = OfertasGruposEndpoint;
//# sourceMappingURL=OfertasGruposEndpoint.js.map
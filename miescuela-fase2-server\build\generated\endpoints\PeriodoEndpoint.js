"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeriodoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class PeriodoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PeriodoDAO.entity.toLowerCase(), '/public/' + dao_1.PeriodoDAO.entity.toLowerCase(), dao_1.PeriodoDAO);
    }
    async postSelectAction(values, session, req) {
        // Si no tiene e aspect, excluímos por defecto los periodos
        // historicos, sino, los dejamos
        if (req.headers['x-chino-aspect'] !== 'include-history') {
            values = values.filter((periodo) => !periodo.mesInicio ||
                (periodo.mesInicio &&
                    new Date(periodo.mesInicio).toString() !==
                        new Date(2015, 0, 1).toString()));
        }
        return values;
    }
    getAllowGuest() {
        return true;
    }
}
exports.PeriodoEndpoint = PeriodoEndpoint;
//# sourceMappingURL=PeriodoEndpoint.js.map
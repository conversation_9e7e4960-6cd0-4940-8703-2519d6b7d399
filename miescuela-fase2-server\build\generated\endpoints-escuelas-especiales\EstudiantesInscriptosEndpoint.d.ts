import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEspecialesEndpoint } from '../../app/config/endpoint/MiEscuelaEspecialesEndpoint';
import { InscripcionAlumno } from '../orm/entities';
export declare class EstudiantesInscriptosEndpoint extends MiEscuelaEspecialesEndpoint<InscripcionAlumno> {
    constructor();
    getAllowGuest(): boolean;
    postSelectAction(values: InscripcionAlumno[], session: ChinoSessionDTO, req: JRRequest): Promise<InscripcionAlumno[]>;
    catch(error: any): void;
    preSelectAction(req: JRRequest, session: ChinoSessionDTO): Promise<any>;
    private formatResponse;
}

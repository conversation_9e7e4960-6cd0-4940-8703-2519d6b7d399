"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EspacioCurricularCustomEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const dao_1 = require("../../orm/dao");
class EspacioCurricularCustomEndpoint extends chino_sdk_1.ChinoFindEndpoint {
    constructor() {
        super(dao_1.EspacioCurricularCustomDAO.entity.toLowerCase(), `/custom/${dao_1.EspacioCurricularCustomDAO.entity.toLowerCase()}`, dao_1.EspacioCurricularCustomDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.EspacioCurricularCustomEndpoint = EspacioCurricularCustomEndpoint;
//# sourceMappingURL=EspacioCurricularEndpointCustom.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GruposInscripcionesOfertaEndpointV2 = void 0;
const MiEscuelaEndpointV2_1 = require("../../../app/config/endpoint/MiEscuelaEndpointV2");
const dao_1 = require("../../orm/dao");
class GruposInscripcionesOfertaEndpointV2 extends MiEscuelaEndpointV2_1.MiEscuelaEndpointV2 {
    constructor() {
        super('acap.gruposinscripcionesofertas', '/acap/gruposinscripciones_ofertas_v2', dao_1.GruposInscripcionesOfertaV2DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.GruposInscripcionesOfertaEndpointV2 = GruposInscripcionesOfertaEndpointV2;
//# sourceMappingURL=GruposInscripcionesOfertaEndpointV2.js.map
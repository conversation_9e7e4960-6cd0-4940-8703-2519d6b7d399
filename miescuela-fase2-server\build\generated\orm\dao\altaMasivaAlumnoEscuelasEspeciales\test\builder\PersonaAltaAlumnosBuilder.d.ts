import { PersonaAltaAlumnos } from 'generated/orm/entities';
export declare class PersonaAltaAlumnosBuilder {
    private persona;
    constructor();
    withCUEAnexo(cueAnexo: number): this;
    withNombre(nombre: string): this;
    withApellido(apellido: string): this;
    withPais(pais: string): this;
    withGenero(genero: string): this;
    withFechaNacimiento(fecha: string): this;
    withTipoDocumento(tipo: string): this;
    withDocumento(documento: string): this;
    withCicloLectivo(ciclo: string): this;
    withTurno(turno: string): this;
    withNivel(nivel: string): this;
    withAnio(anio: string): this;
    withObservacionesError(): this;
    build(): PersonaAltaAlumnos;
}

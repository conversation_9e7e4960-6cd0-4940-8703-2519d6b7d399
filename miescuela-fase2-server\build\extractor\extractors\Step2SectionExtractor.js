"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Step2SectionExtractor = void 0;
const utils_1 = require("../utils");
const tables = {
    section: 'seccion',
    location: 'localizacion',
    address: 'domicilio',
    province: 'provincia',
    turn: 'turno',
    plan_study: 'planestudio',
    orientation: 'orientacion',
    cycle_school: 'ciclolectivo',
    status_cycle_school: 'estadociclolectivo',
    educational_unit: 'unidadeducativa',
    level: 'nivel',
};
class Step2SectionExtractor {
    async run(context, source) {
        for (const [key, table] of Object.entries(tables)) {
            context[key] = await (0, utils_1.getFromTable)(table, source);
        }
        return context;
    }
}
exports.Step2SectionExtractor = Step2SectionExtractor;
//# sourceMappingURL=Step2SectionExtractor.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ cicloLectivo, anio, tipoPeriodo, organizaTareas, expresaIdeas, asumeResponsabilidad, realizaTareas, manifiestaInteres, logrosDificultades, }) => `
<table class="tabla">
        <tr class="title-table">
            <td style="background-color: #e2eedb; font-weight: bold; font-size: 12px;">
                ${cicloLectivo} - ${anio}
            </td>
            <td style="background-color: #e2eedb; border-bottom: 0px; padding-top: 42px; font-weight: bold; font-size: 12px;">
                ${tipoPeriodo}
            </td>
        </tr>
        <tr>
            <div>
                <td style=" background-color: #ececec; font-weight: bold;">QUEHACERES DEL/A ESTUDIANTE</td>
            </div>
            <td style="background-color: #e2eedb;"></td>
        </tr>
        <tr>
            <td>Se organiza para realizar sus tareas en clase.</td>
            <td> ${organizaTareas}</td>
        </tr>
        <tr>
            <td>Expresa sus ideas en clase</td>
            <td> ${expresaIdeas}</td>
        </tr>
        <tr>
            <td>Asume con responsabilidad su participación en tareas de equipo</td>
            <td> ${asumeResponsabilidad}</td>
        </tr>
        <tr>
            <td>Realiza tareas por sí mismo/a.</td>
            <td> ${realizaTareas}</td>
        </tr>
        <tr>
            <td>Manifiesta interés por aprender y pregunta cuando no comprende</td>
            <td> ${manifiestaInteres}</td>
        </tr>
        <tr> 
            <td>Identifica lo aprendido: sus logros y sus dificultades.</td>
            <td> ${logrosDificultades}</td>
        </tr>
    </table>
`;
//# sourceMappingURL=getQuehaceresTable.js.map
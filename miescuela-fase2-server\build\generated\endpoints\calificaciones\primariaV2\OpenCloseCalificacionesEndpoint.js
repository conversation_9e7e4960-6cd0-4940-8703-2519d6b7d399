"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenCloseCalificacionesEndpoint = void 0;
const calificaciones_1 = require("../../../orm/dao/calificaciones");
const MiEscuelaEndpointV2_1 = require("../../../../app/config/endpoint/MiEscuelaEndpointV2");
class OpenCloseCalificacionesEndpoint extends MiEscuelaEndpointV2_1.MiEscuelaEndpointV2 {
    constructor() {
        super('open_close', '/v2/calificaciones/openclose', calificaciones_1.OpenCloseCalificacionesV2DAO);
    }
    getMethods() {
        return ['PUT'];
    }
    configure() {
        this.allowGuest = false;
    }
}
exports.OpenCloseCalificacionesEndpoint = OpenCloseCalificacionesEndpoint;
//# sourceMappingURL=OpenCloseCalificacionesEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EjesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.EjesDAO;
class EjesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.ejes', '/orientaciones/ejes', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.EjesEndpoint = EjesEndpoint;
//# sourceMappingURL=EjesEndpoint.js.map
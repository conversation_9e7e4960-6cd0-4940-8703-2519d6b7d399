import { An<PERSON> } from '../../Anio';
import { EstadoPaseAnio } from '../../EstadoPaseAnio';
import { EstadoProyeccionMotivo } from '../../proyecciones';
import { AccionPase } from './AccionPase';
export interface CombinacionesPase {
    idCombinacion: number;
    anio: Anio;
    estadoPaseAnio: EstadoPaseAnio;
    accionPase: AccionPase | null;
    estadoProyeccionMotivo: EstadoProyeccionMotivo | null;
    deshabilitadoCambioAccionPase?: boolean;
}

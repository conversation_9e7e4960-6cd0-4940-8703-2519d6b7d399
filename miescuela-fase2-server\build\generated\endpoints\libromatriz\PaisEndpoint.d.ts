import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { Pais } from '../../orm/entities';
export declare class PaisEndpoint extends MiEscuelaEndpoint<Pais> {
    constructor();
    postSelectAction(values: Pais[], session: ChinoSessionDTO, req: JRRequest): Promise<any>;
    getAllowGuest(): boolean;
}

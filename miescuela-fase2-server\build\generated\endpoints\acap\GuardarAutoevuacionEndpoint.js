"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuardarAutoevaluacionEndpoint = void 0;
const just_rpc_1 = require("@phinxlab/just-rpc");
const config_1 = require("../../../app/config");
const const_1 = require("../../../app/const");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const business_1 = require("../../../app/business");
class GuardarAutoevaluacionEndpoint extends config_1.MiEscuelaEndpointCustom {
    async exec(req, manager, res) {
        try {
            switch (req.method) {
                case 'GET':
                    const response = await (0, business_1.FlowManager)({
                        flow: const_1.FLOWS.GUARDAR_AUTOEVALUACION,
                        tx: manager,
                        req,
                    }).run();
                    return response;
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('error', e);
            // TODO NOT CHANGE, LIBBY NOT RECONGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(e.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
    configure() {
        this.allowGuest = false;
        this.addMethod(just_rpc_1.JRMethod.POST);
    }
    constructor(name = 'acap.guardar_autoevaluacion', path = '/acap/autoevaluacion_estudiante') {
        super(name, path);
    }
}
exports.GuardarAutoevaluacionEndpoint = GuardarAutoevaluacionEndpoint;
//# sourceMappingURL=GuardarAutoevuacionEndpoint.js.map
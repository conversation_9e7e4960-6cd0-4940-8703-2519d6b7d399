"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Step1EstablishmentExtractor = void 0;
const utils_1 = require("../utils");
const tables = {
    establishment: 'establecimiento',
    establishmentType: 'tipoestablecimiento',
    supervision: 'supervision',
    scholarDistrict: 'distritoescolar',
    functionalDependency: 'dependenciafuncional',
    modality: 'modalidad',
};
class Step1EstablishmentExtractor {
    async run(context, source) {
        for (const [key, table] of Object.entries(tables)) {
            context[key] = await (0, utils_1.getFromTable)(table, source);
        }
        return context;
    }
}
exports.Step1EstablishmentExtractor = Step1EstablishmentExtractor;
//# sourceMappingURL=Step1EstablishmentExtractor.js.map
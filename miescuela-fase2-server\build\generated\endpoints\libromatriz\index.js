"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaisLibroMatrizEndpoint = void 0;
var PaisEndpoint_1 = require("./PaisEndpoint");
Object.defineProperty(exports, "PaisLibroMatrizEndpoint", { enumerable: true, get: function () { return PaisEndpoint_1.PaisEndpoint; } });
__exportStar(require("./MetaFiltersEndpoint"), exports);
__exportStar(require("./DropdownEndpoint"), exports);
__exportStar(require("./LMTiposDocumentosEndpoint"), exports);
__exportStar(require("./CertificadoEndpoint"), exports);
__exportStar(require("./GenerosEndpoint"), exports);
__exportStar(require("./LMTiposDocumentosEndpoint"), exports);
__exportStar(require("./InfoAlumnoEndpoint"), exports);
__exportStar(require("./LibroMatriz"), exports);
__exportStar(require("./ObservacionesEndpoint"), exports);
__exportStar(require("./LibroMatriz"), exports);
__exportStar(require("./EstadoFolioEndpoint"), exports);
__exportStar(require("./FolioDigitalEndpoint"), exports);
//# sourceMappingURL=index.js.map
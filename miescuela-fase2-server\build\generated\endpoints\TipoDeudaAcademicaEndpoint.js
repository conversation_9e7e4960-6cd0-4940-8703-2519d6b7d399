"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoDeudaAcademicaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class TipoDeudaAcademicaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.TipoDeudaAcademicaDAO.entity.toLowerCase(), '/public/' + dao_1.TipoDeudaAcademicaDAO.entity.toLowerCase(), dao_1.TipoDeudaAcademicaDAO);
    }
    getMethods() {
        return ['GET', 'POST', 'PUT', 'DELETE'];
    }
    getAllowGuest() {
        return false;
    }
}
exports.TipoDeudaAcademicaEndpoint = TipoDeudaAcademicaEndpoint;
//# sourceMappingURL=TipoDeudaAcademicaEndpoint.js.map
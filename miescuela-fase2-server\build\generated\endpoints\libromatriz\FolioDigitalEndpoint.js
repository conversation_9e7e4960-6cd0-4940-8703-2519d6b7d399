"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FolioDigitalEndpoint = void 0;
const libromatriz_1 = require("../../orm/dao/libromatriz");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
class FolioDigitalEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(libromatriz_1.FolioDigitalDAO.entity.toLowerCase(), '/folio-digital', libromatriz_1.FolioDigitalDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.FolioDigitalEndpoint = FolioDigitalEndpoint;
//# sourceMappingURL=FolioDigitalEndpoint.js.map
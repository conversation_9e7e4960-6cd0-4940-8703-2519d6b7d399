"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LibroMatrizEndpoint = void 0;
const just_rpc_1 = require("@phinxlab/just-rpc");
const config_1 = require("../../../app/config");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const business_1 = require("../../../app/business");
const const_1 = require("../../../app/const");
class LibroMatrizEndpoint extends config_1.MiEscuelaEndpointCustom {
    constructor(name = 'lm_libro_matriz', path = '/libro-matriz') {
        super(name, path);
    }
    configure() {
        this.allowGuest = false;
        this.addMethod(just_rpc_1.JRMethod.GET);
    }
    async exec(req, tx) {
        try {
            switch (req.method) {
                case 'GET':
                    return await (0, business_1.FlowManager)({
                        flow: const_1.FLOWS.LIBRO_MATRIZ,
                        tx,
                        req,
                    }).run();
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('error', e);
            const err = e;
            // TODO NOT CHANGE, LIBBY NOT RECONGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(err.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
}
exports.LibroMatrizEndpoint = LibroMatrizEndpoint;
//# sourceMappingURL=LibroMatriz.js.map
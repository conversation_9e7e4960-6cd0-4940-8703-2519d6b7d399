"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAreaConocimientosTablesV2 = void 0;
const getAreaConocimientosTable = ({ columns, ciclo, anio, tipoPeriodo, espacioCurricular, calificacionGeneral, }) => {
    if (columns.length === 0)
        columns.push({
            name: '&nbsp;',
            value: '',
        });
    return `
<tr>
    <td colspan="6" class="inner-table">
        <table class="table-bimestres" style="margin-bottom: 29px" cellspacing="0" cellpadding="0">
            <thead>
                <tr>
                    <th class="cell bt-1 tac bl-1 br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold"
                        style="width: 680px" colspan="3">
                        ${ciclo} - ${anio}
                    </th>
                    <th class="cell bt-1 tac br-1 ff-calibri fs-11 va-middle ws-normal bb-1 bgc-gray bold" dir="ltr"
                        style="width:443px" rowspan="2" colspan="3">
                        ${tipoPeriodo}
                    </th>
                </tr>
                <tr>
                    <td class="cell va-middle bl-1 br-1 bold ff-arial fs-10 ws-normal tal bb-1 bgc-gray"
                        style="width:680px" colspan="3">
                        ${espacioCurricular}
                    </td>
                </tr>
            </thead>
            <tbody>
              ${columns
        .map((column) => {
        return `<tr>
                    <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                        ${column.name}
                    </td>
                    <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal" dir="ltr" colspan="3">
                        ${column.value} 
                    </td>
                </tr>`;
    })
        .join('\n')}
                <tr>
                    <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                        Calificación general
                    </td>
                    <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                        ${calificacionGeneral}
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>
<tr style="height: 29px">
    <td class="cell bl-1 br-1" colspan="6"></td>
</tr>
`;
};
const getAreaConocimientosTablesV2 = ({ ciclo, anio, tipoPeriodo, areasConocimientos, }) => areasConocimientos
    .map((conocimiento) => getAreaConocimientosTable({
    ...conocimiento,
    ciclo,
    anio,
    tipoPeriodo,
}))
    .join('\n');
exports.getAreaConocimientosTablesV2 = getAreaConocimientosTablesV2;
//# sourceMappingURL=getAreaConocimientosTablesV2.js.map
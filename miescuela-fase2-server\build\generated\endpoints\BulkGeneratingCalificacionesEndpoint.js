"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkGeneratingCalificacionesEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
const entities_1 = require("../orm/entities");
const dateUtils_1 = require("../../utils/dateUtils");
const Log = new chino_sdk_1.ChinoLogManager('BulkGeneratingCalificacionesEndpoint');
const DATA_TO_SAVE = {
    ppi: true,
    calificacionSegundoCuatrimestre: 0,
    calificacionAnual: '0',
    valoracion: '',
};
class BulkGeneratingCalificacionesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.SecundarioDAO.entity.toLowerCase(), '/public/automatizador/secundario', dao_1.SecundarioDAO);
    }
    async getNotasCalificaciones(periodo, seccion, matriculas, tx, ctx) {
        const calificaciones = await dao_1.CalificacionDAO.query('carga-masiva')
            .setContext(ctx)
            .in('alumno', matriculas.map((mat) => mat.alumno.idAlumno))
            .equals('periodo', periodo)
            .run(tx);
        return calificaciones;
    }
    async getNotasSecundario(periodo, seccion, matriculas, tx, ctx) {
        const notasSecundario = await dao_1.SecundarioDAO.query('carga-masiva')
            .setContext(ctx)
            .in('calificacion.alumno', matriculas.map((mat) => mat.alumno.idAlumno))
            .equals('calificacion.periodo', periodo)
            .equals('espacioCurricularSeccion.seccion', seccion)
            .run(tx);
        return notasSecundario;
    }
    async saveIntencion(intencion, tx, ctx) {
        await dao_1.IntencionCalificadorDAO.save(intencion, tx, {
            isInsert: true,
            context: ctx,
            aspect: 'carga-masiva',
        });
    }
    async validateParams(params) {
        if (!params.nota || !params.periodo || !params.seccion) {
            throw new Error('Faltan parametros');
        }
    }
    async getMetadata(seccion, periodo, tx, ctx) {
        const matriculasQuery = dao_1.AlumnoMovimientoDAO.query('carga-masiva').setContext(ctx);
        matriculasQuery.equals('seccion', seccion);
        const rowsEcsQuery = dao_1.EspacioCurricularSeccionDAO.query('carga-masiva').setContext(ctx);
        rowsEcsQuery.equals('seccion', seccion);
        const [matriculas, ecs] = await Promise.all([
            matriculasQuery.run(tx),
            rowsEcsQuery.run(tx),
        ]);
        let asistencias = [];
        if (matriculas.length === 0 || ecs.length === 0) {
            throw chino_sdk_1.ChinoError.CustomException('No hay matriculas o materias en esta sección', 403);
        }
        Log.info('matriculas', matriculas.length);
        Log.info('ecs', ecs.length);
        asistencias = await dao_1.CalificacionAsistenciaEcDAO.query('bulk-califications')
            .setContext(ctx)
            .in('alumnoMovimiento', matriculas.map((mat) => mat.idAlumnoMovimiento))
            .in('espacioCurricularSeccion', ecs.map((ec) => ec.idEspacioCurricularSeccion))
            .equals('periodo', periodo)
            .run(tx);
        Log.info('asistencias', asistencias.length);
        return {
            matriculas,
            ecs,
            asistencias,
        };
    }
    async getSecundarioAnual(seccion, ctx, tx) {
        return await dao_1.CalificacionesSecundariaAnualDAO.query('calificador')
            .setContext(ctx)
            .equals('espacioCurricularSeccion.seccion', seccion)
            .run(tx);
    }
    async obtenerCalificacionesNuevas(matriculas, periodo, calificaciones, tx, ctx) {
        const calificacionesToSave = matriculas
            .filter((matricula) => !calificaciones.some((c) => c.alumno.idAlumno === matricula.alumno.idAlumno &&
            c.periodo.idPeriodo === periodo))
            .map((matricula) => ({
            periodo: { idPeriodo: periodo },
            alumno: { idAlumno: matricula.alumno.idAlumno },
        }));
        if (calificacionesToSave.length === 0) {
            return [];
        }
        const created = (await dao_1.CalificacionDAO.save(calificacionesToSave, tx, {
            context: ctx,
            aspect: 'carga-masiva',
        }));
        return created;
    }
    async preInsertAction(req, session) {
        const ctx = chino_sdk_1.ChinoContext.fromSession(session);
        const tx = chino_sdk_1.ChinoManager.getManager();
        const date = dateUtils_1.dateUtils.toUTC(new Date());
        const periodo = req.body.periodo;
        const nota = req.body.nota;
        const close = req.body.close;
        const closeCalfs = close === '1';
        const seccion = req.body.seccion;
        this.params = {
            periodo,
            nota,
            close,
            seccion,
        };
        const user = session.user;
        try {
            await this.validateParams({
                periodo,
                nota: nota,
                seccion,
                close,
            });
        }
        catch (error) {
            await this.saveIntencion({
                seccion: {
                    idSeccion: Number(this.params.seccion),
                },
                periodo: { idPeriodo: Number(this.params.periodo) },
                nota,
                createdBy: { idUsuario: Number(user.id) },
                realizado: false,
                createdAt: date,
            }, tx, ctx);
            throw chino_sdk_1.ChinoError.CustomException(error.message, 400);
        }
        Log.info({
            periodo,
            calificacion: nota,
            seccion,
        });
        const { matriculas, ecs: rowsEcs, asistencias, } = await this.getMetadata(seccion, periodo, tx, ctx);
        const notasAnuales = await this.getSecundarioAnual(seccion, ctx, tx);
        const notasAnualesMap = new Map();
        const asistenciasMap = new Map();
        for (const asistencia of asistencias) {
            asistenciasMap.set(asistencia.alumnoMovimiento.idAlumnoMovimiento +
                '-' +
                asistencia.espacioCurricularSeccion.idEspacioCurricularSeccion, {
                ...asistencia,
                idCalificacionAsistenciaEc: Number(asistencia.idCalificacionAsistenciaEc),
                alumnoMovimiento: {
                    idAlumnoMovimiento: asistencia.alumnoMovimiento.idAlumnoMovimiento,
                },
                espacioCurricularSeccion: {
                    idEspacioCurricularSeccion: Number(asistencia.espacioCurricularSeccion.idEspacioCurricularSeccion),
                },
                periodo: {
                    idPeriodo: asistencia.periodo.idPeriodo,
                },
                updatedBy: {
                    idUsuario: Number(user.id),
                },
                createdBy: {
                    idUsuario: Number(user.id),
                },
            });
        }
        for (const nota of notasAnuales) {
            notasAnualesMap.set(nota.alumno.idAlumno +
                '-' +
                nota.espacioCurricularSeccion.idEspacioCurricularSeccion, nota);
        }
        Log.info('Total de alumnos para calificar', matriculas.length);
        Log.info('Total de materias para calificar', rowsEcs.length);
        if (matriculas.length === 0) {
            throw chino_sdk_1.ChinoError.CustomException('No hay matriculas en esta sección', 403);
        }
        if (rowsEcs.length === 0) {
            throw chino_sdk_1.ChinoError.CustomException('No hay materias en esta sección', 403);
        }
        const calificaciones = await this.getNotasCalificaciones(periodo, seccion, matriculas, tx, ctx);
        const calificacionesNuevas = await this.obtenerCalificacionesNuevas(matriculas, periodo, calificaciones, tx, ctx);
        const notasSecundario = await this.getNotasSecundario(periodo, seccion, matriculas, tx, ctx);
        // build calificaciones hash map
        const calificacionesAlumnoMap = new Map();
        for (const calf of [...calificaciones, ...calificacionesNuevas]) {
            calificacionesAlumnoMap.set(calf.alumno.idAlumno, calf);
        }
        const mapsRowsSecundario = new Map();
        for (const row of notasSecundario) {
            mapsRowsSecundario.set(row.calificacion.alumno.idAlumno +
                '-' +
                row.espacioCurricularSeccion.idEspacioCurricularSeccion, row);
        }
        const records = [];
        const anualesToSave = [];
        DATA_TO_SAVE.calificacionAnual = nota + '';
        DATA_TO_SAVE.calificacionSegundoCuatrimestre = nota + '';
        DATA_TO_SAVE.valoracion = 'Nota creada con calificador masivo';
        for (const matricula of matriculas) {
            const calificacion = calificacionesAlumnoMap.get(matricula.alumno.idAlumno);
            for (const ecs of rowsEcs) {
                const key = `${matricula.alumno.idAlumno}-${ecs.idEspacioCurricularSeccion}`;
                const asistencia = asistenciasMap.get(key);
                if (asistencia) {
                    asistencia.asistencia = true;
                }
                else {
                    const asistencia = new entities_1.CalificacionesAsistenciaEc();
                    asistencia.alumnoMovimiento = {
                        idAlumnoMovimiento: matricula.idAlumnoMovimiento,
                    };
                    asistencia.espacioCurricularSeccion = {
                        idEspacioCurricularSeccion: Number(ecs.idEspacioCurricularSeccion),
                    };
                    asistencia.asistencia = true;
                    asistencia.periodo = { idPeriodo: periodo };
                    asistencia.updatedAt = date;
                    asistencia.createdAt = date;
                    asistencia.updatedBy = {
                        idUsuario: Number(user.id),
                    };
                    asistencia.createdBy = {
                        idUsuario: Number(user.id),
                    };
                    asistenciasMap.set(key, asistencia);
                }
                const notaAnual = notasAnualesMap.get(key);
                if (!notaAnual) {
                    const anual = new entities_1.CalificacionesSecundarioAnual();
                    anual.alumno = { idAlumno: matricula.alumno.idAlumno };
                    anual.espacioCurricularSeccion = {
                        idEspacioCurricularSeccion: Number(ecs.idEspacioCurricularSeccion),
                    };
                    anual.nota = nota + '';
                    anualesToSave.push(anual);
                }
                let secundario;
                if (!mapsRowsSecundario.has(key)) {
                    secundario = new entities_1.Secundario();
                    secundario.calificacion = calificacion;
                }
                else {
                    secundario = mapsRowsSecundario.get(key);
                }
                secundario.abierto = !closeCalfs;
                secundario.nota = nota + '';
                secundario.data = DATA_TO_SAVE;
                secundario.aprobado = nota > 5;
                secundario.espacioCurricularSeccion = {
                    idEspacioCurricularSeccion: Number(ecs.idEspacioCurricularSeccion),
                };
                secundario.updatedAt = date;
                secundario.updatedBy = {
                    idUsuario: Number(user.id),
                };
                secundario.createdBy = {
                    idUsuario: Number(user.id),
                };
                records.push(secundario);
            }
        }
        const calfAsistenciaToInsert = [...asistenciasMap.values()].filter((ca) => !Boolean(ca.idCalificacionAsistenciaEc));
        const calfAsistenciaToUpdate = [...asistenciasMap.values()].filter((ca) => Boolean(ca.idCalificacionAsistenciaEc));
        const anualesToInsert = anualesToSave.filter((ca) => !Boolean(ca.idSecundarioAnual));
        const anualesToUpdate = anualesToSave.filter((ca) => Boolean(ca.idSecundarioAnual));
        const promises = [];
        if (anualesToInsert.length > 0)
            promises.push(dao_1.CalificacionesSecundariaAnualDAO.save(anualesToInsert, tx, {
                isInsert: true,
                context: ctx,
                aspect: 'sited',
            }));
        if (anualesToUpdate.length > 0)
            promises.push(dao_1.CalificacionesSecundariaAnualDAO.save(anualesToUpdate, tx, {
                isInsert: true,
                context: ctx,
                aspect: 'sited',
            }));
        if (calfAsistenciaToInsert.length > 0)
            promises.push(dao_1.CalificacionAsistenciaEcDAO.save(calfAsistenciaToInsert, tx, {
                aspect: 'bulk-califications',
                isInsert: true,
                context: ctx,
            }));
        if (calfAsistenciaToUpdate.length > 0)
            promises.push(dao_1.CalificacionAsistenciaEcDAO.save(calfAsistenciaToUpdate, tx, {
                aspect: 'bulk-califications',
                isUpdate: true,
                context: ctx,
            }));
        await Promise.all(promises);
        const [recordsToUpdate, recordsToInsert] = records.reduce((acc, item) => {
            acc[(item === null || item === void 0 ? void 0 : item.idConocimiento) ? 0 : 1].push(item);
            return acc;
        }, [[], []]);
        if (recordsToUpdate.length > 0) {
            const recordsToUpdateFormatted = recordsToUpdate.map((item) => {
                return this.formatSecundarioCalificacionToSave(item);
            });
            await dao_1.SecundarioDAO.save(recordsToUpdateFormatted, tx, {
                aspect: 'carga-masiva',
                isUpdate: true,
                context: ctx,
            });
        }
        const withCalificacionesFormatted = recordsToInsert.map((item) => {
            return this.formatSecundarioCalificacionToSave(item);
        });
        req.body = withCalificacionesFormatted;
    }
    formatSecundarioCalificacionToSave(secundario) {
        return {
            ...secundario,
            calificacion: {
                ...(secundario.calificacion.idCalificacion && {
                    idCalificacion: Number(secundario.calificacion.idCalificacion),
                }),
                periodo: {
                    idPeriodo: secundario.calificacion.periodo.idPeriodo,
                },
                alumno: { idAlumno: secundario.calificacion.alumno.idAlumno },
            },
            alumno: Number(secundario.calificacion.alumno.idAlumno),
        };
    }
    async postInsertAction(value, session, req, tx) {
        var _a;
        const user = session.user;
        await this.saveIntencion({
            seccion: {
                idSeccion: Number(this.params.seccion),
            },
            periodo: { idPeriodo: Number(this.params.periodo) },
            nota: this.params.nota,
            createdBy: { idUsuario: Number(user.id) },
            realizado: true,
            createdAt: dateUtils_1.dateUtils.toUTC(new Date()),
        }, tx, chino_sdk_1.ChinoContext.fromSession(session));
        return Array.isArray(value)
            ? value.map((v) => {
                var _a;
                return ({
                    ...v,
                    calificacionAnual: (_a = v.data) === null || _a === void 0 ? void 0 : _a.calificacionAnual,
                });
            })
            : {
                ...value,
                calificacionAnual: (_a = value.data) === null || _a === void 0 ? void 0 : _a.calificacionAnual,
            };
    }
    getAllowGuest() {
        return false;
    }
}
exports.BulkGeneratingCalificacionesEndpoint = BulkGeneratingCalificacionesEndpoint;
//# sourceMappingURL=BulkGeneratingCalificacionesEndpoint.js.map
import { MiEscuelaEndpoint } from '../../app/config/endpoint/MiEscuelaEndpoints';
import { PPS } from '../orm/entities';
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
export declare class PPSEndpoint extends MiEscuelaEndpoint<PPS> {
    constructor();
    getAllowGuest(): boolean;
    handleAdjuntos(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
    preUpdateAction(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
}

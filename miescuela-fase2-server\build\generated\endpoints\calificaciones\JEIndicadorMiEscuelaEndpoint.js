"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JEIndicadorMiEscuelaEndpoint = void 0;
const MiEscuelaEndpointV2_1 = require("../../../app/config/endpoint/MiEscuelaEndpointV2");
const dao_1 = require("../../orm/dao");
class JEIndicadorMiEscuelaEndpoint extends MiEscuelaEndpointV2_1.MiEscuelaEndpointV2 {
    constructor() {
        super(dao_1.JEIndicadorV2DAO.entity.toLowerCase(), '/calificaciones/' + dao_1.JEIndicadorV2DAO.entity.toLowerCase(), dao_1.JEIndicadorV2DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.JEIndicadorMiEscuelaEndpoint = JEIndicadorMiEscuelaEndpoint;
//# sourceMappingURL=JEIndicadorMiEscuelaEndpoint.js.map
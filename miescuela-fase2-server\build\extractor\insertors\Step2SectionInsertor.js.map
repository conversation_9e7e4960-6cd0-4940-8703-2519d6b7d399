{"version": 3, "file": "Step2SectionInsertor.js", "sourceRoot": "", "sources": ["../../../src/extractor/insertors/Step2SectionInsertor.ts"], "names": [], "mappings": ";;;AAAA,gCAA6D;AAK7D,oCAAwD;AAExD,MAAM,MAAM,GAAG;IACb,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,cAAc;IACxB,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,WAAW;IACrB,IAAI,EAAE,OAAO;IACb,UAAU,EAAE,aAAa;IACzB,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,cAAc;IAC5B,mBAAmB,EAAE,oBAAoB;IACzC,gBAAgB,EAAE,iBAAiB;IACnC,KAAK,EAAE,OAAO;CACf,CAAC;AACF,MAAM,MAAM,GAAG,IAAI,gBAAU,CAAC,YAAY,CAAC,CAAC;AAE5C,MAAa,oBAAoB;IAC/B,KAAK,CAAC,GAAG,CACP,OAAkC,EAClC,MAAkB,EAClB,aAA8C;QAE9C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,qFAAqF,EACrF,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,CACvC,CAAC;YACF,aAAa,CAAC,QAAQ,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACnE;QAED,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,EAAE;YACnC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,6QAA6Q,EAC7Q;gBACE,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,IAAI;gBACV,KAAK,CAAC,YAAY;gBAClB,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,YAAY;gBAClB,KAAK,CAAC,YAAY;gBAClB,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,IAAI;gBACV,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG;gBAC9D,KAAK,CAAC,WAAW;aAClB,CACF,CAAC;YACF,aAAa,CAAC,OAAO,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACjE;QAED,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,wMAAwM,EACxM;gBACE,KAAK,CAAC,cAAc;gBACpB,aAAa;gBACb,EAAE;gBACF,EAAE;gBACF,CAAC;gBACD,eAAe;gBACf,KAAK,CAAC,iBAAiB;gBACvB,KAAK,CAAC,WAAW;aAClB,CACF,CAAC;YACF,aAAa,CAAC,QAAQ,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACnE;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAClC,CACE,IAAiE,EACjE,OAAO,EACP,EAAE;YACF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;oBACnB,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;oBACxB,gBAAgB,EAAE,OAAO,CAAC,IAAI;iBAC/B,CAAC;aACH;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EACD,EAAE,CACH,CAAC;QACF,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACxC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,+EAA+E,EAC/E,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,gBAAgB,CAAC,CACrC,CAAC;YACF,aAAa,CAAC,KAAK,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,KAAK,EAAE;YACjC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,8EAA8E,EAC9E,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,CACnC,CAAC;YACF,aAAa,CAAC,KAAK,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAChC,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC5C,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,+IAA+I,EAC/I;gBACE,KAAK,CAAC,iBAAiB;gBACvB,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,eAAe;gBACrB,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,OAAO;aACd,CACF,CAAC;YACF,aAAa,CAAC,gBAAgB,GAAG,IAAA,kBAAU,EACzC,aAAa,CAAC,gBAAgB,EAC9B,IAAI,CACL,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,WAAW,EAAE;YACvC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,gGAAgG,EAChG,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,WAAW,CAAC,CACzC,CAAC;YACF,aAAa,CAAC,WAAW,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SACzE;QAED,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,EAAE;YACtC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,uHAAuH,EACvH;gBACE,KAAK,CAAC,aAAa;gBACnB,KAAK,CAAC,aAAa;gBACnB,eAAe,KAAK,CAAC,aAAa,EAAE;aACrC,CACF,CAAC;YACF,aAAa,CAAC,UAAU,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SACvE;QAED,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9B,KAAK,MAAM,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE;YAC7C,KAAK,MAAM,KAAK,IAAI,aAAa,CAAC,KAAK,EAAE;gBACvC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,mFAAmF,EACnF,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,YAAY,CAAC,CACxC,CAAC;gBACF,aAAa,CAAC,cAAc,GAAG,IAAA,kBAAU,EACvC,aAAa,CAAC,cAAc,EAC5B,IAAI,CACL,CAAC;aACH;SACF;QAED,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,aAAa,CAAC,UAAU,EAAE;YAC3C,KAAK,MAAM,aAAa,IAAI,aAAa,CAAC,cAAc,EAAE;gBACxD,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,mGAAmG,EACnG,CAAC,aAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,CACzD,CAAC;gBACF,aAAa,CAAC,gBAAgB,GAAG,IAAA,kBAAU,EACzC,aAAa,CAAC,gBAAgB,EAC9B,IAAI,CACL,CAAC;aACH;SACF;QAED,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACnC,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC/C,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,2HAA2H,EAC3H,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,MAAM,CAAC,CAC3C,CAAC;YACF,aAAa,CAAC,mBAAmB,GAAG,IAAA,kBAAU,EAC5C,aAAa,CAAC,mBAAmB,EACjC,IAAI,CACL,CAAC;SACH;QAED,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,YAAY,EAAE;YACxC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,+IAA+I,EAC/I;gBACE,KAAK,CAAC,cAAc;gBACpB,KAAK,CAAC,IAAI;gBACV,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,oBAAoB;aAC3B,CACF,CAAC;YACF,aAAa,CAAC,YAAY,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC3E;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE;YAChC,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,8EAA8E,EAC9E,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,CACnC,CAAC;YACF,aAAa,CAAC,IAAI,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3D;QAED,KAAK,MAAM,gBAAgB,IAAI,aAAa,CAAC,gBAAgB,EAAE;YAC7D,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,IAAI,CAC9C,CAAC,KAAU,EAAE,EAAE,CACb,KAAK,CAAC,eAAe,IAAI,gBAAgB,CAAC,eAAe,CAC5D,CAAC;YACF,MAAM,EACJ,IAAI,GACL,GAAG,MAAM,MAAM,CAAC,KAAK,CACpB,+GAA+G,EAC/G;gBACE,eAAe,gBAAgB,CAAC,qBAAqB,EAAE;gBACvD,gBAAgB,CAAC,qBAAqB;gBACtC,UAAU,CAAC,cAAc;aAC1B,CACF,CAAC;YACF,aAAa,CAAC,KAAK,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;YACtD,MAAM,IAAI,GACR,aAAa,CAAC,KAAK,CAAC,IAAI,CACtB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,CAC/C,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,gBAAgB,GAAG,IAAA,wBAAgB,EAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;YAC1E,MAAM,KAAK,GAAG,IAAA,wBAAgB,EAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,KAAK,CAAC,cAAc,KAAK,IAAI;gBAAE,SAAS;YAC5C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,MAAM,CAAC,KAAK,CACjC;;;;;;;;;;;wHAWgH,EAChH;gBACE,KAAK,CAAC,SAAS;gBACf,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE;gBAC1B,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,eAAe,IAAI,CAAC;gBAC1B,KAAK,CAAC,aAAa,IAAI,CAAC;gBACxB,IAAI,CAAC,OAAO;gBACZ,KAAK,CAAC,cAAc;gBACpB,gBAAgB,CAAC,qBAAqB;gBACtC,KAAK,CAAC,cAAc;gBACpB,KAAK,CAAC,iBAAiB;gBACvB,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,QAAQ;aACf,CACF,CAAC;YACF,aAAa,CAAC,OAAO,GAAG,IAAA,kBAAU,EAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACjE;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AAtRD,oDAsRC"}
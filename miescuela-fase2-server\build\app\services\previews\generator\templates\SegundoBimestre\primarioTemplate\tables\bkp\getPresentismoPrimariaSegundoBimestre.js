"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPresentismoPrimariaSegundoBimestre = void 0;
const getPresentismoPrimariaSegundoBimestre = (tipoPeriodo, presentismo, observacionBoletin) => {
    return `
  <table class="table-bimestres" style="width: 100%; margin-bottom: 29px;" cellspacing="0" cellpadding="0">
  <thead>

    <tr style="height: 19px;" class="bb-1">
      <th class="cell bt-1 tac bl-1 br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold" style="width:599px;" colspan="3"
        rowspan="2">
        Presentismo
      </th>
      <th class="cell bt-1 tac br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold" style="width:303px;" colspan="3"
        rowspan="2">
        ${tipoPeriodo}
      </th>
    </tr>

  </thead>
  <tbody>

<tr>
  <td class="cell bl-1 br-1 ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
    Ausentes
  </td>
  <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
  ${presentismo.ausentes}
  </td>
</tr>

<tr>
  <td class="cell bl-1 br-1 ff-calibri fs-11 tar bb-1 va-middle ws-normal" dir="ltr" colspan="3">
    Justificados
  </td>
  <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
     ${presentismo.justificados}
  </td>
</tr>

<tr>
  <td class="cell bl-1 br-1 ff-calibri fs-11 tar bb-1 va-middle ws-normal" dir="ltr" colspan="3" >
    Injustificados
  </td>
  <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3" >
     ${presentismo.injustificados}
  </td>
</tr>

<tr>
  <td class="cell bl-1 br-1 ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
    Ingresos tardíos/ Retiros Anticipados
  </td>
  <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
      ${presentismo.ingresoTardio + presentismo.retiroAnticipado}
  </td>
</tr>

<tr>
  <td class="cell bl-1 br-1 ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
    Presentes
  </td>
  <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
      ${presentismo.presentes}
  </td>
</tr>

<tr>
  <td class="cell bl-1 br-1 ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
    Observaciones de Presentismo:
  </td>
  <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal wb-all" dir="ltr" colspan="3">
      ${(observacionBoletin === null || observacionBoletin === void 0 ? void 0 : observacionBoletin.observacion) || ''}
  </td>
</tr>

</tbody>
</table>
     `;
};
exports.getPresentismoPrimariaSegundoBimestre = getPresentismoPrimariaSegundoBimestre;
//# sourceMappingURL=getPresentismoPrimariaSegundoBimestre.js.map
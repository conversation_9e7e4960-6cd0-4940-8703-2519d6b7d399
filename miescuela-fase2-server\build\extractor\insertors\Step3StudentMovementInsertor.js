"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Step3StudentMovementInsertor = void 0;
const lib_1 = require("../lib");
const utils_1 = require("../utils");
const Logger = new lib_1.LogManager(`Insertor 3`);
class Step3StudentMovementInsertor {
    async run(context, target, targetContext) {
        Logger.info('country');
        for (const value of context.country) {
            const { rows, } = await target.query(`INSERT INTO pais (id_pais, descripcion_pais) VALUES ($1, $2) RETURNING *;`, [value.idpais, value.descripcion]);
            targetContext.country = (0, utils_1.addToArray)(targetContext.country, rows);
        }
        Logger.info('estado alumno');
        for (const value of context.studentState) {
            console.log('value.idestadoalumno', value.idestadoalumno);
            const { rows, } = await target.query(`INSERT INTO estado_alumno (id_estado_alumno, descrcipcion_estado_alumno) VALUES ($1, $2) RETURNING *;`, [value.idestadoalumno, value.descripcion]);
            targetContext.studentState = (0, utils_1.addToArray)(targetContext.studentState, rows);
        }
        Logger.info('TODO: estado matricula');
        const matriculaStates = ['Test Positive', 'Test negative'];
        for (const value of matriculaStates) {
            const { rows, } = await target.query(`INSERT INTO estado_matricula (descripcion_estado_matricula) VALUES ($1) RETURNING *;`, [value]);
            targetContext.matriculaState = (0, utils_1.addToArray)(targetContext.matriculaState, rows);
        }
        Logger.info('TODO: condition');
        const conditions = ['Condition Positive', 'Condition negative'];
        for (const value of conditions) {
            const { rows, } = await target.query(`INSERT INTO condicion (descripcion_condicion) VALUES ($1) RETURNING *;`, [value]);
            targetContext.condition = (0, utils_1.addToArray)(targetContext.condition, rows);
        }
        Logger.info('TODO: genero');
        const genres = ['Sin genero'];
        for (const value of genres) {
            const { rows, } = await target.query(`INSERT INTO genero (descripcion_genero) VALUES ($1) RETURNING *;`, [value]);
            targetContext.genres = (0, utils_1.addToArray)(targetContext.genres, rows);
        }
        Logger.info('tipo documento');
        for (const value of context.documentType) {
            const { rows, } = await target.query(`INSERT INTO tipo_documento (id_tipo_documento, descripcion_tipo_documento) VALUES ($1, $2) RETURNING *;`, [value.idtipodocumento, value.descripcion]);
            targetContext.documentType = (0, utils_1.addToArray)(targetContext.documentType, rows);
        }
        Logger.info('persona');
        const chunk = 5000;
        let i, j;
        const copyPerson = [...context.person];
        for (i = 0, j = copyPerson.length; i < j; i += chunk) {
            Logger.info(`Doing person ${i}`);
            const piece = copyPerson.slice(i, i + chunk);
            const values = piece.reduce((acum, value, index) => {
                acum.str.push(`(
          $${index * 13 + 1},
          $${index * 13 + 2},
          $${index * 13 + 3},
          $${index * 13 + 4},
          $${index * 13 + 5},
          $${index * 13 + 6},
          $${index * 13 + 7},
          $${index * 13 + 8},
          $${index * 13 + 9},
          $${index * 13 + 10},
          $${index * 13 + 11},
          $${index * 13 + 12},
          $${index * 13 + 13}
        )`);
                acum.values.push(...[
                    value.idpersona,
                    value.nombre,
                    value.apellido,
                    value.fechanacimiento,
                    value.estado_civil,
                    value.idtipodocumento,
                    value.documento,
                    value.pob_indig_habla,
                    value.lengua_indig,
                    value.pob_indig_pertenece,
                    value.idnacionalidad || targetContext.country[0].id_pais,
                    targetContext.genres[0].id_genero,
                    value.idpaisnac ||
                        value.idnacionalidad ||
                        targetContext.country[0].id_pais,
                ]);
                return acum;
            }, { str: [], values: [] });
            const { rows } = await target.query(`INSERT INTO persona (id_persona,
                                      nombre,
                                      apellido,
                                      fecha_nacimiento,
                                      estado_civil,
                                      id_tipo_documento,
                                      documento,
                                      pb_indigena_habla,
                                      lengua_indigena,
                                      pb_indigena_pertenece,
                                      id_nacionalidad,
                                      id_genero,
                                      id_pais_nacimiento) VALUES ${values.str.join(', ')} RETURNING *;`, values.values);
            targetContext.person = (0, utils_1.addToArray)(targetContext.person, rows);
        }
        Logger.info('alumno');
        const copyAlumno = [...context.student];
        for (i = 0, j = copyAlumno.length; i < j; i += chunk) {
            Logger.info(`Doing student ${i}`);
            const piece = copyAlumno.slice(i, i + chunk);
            const values = piece.reduce((acum, value, index) => {
                acum.str.push(`(
          $${index * 4 + 1},
          $${index * 4 + 2},
          $${index * 4 + 3},
          $${index * 4 + 4}
        )`);
                acum.values.push(...[
                    value.idalumno,
                    value.idpersona,
                    targetContext.matriculaState[0].id_estado_matricula,
                    targetContext.condition[0].id_condicion,
                ]);
                return acum;
            }, { str: [], values: [] });
            const { rows } = await target.query(`INSERT INTO alumno (id_alumno, id_persona, id_estado_matricula, id_condicion) VALUES ${values.str.join(', ')} RETURNING *;`, values.values);
            targetContext.student = (0, utils_1.addToArray)(targetContext.student, rows);
        }
        Logger.info('estado_promocion_pase_anio');
        const { rows } = await target.query(`INSERT INTO estado_promocion_pase_anio (id_estado_pase, descripcion_estado_pase) VALUES (1, 'Aprueba'), (2, 'Pendiente'), (3, 'No Promociona') RETURNING *;`, []);
        targetContext.estado_promocion_pase_anio = (0, utils_1.addToArray)(targetContext.estado_promocion_pase_anio, rows);
        Logger.info('alumno movimiento');
        let copyStudentMovement = [...context.studentMovement];
        for (i = 0, j = copyStudentMovement.length; i < j; i += chunk) {
            Logger.info(`Doing student movement ${i}`);
            const piece = copyStudentMovement.slice(i, i + chunk);
            const values = piece.reduce((acum, value, index) => {
                const State = (0, utils_1.getRandomElement)(targetContext.estado_promocion_pase_anio);
                acum.str.push(`(
          $${index * 5 + 1},
          $${index * 5 + 2},
          $${index * 5 + 3},
          $${index * 5 + 4},
          $${index * 5 + 5}
        )`);
                acum.values.push(...[
                    value.idalumnomovimiento,
                    value.idalumno,
                    value.idseccion,
                    value.idciclolectivo,
                    State.id_estado_pase, // no se porque esto es id_estado_promocion_pase_anio
                ]);
                return acum;
            }, { str: [], values: [] });
            const { rows } = await target.query(`INSERT INTO alumno_movimiento (id_alumno_movimiento, id_alumno, id_seccion, id_ciclo_lectivo, id_estado_alumno) VALUES ${values.str.join(', ')} RETURNING *;`, values.values);
            targetContext.studentMovement = (0, utils_1.addToArray)(targetContext.studentMovement, rows);
        }
        Logger.info('alumno movimiento');
        copyStudentMovement = [...context.studentMovement];
        for (i = 0, j = copyStudentMovement.length; i < j; i += chunk) {
            Logger.info(`Doing student inscriptions ${i}`);
            const piece = copyStudentMovement.slice(i, i + chunk);
            const values = piece.reduce((acum, value, index) => {
                const section = targetContext.section.find((section) => section.id_seccion == value.idseccion);
                const planestudionivel = targetContext.plan_study_level.find((plan_study_level) => plan_study_level.id_plan_estudio_nivel ==
                    section.id_plan_estudio_nivel);
                const modalitylevel = targetContext.modality_level.find((modality_level) => modality_level.id_modalidad_nivel ==
                    planestudionivel.id_modalidad_nivel);
                const condition = (0, utils_1.getRandomElement)(targetContext.condition);
                acum.str.push(`(
          $${index * 6 + 1},
          $${index * 6 + 2},
          $${index * 6 + 3},
          $${index * 6 + 4},
          $${index * 6 + 5},
          $${index * 6 + 6}
        )`);
                acum.values.push(...[
                    value.idalumno,
                    modalitylevel.id_nivel,
                    section.id_anio,
                    section.id_turno || -1,
                    condition.id_condicion,
                    section.id_localizacion,
                ]);
                return acum;
            }, { str: [], values: [] });
            const { rows } = await target.query(`INSERT INTO inscripcion_alumno (id_alumno,
                                id_nivel,
                                id_anio,
                                id_turno,
                                id_condicion,
                                id_localizacion) VALUES ${values.str.join(', ')} RETURNING *;`, values.values);
            targetContext.inscriptionStudent = (0, utils_1.addToArray)(targetContext.inscriptionStudent, rows);
        }
        return targetContext;
    }
}
exports.Step3StudentMovementInsertor = Step3StudentMovementInsertor;
//# sourceMappingURL=Step3StudentMovementInsertor.js.map
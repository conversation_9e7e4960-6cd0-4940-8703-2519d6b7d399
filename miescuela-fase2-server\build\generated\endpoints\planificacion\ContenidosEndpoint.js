"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContenidosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ContenidosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ContenidosDAO.entity.toLowerCase(), '/planificacion/' + dao_1.ContenidosDAO.entity.toLowerCase(), dao_1.ContenidosDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ContenidosEndpoint = ContenidosEndpoint;
//# sourceMappingURL=ContenidosEndpoint.js.map
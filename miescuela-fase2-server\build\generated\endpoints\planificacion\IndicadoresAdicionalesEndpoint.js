"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicadoresAdicionalesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class IndicadoresAdicionalesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.IndicadoresAdicionalesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.IndicadoresAdicionalesDAO.entity.toLowerCase(), dao_1.IndicadoresAdicionalesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.IndicadoresAdicionalesEndpoint = IndicadoresAdicionalesEndpoint;
//# sourceMappingURL=IndicadoresAdicionalesEndpoint.js.map
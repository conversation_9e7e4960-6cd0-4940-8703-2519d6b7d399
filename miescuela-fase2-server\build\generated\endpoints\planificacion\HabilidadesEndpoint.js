"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HabilidadesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class HabilidadesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.HabilidadesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.HabilidadesDAO.entity.toLowerCase(), dao_1.HabilidadesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.HabilidadesEndpoint = HabilidadesEndpoint;
//# sourceMappingURL=HabilidadesEndpoint.js.map
{"version": 3, "file": "getObservacionesTable.js", "sourceRoot": "", "sources": ["../../../../../../../../../../src/app/services/previews/generator/templates/SegundoBimestre/primarioTemplate/tables/bkp/getObservacionesTable.ts"], "names": [], "mappings": ";;AAAA,kBAAe,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAO,EAAE,EAAE,CAAC;UAC7D,CAAC,GAAG,EAAE;IACN,IAAI,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,IAAG,IAAI,EAAE;QAChC,OAAO;;;;;;qBAME,CAAC;KACX;SAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH,CAAC,CAAC,EAAE;;;;cAIE,aAAa;;;;;;;YAOf,WAAW;;;YAGX,aAAa;;;EAGvB,CAAC"}
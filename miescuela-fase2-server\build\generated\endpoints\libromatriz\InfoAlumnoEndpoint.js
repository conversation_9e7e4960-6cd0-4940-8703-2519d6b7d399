"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InfoAlumnoEndpoint = void 0;
const just_rpc_1 = require("@phinxlab/just-rpc");
const config_1 = require("../../../app/config");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const business_1 = require("../../../app/business");
const const_1 = require("../../../app/const");
class InfoAlumnoEndpoint extends config_1.MiEscuelaEndpointCustom {
    constructor(name = 'lm_alumno', path = '/alumno') {
        super(name, path);
    }
    configure() {
        this.allowGuest = false;
        this.addMethod(just_rpc_1.JRMethod.GET);
        this.addMethod(just_rpc_1.JRMethod.PUT);
    }
    async exec(req, tx) {
        try {
            switch (req.method) {
                case 'GET':
                    return await (0, business_1.FlowManager)({
                        flow: const_1.FLOWS.INFO_ALUMNO,
                        tx,
                        req,
                    }).run();
                case 'PUT':
                    return await (0, business_1.FlowManager)({
                        flow: const_1.FLOWS.SAVE_INFO_ALUMNO,
                        tx,
                        req,
                    }).run();
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('error', e);
            const err = e;
            // TODO NOT CHANGE, LIBBY NOT RECONGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(err.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
}
exports.InfoAlumnoEndpoint = InfoAlumnoEndpoint;
//# sourceMappingURL=InfoAlumnoEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JEMateriaAnioEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class JEMateriaAnioEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.JEMateriaAnioDAO.entity.toLowerCase(), '/public/' + dao_1.JEMateriaAnioDAO.entity.toLowerCase(), dao_1.JEMateriaAnioDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.JEMateriaAnioEndpoint = JEMateriaAnioEndpoint;
//# sourceMappingURL=JEMateriaAnioEndpoint.js.map
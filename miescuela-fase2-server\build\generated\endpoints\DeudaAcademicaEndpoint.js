"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeudaAcademicaEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
const entities_1 = require("../orm/entities");
const ERROR_DE_CARGA = 'error de carga';
const PENDIENTE_CAMBIO_PLAN = 'pendiente por cambio de plan de estudios';
const Log = new chino_sdk_1.ChinoLogManager('DeudaAcademicaEndpoint');
class DeudaAcademicaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.DeudaAcademicaDAO.entity.toLowerCase(), '/public/' + dao_1.DeudaAcademicaDAO.entity.toLowerCase(), dao_1.DeudaAcademicaDAO);
    }
    async postSelectAction(values, session, req) {
        const context = chino_sdk_1.ChinoContext.fromSession(session);
        const tx = chino_sdk_1.ChinoManager.getManager();
        if (req.query.alumno) {
            const idAlumno = req.query.alumno.equals;
            try {
                const sessionUser = session.user;
                const currentCL = sessionUser.groupSelected.localizacion.idCicloLectivo;
                await dao_1.AlumnoDAO.findByPK(idAlumno, {
                    aspect: 'bypk',
                    context,
                }, tx);
                const alumnoMovimiento = await chino_sdk_1.ChinoManager.getManager()
                    .getRepository(entities_1.AlumnoMovimiento)
                    .find({
                    relations: [
                        'alumno',
                        'seccion',
                        'seccion.nivel',
                        'seccion.anio',
                        'cicloLectivo',
                    ],
                    where: {
                        alumno: { idAlumno: idAlumno },
                        seccion: {
                            nivel: { descripcionNivel: 'Secundario' },
                        },
                    },
                });
                const idSeccionesRepitentes = alumnoMovimiento
                    .filter((am) => am.deletedAt !== null)
                    .map((am2) => Number(am2.seccion.idSeccion));
                const alumnosMovimientosNoRepitentes = alumnoMovimiento.filter((am) => am.deletedAt === null);
                const recuperacionQuery = await dao_1.RecuperacionDAO.query('deuda-academica')
                    .setContext(context)
                    .higher('espacioCurricularSeccion.espacioCurricular.localizacion', -1)
                    .lowerOrEquals('espacioCurricularSeccion.seccion.cicloLectivo', currentCL)
                    .equals('alumno', Number(idAlumno))
                    .orderBy('createdAt', chino_sdk_1.QueryOrderDirection.DESC)
                    .run(tx);
                const recuperacion = recuperacionQuery.filter((r) => !idSeccionesRepitentes.includes(Number(r.espacioCurricularSeccion.seccion.idSeccion)));
                const aprobadasEnRecuperacion = recuperacion.filter((r) => !r.fechaExamen &&
                    (r.periodo.idPeriodo === 14 || r.periodo.idPeriodo === 15) &&
                    r.aprobado);
                const secciones = alumnosMovimientosNoRepitentes.map((am) => am.seccion.idSeccion);
                const pems = values.map((v) => v.planEstudioMateria.idPlanEstudioMateria);
                const anio = values.map((v) => v.anio.idAnio);
                let espacioCurricularSecciones = [];
                if (secciones.length > 0 && pems.length > 0 && anio.length > 0) {
                    espacioCurricularSecciones = await dao_1.EspacioCurricularSeccionDAO.query('deuda-academica')
                        .in('seccion', secciones)
                        .in('espacioCurricular.planEstudioMateria', pems)
                        .in('seccion.anio', anio)
                        .setContext(context)
                        .run(tx);
                }
                if (espacioCurricularSecciones.length !== 0) {
                    values.forEach((v) => {
                        v.espacioCurricularSeccion = espacioCurricularSecciones.find((ecs) => {
                            return (ecs.seccion.cicloLectivo.idCicloLectivo ===
                                v.cicloLectivo.idCicloLectivo &&
                                ecs.espacioCurricular.planEstudioMateria
                                    .idPlanEstudioMateria ===
                                    v.planEstudioMateria.idPlanEstudioMateria &&
                                ecs.seccion.anio.idAnio === v.anio.idAnio);
                        });
                    });
                }
                const noAprobadasDicMarzo = recuperacion.filter((r) => {
                    return !aprobadasEnRecuperacion
                        .map((r) => r.espacioCurricularSeccion.idEspacioCurricularSeccion)
                        .includes(r.espacioCurricularSeccion.idEspacioCurricularSeccion);
                });
                if (values.length === 0 && noAprobadasDicMarzo.length === 0) {
                    return {
                        deudaAcademica: [],
                        recuperacion: [],
                    };
                }
                // Filtro deudas de años repetidos basados en ciclos lectivos repitentes
                const alumnosMovimientosRepitieron = alumnosMovimientosNoRepitentes.filter((am) => {
                    // Filtramos los AM, donde haya algún otro AM con el mismo anio y cicloLectivo mayor
                    return alumnosMovimientosNoRepitentes.some((am2) => am2.seccion.anio.numeroAnio === am.seccion.anio.numeroAnio && // mismo año (1er, 2do..)
                        am2.cicloLectivo.anio > am.cicloLectivo.anio);
                });
                const ciclosLectivosRepitentes = alumnosMovimientosRepitieron.map((am) => am.cicloLectivo.idCicloLectivo);
                const deudasAniosNoRepetidos = values.filter((deuda) => {
                    const descripcionDeuda = deuda.tipoDeudaAcademica.descripcion
                        .toLowerCase()
                        .trim();
                    return (!ciclosLectivosRepitentes.includes(deuda.cicloLectivo.idCicloLectivo) ||
                        descripcionDeuda === ERROR_DE_CARGA ||
                        descripcionDeuda === PENDIENTE_CAMBIO_PLAN);
                });
                const recuperacionesAniosNoRepetidos = noAprobadasDicMarzo.filter((recup) => {
                    var _a, _b;
                    const descripcionRecup = (_b = (_a = recup === null || recup === void 0 ? void 0 : recup.tipoDeudaAcademica) === null || _a === void 0 ? void 0 : _a.descripcion) === null || _b === void 0 ? void 0 : _b.toLowerCase().trim();
                    return (!ciclosLectivosRepitentes.includes(recup.espacioCurricularSeccion.seccion.cicloLectivo
                        .idCicloLectivo) || descripcionRecup === PENDIENTE_CAMBIO_PLAN);
                });
                values = {
                    deudaAcademica: deudasAniosNoRepetidos,
                    recuperacion: recuperacionesAniosNoRepetidos,
                };
            }
            catch (error) {
                Log.error(error.message);
                return chino_sdk_1.ChinoError.CustomException((error === null || error === void 0 ? void 0 : error.message) || 'Ocurrió un error al consultar la deuda academica', 500);
            }
            return values;
        }
        else {
            return values;
        }
    }
    getAllowGuest() {
        return true;
    }
}
exports.DeudaAcademicaEndpoint = DeudaAcademicaEndpoint;
//# sourceMappingURL=DeudaAcademicaEndpoint.js.map
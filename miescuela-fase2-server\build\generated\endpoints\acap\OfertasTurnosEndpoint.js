"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfertasTurnosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const helpers_1 = require("../../../utils/helpers");
class OfertasTurnosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.ofertasturnos', '/acap/ofertasturnos', dao_1.OfertasTurnosDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.OfertasTurnosEndpoint = OfertasTurnosEndpoint;
//# sourceMappingURL=OfertasTurnosEndpoint.js.map
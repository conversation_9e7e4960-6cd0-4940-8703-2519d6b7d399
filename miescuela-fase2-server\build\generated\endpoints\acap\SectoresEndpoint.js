"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SectoresEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const helpers_1 = require("../../../utils/helpers");
class SectoresEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.SectoresDAO.entity.toLowerCase(), '/acap/' + dao_1.SectoresDAO.entity.toLowerCase(), dao_1.SectoresDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.SectoresEndpoint = SectoresEndpoint;
//# sourceMappingURL=SectoresEndpoint.js.map
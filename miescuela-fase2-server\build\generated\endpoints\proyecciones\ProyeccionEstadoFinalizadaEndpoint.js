"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProyeccionEstadoFinalizadaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ProyeccionEstadoFinalizadaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('proyecciones_finalizadas', '/public/proyecciones_finalizadas', dao_1.ProyeccionFinalizadaDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ProyeccionEstadoFinalizadaEndpoint = ProyeccionEstadoFinalizadaEndpoint;
//# sourceMappingURL=ProyeccionEstadoFinalizadaEndpoint.js.map
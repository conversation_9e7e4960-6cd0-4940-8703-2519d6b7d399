"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalizacionEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class LocalizacionEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.LocalizacionV2DAO.entity.toLowerCase(), `/public/${dao_1.LocalizacionV2DAO.entity.toLowerCase()}v2`, dao_1.LocalizacionV2DAO);
    }
    getUniqueByDistritoEscolar(values) {
        const existingDistrict = new Set();
        return values.filter((item) => {
            var _a, _b;
            const idDistrito = (_b = (_a = item.establecimiento) === null || _a === void 0 ? void 0 : _a.distritoEscolar) === null || _b === void 0 ? void 0 : _b.idDistritoEscolar;
            if (idDistrito == null || existingDistrict.has(idDistrito)) {
                return false;
            }
            existingDistrict.add(idDistrito);
            return true;
        });
    }
    async postSelectAction(values, session, req) {
        if ('district-query' === req.headers['x-chino-aspect']) {
            values = this.getUniqueByDistritoEscolar(values);
        }
        return values;
    }
    getAllowGuest() {
        return false;
    }
}
exports.LocalizacionEndpoint = LocalizacionEndpoint;
//# sourceMappingURL=LocalizacionV2Endpoint.js.map
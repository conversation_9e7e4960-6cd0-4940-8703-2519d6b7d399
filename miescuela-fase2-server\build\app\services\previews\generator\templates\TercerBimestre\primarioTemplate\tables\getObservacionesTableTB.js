"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ observaciones, nombreSeccion, tipoPeriodo }) => `
        ${(() => {
    if ((observaciones === null || observaciones === void 0 ? void 0 : observaciones.length) > 2500) {
        return `<style>
              @media print {
                .portada-header {
                  page-break-after: always;
                }
              }
            </style>`;
    }
    else {
        return '';
    }
})()}
      <div class="portada-header">     
       <div class="text-title">
          <div>
            ${nombreSeccion}
          </div>
          <div>
            OBSERVACIONES
          </div>
        </div>
        <div class="text-subtitle">
          ${tipoPeriodo}
        </div>
        <div class="observaciones-field"> 
          ${observaciones}
        </div>  
      </div>
 `;
//# sourceMappingURL=getObservacionesTableTB.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runner = void 0;
const config_1 = require("@phinxlab/config");
const lib_1 = require("./lib");
const extractors_1 = require("./extractors");
const insertors_1 = require("./insertors");
const runner = async () => {
    const source = config_1.Config.getConfig('postgres-config-source-extractor.json');
    const target = config_1.Config.getConfig('postgres-config.json');
    const extractor = new lib_1.Extractor({ source, target, extractors: extractors_1.extractors, insertors: insertors_1.insertors });
    const data = await extractor.run();
    if (data) {
        for (const [key, value] of Object.entries(data.target)) {
            console.log(key, value.length);
        }
    }
};
exports.runner = runner;
(0, exports.runner)();
//# sourceMappingURL=runner.js.map
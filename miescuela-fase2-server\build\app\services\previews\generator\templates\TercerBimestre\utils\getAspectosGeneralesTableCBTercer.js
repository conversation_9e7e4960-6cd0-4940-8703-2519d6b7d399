"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ ciclo, anio, tipoPeriodo, apoyoPregunta = '', apoyo = [], otrosApoyos = '', acompaniamientoAreaPregunta = '', acompaniamientoArea = [], }) => {
    var _a;
    return `
<div class="page-container">
        <table class="tabla">
            <tr class="title-table">
                <td style="background-color: #e2eedb; font-weight: bold; font-size: 12px; width: 60%;">
                    ${ciclo} - ${anio}
                </td>
                <td style="background-color: #e2eedb; border-bottom: 0px; font-weight: bold; font-size: 12px; padding-top: 20px;">
                    ${tipoPeriodo}
                </td>
            </tr>
           <tr>
                <td style="background-color: #e2eedb; font-weight: bold; text-align: center;">
                    ASPECTOS GENERALES
                </td>
                <td style="background-color: #e2eedb;"></td>
            </tr>
            <tr>
              <td>¿Inicia el período de acompañamiento para la promoción?</td>
              <td style="text-align: center;">${acompaniamientoAreaPregunta !== null && acompaniamientoAreaPregunta !== void 0 ? acompaniamientoAreaPregunta : '-'}</td>
            </tr>
            
            <tr>
              <td>¿En qué área/s se focaliza el acompañamiento para la promoción?</td>
              <td style="text-align: center;">${(_a = acompaniamientoArea.filter((ap) => ap).join(', ')) !== null && _a !== void 0 ? _a : '-'}</td>
            </tr>
            <tr>
                <td>¿Posee apoyos/acompañamientos?</td>
                <td style="text-align: center;">${apoyoPregunta !== null && apoyoPregunta !== void 0 ? apoyoPregunta : '-'}</td>
            </tr>
            ${apoyo.length > 0
        ? `
            <tr>
                <td>¿Cuáles?</td>
                <td style="text-align: center;">${apoyo
            .filter((ap) => ap !== 'OTROS')
            .join(', ')} ${(apoyo === null || apoyo === void 0 ? void 0 : apoyo.length) === 1 && apoyo.includes('OTROS')
            ? `OTROS - ${otrosApoyos}`
            : apoyo.includes('OTROS')
                ? `, OTROS - ${otrosApoyos}`
                : '-'}</td>
            </tr>
            `
        : ''}
        </table>
      </div>`;
};
//# sourceMappingURL=getAspectosGeneralesTableCBTercer.js.map
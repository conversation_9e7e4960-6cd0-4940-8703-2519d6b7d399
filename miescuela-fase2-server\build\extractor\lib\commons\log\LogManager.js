"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
};
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _LogManager_name, _LogManager_logger;
Object.defineProperty(exports, "__esModule", { value: true });
exports.logReturn = exports.LogManager = void 0;
const log4js = __importStar(require("log4js"));
const ExtractorLog_1 = require("./ExtractorLog");
//FIXME THIS SHOULD GO TO LOOK FOR THE FILE DRECTYLY;
log4js.configure(ExtractorLog_1.ExtractorLog.get());
/**
 *
 */
class LogManager {
    constructor(name) {
        _LogManager_name.set(this, void 0);
        _LogManager_logger.set(this, void 0);
        __classPrivateFieldSet(this, _LogManager_name, name, "f");
        __classPrivateFieldSet(this, _LogManager_logger, log4js.getLogger(name), "f");
    }
    info(text, ...optionalParams) {
        __classPrivateFieldGet(this, _LogManager_logger, "f").info(text, optionalParams);
    }
    debug(text, ...optionalParams) {
        __classPrivateFieldGet(this, _LogManager_logger, "f").debug(text, optionalParams);
    }
    fatal(text, ...optionalParams) {
        __classPrivateFieldGet(this, _LogManager_logger, "f").fatal(text, optionalParams);
    }
    warn(text, ...optionalParams) {
        __classPrivateFieldGet(this, _LogManager_logger, "f").warn(text, optionalParams);
    }
    error(text, ...optionalParams) {
        __classPrivateFieldGet(this, _LogManager_logger, "f").error(text, optionalParams);
    }
    verbose(text, ...optionalParams) {
        __classPrivateFieldGet(this, _LogManager_logger, "f").trace(text, optionalParams);
    }
    static create(name) {
        return new LogManager(name);
    }
}
exports.LogManager = LogManager;
_LogManager_name = new WeakMap(), _LogManager_logger = new WeakMap();
const inlineLogger = new LogManager('Inline');
function logReturn(xx) {
    inlineLogger.info(xx);
    return xx;
}
exports.logReturn = logReturn;
//# sourceMappingURL=LogManager.js.map
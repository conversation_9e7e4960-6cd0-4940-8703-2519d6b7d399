"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtractorLog = void 0;
const defaultConfig = {
    appenders: {
        out: { type: 'stdout' },
    },
    categories: {
        default: { appenders: ['out'], level: 'info' },
    },
};
class ExtractorLog {
    static define(logConfig) {
        this.config = logConfig;
    }
    static get() {
        return this.config;
    }
}
exports.ExtractorLog = ExtractorLog;
ExtractorLog.config = defaultConfig;
//# sourceMappingURL=ExtractorLog.js.map
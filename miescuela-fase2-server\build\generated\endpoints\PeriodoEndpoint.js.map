{"version": 3, "file": "PeriodoEndpoint.js", "sourceRoot": "", "sources": ["../../../src/generated/endpoints/PeriodoEndpoint.ts"], "names": [], "mappings": ";;;AACA,qFAAiF;AACjF,oCAAwC;AAGxC,MAAa,eAAgB,SAAQ,sCAA0B;IAC7D;QACE,KAAK,CACH,gBAAU,CAAC,MAAM,CAAC,WAAW,EAAE,EAC/B,UAAU,GAAG,gBAAU,CAAC,MAAM,CAAC,WAAW,EAAE,EAC5C,gBAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAAW,EACX,OAAwB,EACxB,GAAQ;QAER,2DAA2D;QAC3D,gCAAgC;QAChC,IAAI,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,iBAAiB,EAAE;YACvD,MAAM,GAAG,MAAM,CAAC,MAAM,CACpB,CAAC,OAAgB,EAAE,EAAE,CACnB,CAAC,OAAO,CAAC,SAAS;gBAClB,CAAC,OAAO,CAAC,SAAS;oBAChB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE;wBACpC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CACvC,CAAC;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA/BD,0CA+BC"}
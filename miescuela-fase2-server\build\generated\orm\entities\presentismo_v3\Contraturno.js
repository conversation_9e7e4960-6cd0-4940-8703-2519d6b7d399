"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Contraturno = void 0;
const typeorm_1 = require("typeorm");
const CicloLectivo_1 = require("../CicloLectivo");
const custom_1 = require("../custom");
let Contraturno = class Contraturno {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id_contraturno',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], Contraturno.prototype, "idContraturno", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => custom_1.SeccionCustom),
    (0, typeorm_1.JoinColumn)([{ name: 'id_seccion', referencedColumnName: 'idSeccion' }]),
    __metadata("design:type", custom_1.SeccionCustom)
], Contraturno.prototype, "seccion", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => CicloLectivo_1.CicloLectivo),
    (0, typeorm_1.JoinColumn)([
        { name: 'id_ciclo_lectivo', referencedColumnName: 'idCicloLectivo' },
    ]),
    __metadata("design:type", CicloLectivo_1.CicloLectivo)
], Contraturno.prototype, "cicloLectivo", void 0);
__decorate([
    (0, typeorm_1.Column)('boolean', { name: 'active' }),
    __metadata("design:type", Boolean)
], Contraturno.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.Column)('date', { name: 'fecha_inicio' }),
    __metadata("design:type", String)
], Contraturno.prototype, "fechaInicio", void 0);
__decorate([
    (0, typeorm_1.Column)('date', { name: 'fecha_fin' }),
    __metadata("design:type", String)
], Contraturno.prototype, "fechaFin", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp', { name: 'created_at' }),
    __metadata("design:type", String)
], Contraturno.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)('integer', { name: 'updated_by', nullable: true }),
    __metadata("design:type", Object)
], Contraturno.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp', { name: 'updated_at' }),
    __metadata("design:type", String)
], Contraturno.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp', { name: 'metadata' }),
    __metadata("design:type", Object)
], Contraturno.prototype, "metadata", void 0);
Contraturno = __decorate([
    (0, typeorm_1.Index)('contraturnos_pk', ['idContraturno'], {
        unique: true,
    }),
    (0, typeorm_1.Entity)('contraturnos', {
        schema: 'presentismo_v3',
    })
], Contraturno);
exports.Contraturno = Contraturno;
//# sourceMappingURL=Contraturno.js.map
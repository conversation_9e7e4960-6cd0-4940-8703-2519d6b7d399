"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFromTable = exports.getFromFile = exports.saveToFile = exports.getRandomElement = exports.addToArray = void 0;
const fs_1 = __importDefault(require("fs"));
const addToArray = (array, _value) => {
    const value = Array.isArray(_value) ? _value : [_value];
    if (!array) {
        return [...value];
    }
    return [...array, ...value];
};
exports.addToArray = addToArray;
const getRandomElement = (array) => array[Math.floor(Math.random() * array.length)];
exports.getRandomElement = getRandomElement;
const saveToFile = (key, data) => {
    fs_1.default.writeFileSync(`/tmp/miescuela${key}.json`, JSON.stringify(data));
};
exports.saveToFile = saveToFile;
const getFromFile = (key) => {
    if (!fs_1.default.existsSync(`/tmp/miescuela${key}.json`)) {
        return null;
    }
    const data = fs_1.default.readFileSync(`/tmp/miescuela${key}.json`);
    return JSON.parse(data);
};
exports.getFromFile = getFromFile;
const getFromTable = async (table, source) => {
    /* const cache = getFromFile(table);
    if (cache) {
      console.log('Levantando de /tmp', table);
      return cache;
    } */
    const { rows } = await source.query(`SELECT * FROM ${table}`);
    /* saveToFile(table, rows); */
    return rows;
};
exports.getFromTable = getFromTable;
//# sourceMappingURL=utils.js.map
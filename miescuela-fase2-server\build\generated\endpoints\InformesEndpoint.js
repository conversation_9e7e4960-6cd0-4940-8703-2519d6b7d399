"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InformesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class InformesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.InformesDAO.entity.toLowerCase(), '/public/' + dao_1.InformesDAO.entity.toLowerCase(), dao_1.InformesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.InformesEndpoint = InformesEndpoint;
//# sourceMappingURL=InformesEndpoint.js.map
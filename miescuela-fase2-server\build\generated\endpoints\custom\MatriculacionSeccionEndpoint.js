"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MatriculacionSeccionEndpoint = void 0;
const config_1 = require("../../../app/config");
const MatriculacionSeccionDAO_1 = require("../../../generated/orm/dao/custom/MatriculacionSeccionDAO");
class MatriculacionSeccionEndpoint extends config_1.MiEscuelaEndpoint {
    constructor() {
        super(MatriculacionSeccionDAO_1.MatriculacionSeccionDAO.entity.toLowerCase(), '/matriculacion/' + MatriculacionSeccionDAO_1.MatriculacionSeccionDAO.entity.toLowerCase(), MatriculacionSeccionDAO_1.MatriculacionSeccionDAO);
    }
    getAllowGuest() {
        return false;
    }
    getMethods() {
        return ['GET'];
    }
}
exports.MatriculacionSeccionEndpoint = MatriculacionSeccionEndpoint;
//# sourceMappingURL=MatriculacionSeccionEndpoint.js.map
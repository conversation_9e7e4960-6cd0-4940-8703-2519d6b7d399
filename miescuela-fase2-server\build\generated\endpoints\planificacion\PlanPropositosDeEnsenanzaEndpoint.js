"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanPropositosDeEnsenanzaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PlanPropositosDeEnsenanzaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PlanPropositosDeEnsenanzaDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PlanPropositosDeEnsenanzaDAO.entity.toLowerCase(), dao_1.PlanPropositosDeEnsenanzaDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PlanPropositosDeEnsenanzaEndpoint = PlanPropositosDeEnsenanzaEndpoint;
//# sourceMappingURL=PlanPropositosDeEnsenanzaEndpoint.js.map
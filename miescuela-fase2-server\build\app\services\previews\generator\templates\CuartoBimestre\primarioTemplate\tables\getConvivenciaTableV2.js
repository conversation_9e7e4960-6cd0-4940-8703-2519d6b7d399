"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConvivenciaTableV2 = void 0;
const getConvivenciaTableV2 = ({ ciclo, anio, tipoPeriodo, acuerdosGrupales = '', repetuosoConSusPares = '', repetuosoConAdultos = '', resuelveConflictos = '', situacionesDeJuego = '', }) => {
    return acuerdosGrupales
        ? `<table style="width: 100%; margin-bottom: 29px;" class="table-bimestres" cellspacing="0" cellpadding="0">
    <thead>
        <tr>
            <th class="cell bt-1 tac bl-1 br-1 ff-calibri fs-11 va-bottom ws-normal bb-1 bgc-gray bold" style="width:680px" colspan="3">
                ${ciclo} - ${anio}
            </th>
            <th class="cell bt-1 tac br-1 ff-calibri fs-11 va-middle ws-normal bb-1 bgc-gray bold" dir="ltr" style="width:443px" rowspan="2"
                colspan="3">
                ${tipoPeriodo}
            </th>
        </tr>
        <tr>
            <td class="cell va-middle bl-1 br-1 bold ff-arial fs-10 ws-normal tal bb-1 bgc-gray" colspan="3">
                CONVIVENCIA
            </td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                Acepta acuerdos grupales y normas institucionales.
            </td>
            <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
                ${acuerdosGrupales}
            </td>
        </tr>
        <tr>
            <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr" colspan="3">
                Participa en situaciones de juego.
            </td>
            <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tal " dir="ltr" colspan="3">
                ${situacionesDeJuego}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                Resuelve conflictos a través del diálogo.
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${resuelveConflictos}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
                Se vincula de manera respetuosa con los adultos.
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${repetuosoConAdultos}
            </td>
        </tr>
        <tr>
            <td class="cell ff-arial fs-11 va-middle ws-normal bold tal bl-1 br-1 bb-1" colspan="3">
              Se vincula de manera respetuosa con sus pares.
            </td>
            <td class="cell ff-arial fs-11 va-middle ws-normal tal br-1 bb-1" colspan="3">
                ${repetuosoConSusPares}
            </td>
        </tr>
    </tbody>
</table>
`
        : ``;
};
exports.getConvivenciaTableV2 = getConvivenciaTableV2;
//# sourceMappingURL=getConvivenciaTableV2.js.map
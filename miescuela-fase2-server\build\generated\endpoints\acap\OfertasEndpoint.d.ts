import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { Oferta as Entity } from '../../orm/entities';
import { ChinoSessionDTO, EntityManager, ObjectType } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
export declare class OfertasEndpoint extends MiEscuelaEndpoint<Entity> {
    constructor();
    getAllowGuest(): boolean;
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<any>;
    postInsertAction(value: Entity | ObjectType<Entity>, session: ChinoSessionDTO, req: JRRequest, tx: EntityManager): Promise<Entity | ObjectType<Entity>>;
    postUpdateAction(value: Entity | ObjectType<Entity>, session: ChinoSessionDTO, req: JRRequest, tx: EntityManager): Promise<Entity | ObjectType<Entity>>;
    /**
     * registra un nuevo movimiento de referente en el historico cuando la oferta es creada y
     * registra fecha de alta del referente en el grupo.
     * nueva.
     * @param value
     * @param session
     * @param req
     * @param tx
     */
    registrarHistoricosNuevasOfertas(value: Entity | ObjectType<Entity>, session: ChinoSessionDTO, req: JRRequest, tx: EntityManager): Promise<void>;
    /**
     * registra un nuevo movimiento de referente en el historico cuando la oferta es actualizada y
     * se le han agregado nuevos grupos dentro de la oferta.
     * @param value
     * @param session
     * @param req
     * @param tx
     */
    registrarHistoricosNuevosGrupos(value: Entity | ObjectType<Entity>, session: ChinoSessionDTO, req: JRRequest, tx: EntityManager): Promise<void>;
    preUpdateAction(req: JRRequest, session: ChinoSessionDTO): Promise<any>;
}

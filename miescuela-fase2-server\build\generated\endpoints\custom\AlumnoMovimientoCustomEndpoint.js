"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlumnoMovimientoCustomEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class AlumnoMovimientoCustomEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.AlumnoMovimientoCustomDAO.entity.toLowerCase(), '/public/custom/' + dao_1.AlumnoMovimientoCustomDAO.entity.toLowerCase(), dao_1.AlumnoMovimientoCustomDAO);
    }
    async alumnoMovimientoUserId(req, session) {
        req.body = {
            ...req.body,
            updatedBy: Number(session.user.id),
        };
    }
    async preInsertAction(req, session) {
        await this.alumnoMovimientoUserId(req, session);
    }
    async preUpdateAction(req, session) {
        await this.alumnoMovimientoUserId(req, session);
    }
    getAllowGuest() {
        return true;
    }
}
exports.AlumnoMovimientoCustomEndpoint = AlumnoMovimientoCustomEndpoint;
//# sourceMappingURL=AlumnoMovimientoCustomEndpoint.js.map
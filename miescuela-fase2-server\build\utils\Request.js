"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTTPRequest = exports.METHOD = void 0;
const axios_1 = __importDefault(require("axios"));
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const Log = new chino_sdk_1.ChinoLogManager('REQUEST');
var METHOD;
(function (METHOD) {
    METHOD["POST"] = "post";
    METHOD["GET"] = "get";
    METHOD["PUT"] = "put";
    METHOD["DELETE"] = "delete";
})(METHOD = exports.METHOD || (exports.METHOD = {}));
class HTTPRequest {
    applyHeaders(config, headers) {
        Object.entries(headers).forEach(([key, value]) => {
            if (config.headers) {
                config.headers[key] = value;
            }
        });
        return config;
    }
    applyParams(config, params) {
        Object.entries(params).forEach(([key, value]) => {
            config.params[key] = value;
        });
        return config;
    }
    config(requestOptions) {
        const { headers: RHeaders = {}, params: RParams = {} } = requestOptions;
        let config = {
            headers: {},
            params: {},
        };
        config = this.applyHeaders(config, RHeaders);
        config = this.applyParams(config, RParams);
        return config;
    }
    async run(requestOptions) {
        try {
            const { url, body = {}, method = METHOD.GET } = requestOptions;
            let response;
            switch (method) {
                case METHOD.POST:
                    Log.info(`POST -> ${url}`);
                    response = await axios_1.default.post(url, body, this.config(requestOptions));
                    break;
                case METHOD.PUT:
                    Log.info(`PUT -> ${url}`);
                    response = await axios_1.default.put(url, body, this.config(requestOptions));
                    break;
                case METHOD.GET:
                    Log.info(`GET -> ${url}`);
                    response = await axios_1.default.get(url, this.config(requestOptions));
                    break;
                case METHOD.DELETE:
                    Log.info(`DELETE -> ${url}`);
                    response = await axios_1.default.delete(url, this.config(requestOptions));
                    break;
            }
            return {
                data: response.data,
                status: response.status,
                headers: response.headers,
            };
        }
        catch (error) {
            throw chino_sdk_1.ChinoError.CustomException(error.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
}
exports.HTTPRequest = HTTPRequest;
//# sourceMappingURL=Request.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalificacionesF2CustomEndpoint = void 0;
const just_rpc_1 = require("@phinxlab/just-rpc");
const config_1 = require("../../../app/config");
const entities_1 = require("../../orm/entities");
const typeorm_1 = require("typeorm");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
class CalificacionesF2CustomEndpoint extends config_1.MiEscuelaEndpointCustom {
    constructor(name = 'calificaciones-varios', path = '/calificaciones/calificacionesvarios') {
        super(name, path);
    }
    async exec(req, manager, res) {
        try {
            switch (req.method) {
                case 'GET': {
                    const query = req.query;
                    const { inTab, seccion, alumnos, limit = 50, offset } = query || {};
                    const calificaciones = await manager
                        .getRepository(entities_1.Calificaciones)
                        .find({
                        where: {
                            ...(Boolean(alumnos) && {
                                alumnoMovimiento: (0, typeorm_1.In)(alumnos.split(',')),
                            }),
                            ...(Boolean(seccion) && {
                                alumnoMovimiento: {
                                    seccion: {
                                        idSeccion: seccion,
                                    },
                                },
                            }),
                        },
                        take: limit,
                    });
                    return calificaciones;
                }
                default:
                    throw chino_sdk_1.ChinoError.CustomException('METHOD NO ACCEPTABLE', chino_sdk_1.STATUS_CODE.NOT_IMPLEMENTED);
            }
        }
        catch (e) {
            console.log('error', e);
            // TODO NOT CHANGE, LIBBY NOT RECOGNIZE OTHER STATUS
            throw chino_sdk_1.ChinoError.CustomException(e.message, chino_sdk_1.STATUS_CODE.BAD_REQUEST);
        }
    }
    configure() {
        this.allowGuest = true;
        this.addMethod(just_rpc_1.JRMethod.GET);
    }
}
exports.CalificacionesF2CustomEndpoint = CalificacionesF2CustomEndpoint;
//# sourceMappingURL=CalificacionesCustomEndpoint.js.map
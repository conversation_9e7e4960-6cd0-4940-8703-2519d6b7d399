import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { OfertaGrupo as Entity } from '../../orm/entities';
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
export declare class OfertasGruposEndpoint extends MiEscuelaEndpoint<Entity> {
    constructor();
    getAllowGuest(): boolean;
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<any>;
    preUpdateAction(req: JRRequest, session: ChinoSessionDTO): Promise<any>;
}

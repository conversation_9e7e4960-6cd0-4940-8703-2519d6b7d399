"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PIPEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const UploadFile_1 = require("../../../utils/UploadFile");
const Log = chino_sdk_1.ChinoLogManager.create('PIP Endpoint');
const upload = (0, UploadFile_1.createValidation)([
    UploadFile_1.knownTypes['application/pdf'],
    UploadFile_1.knownTypes['application/msword'],
    UploadFile_1.knownTypes['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    UploadFile_1.knownTypes['image/jpeg'],
    UploadFile_1.knownTypes['image/png'],
    UploadFile_1.knownTypes['image/webp'],
], 10 * 1024 * 1024);
class PIPEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PIPDAO.entity.toLowerCase(), '/calificaciones/' + dao_1.PIPDAO.entity.toLowerCase(), dao_1.PIPDAO);
    }
    getAllowGuest() {
        return false;
    }
    async handleInformes(req, session) {
        const t = req.body;
        const processAdjunto = async (adjunto) => {
            if (adjunto.base64) {
                const result = await upload(adjunto.filename, adjunto.base64, adjunto.mimeType);
                if (result === null)
                    throw 'Error al subir los informes.';
                // Reemplaza el contenido del adjunto con la URL
                return {
                    url: result.webViewLink,
                    filename: adjunto.filename,
                    idAdjunto: adjunto.idAdjunto,
                };
            }
            return adjunto;
        };
        for (const tabKey in t.data) {
            const tabData = t.data[tabKey];
            for (const fieldKey in tabData) {
                const fieldData = tabData[fieldKey];
                if (fieldData &&
                    typeof fieldData === 'object' &&
                    'filename' in fieldData) {
                    t.data[tabKey][fieldKey] = await processAdjunto(fieldData);
                }
            }
        }
    }
    async preUpdateAction(req, session) {
        return await this.handleInformes(req, session);
    }
    async preInsertAction(req, session) {
        const t = req.body;
        return await this.handleInformes(req, session);
    }
}
exports.PIPEndpoint = PIPEndpoint;
//# sourceMappingURL=PIPEndpoint.js.map
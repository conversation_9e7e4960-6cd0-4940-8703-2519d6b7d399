"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjetivosBloquesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.ObjetivosBloquesDAO;
class ObjetivosBloquesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.objetivosbloques', '/orientaciones/objetivosbloques', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ObjetivosBloquesEndpoint = ObjetivosBloquesEndpoint;
//# sourceMappingURL=ObjetivosBloquesEndpoint.js.map
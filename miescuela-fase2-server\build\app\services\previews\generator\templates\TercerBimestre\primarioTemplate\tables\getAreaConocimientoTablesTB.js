"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const getAreaConocimientoTableTB = ({ columns, nombreSeccion, espacioCurricular, descripcionTipoPeriodo, primario, }) => {
    return `
        <div class="table-title">
          <div class="table-title-container">
            <div>
                ${nombreSeccion} ${descripcionTipoPeriodo}
            </div>
          </div>
          <div class="practicas-container">
            <div style="font-weight: bold; padding-bottom: 10px; padding-top: 5px;">
                ${espacioCurricular}
            </div>
            <div style="padding-bottom: 10px;">
                ${columns.find((e) => e.name === 'Indicadores').value[0]}
            </div>
          </div>

        <table class="tabla">
        ${(() => {
        var _a;
        let rows = '';
        const normalizedLabels = {
            Desempenio: 'Desempeño',
            'Aspectos fortalecer': 'Aspectos a fortalecer',
            Ppi: 'PPI',
        };
        (_a = columns === null || columns === void 0 ? void 0 : columns.sort((a, b) => a.name.localeCompare(b.name))) === null || _a === void 0 ? void 0 : _a.forEach((column) => {
            var _a;
            if (column.name !== 'Indicadores') {
                rows += ` <tr>
                          <td >${(_a = normalizedLabels[column.name]) !== null && _a !== void 0 ? _a : column.name}</td>
                          <td style="width: 200%;">${column.value}</td>
                      </tr>`;
            }
        });
        return rows;
    })()}
        </table>
    </div>`;
};
exports.default = (areasConocimiento) => `
<style>
    * {
        font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
    }
    td, th{
        border: 1px solid black;
        border-left: 0px;
        padding-left: 20px;
        padding-top: 10px;
        padding-bottom: 10px;
        width: 50%;
        font-size: 9px;
        word-wrap: break-word;
    }
    .page-container{
        margin: auto;
        line-height: 17px;
    }
    .tabla{
        caption-side: bottom;
        margin-left: auto;
        margin-right: auto;
        border-collapse: collapse;
        min-height: 24%;
        width: 100%;
        flex-flow: column;
        align-items: center;
        border: 2px solid black;
        border-right: 2px solid black;
        border-bottom: 2px solid black;
        border-top: 0px;
        table-layout: fixed;
    }
    @page {
      size: A4;
      margin: 0;
    }
    @media print {
      .page-container
      {
        page-break-after: always;
      }
      .table-title{
        page-break.inside: avoid;
      }
    }
</style>
<div class="page-container">
    ${(() => {
    let rows = '';
    areasConocimiento.forEach((areaConocimiento) => {
        if (areasConocimiento.name !== 'Indicadores') {
            rows += getAreaConocimientoTableTB(areaConocimiento);
        }
    });
    return rows;
})()}
  </div>
`;
//# sourceMappingURL=getAreaConocimientoTablesTB.js.map
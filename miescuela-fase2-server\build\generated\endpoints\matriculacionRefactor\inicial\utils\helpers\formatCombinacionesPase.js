"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatCombinacionesPase = void 0;
const utils_1 = require("../utils");
const helpers_1 = require("./helpers");
function removerDuplicadosPorNivelYEstadoPase(arr) {
    const seen = new Set();
    const resultado = [];
    for (const item of arr) {
        const clave = `${item.anio.idAnio}-${item.estadoPaseAnio.idEstadoPaseAnio}`;
        if (!seen.has(clave)) {
            seen.add(clave);
            resultado.push(item);
        }
    }
    return resultado;
}
const formatCombinacionesPase = async (values, secciones, anioOrigen, turnoOrigen) => {
    var _a;
    const motivos = await (0, utils_1.obtenerMotivos)();
    const grouped = {};
    const combinacionesInicial = removerDuplicadosPorNivelYEstadoPase(values);
    const combinacionesInicialSorted = (0, helpers_1.sortCombinacionesPase)(combinacionesInicial);
    for (const combinacion of combinacionesInicialSorted) {
        const { anio: { idAnio, numeroAnio, descripcionAnio, nivel }, estadoPaseAnio, } = combinacion;
        if (!grouped[idAnio]) {
            grouped[idAnio] = {
                idAnio,
                numeroAnio,
                descripcionAnio,
                idNivel: nivel.idNivel,
                turno: { idTurno: turnoOrigen },
                combinaciones: [],
            };
        }
        const aniosDestino = (0, helpers_1.getAniosDestino)(anioOrigen, estadoPaseAnio.idEstadoPaseAnio);
        const seccionesDestino = (0, helpers_1.getSeccionesParaAnio)(secciones, aniosDestino);
        const seccionesEspeciales = (0, helpers_1.getSeccionesEspeciales)(estadoPaseAnio.idEstadoPaseAnio, motivos);
        const color = (0, helpers_1.getColorByEstado)(estadoPaseAnio.idEstadoPaseAnio);
        grouped[idAnio].combinaciones.push({
            idCombinacion: combinacion.idCombinacion,
            idEstadoPaseAnio: estadoPaseAnio.idEstadoPaseAnio,
            descripcionEstadoPaseAnio: (_a = estadoPaseAnio.descripcionEstadoPaseAnio) !== null && _a !== void 0 ? _a : '',
            color,
            secciones: [...seccionesDestino, ...seccionesEspeciales],
        });
    }
    return Object.values(grouped);
};
exports.formatCombinacionesPase = formatCombinacionesPase;
//# sourceMappingURL=formatCombinacionesPase.js.map
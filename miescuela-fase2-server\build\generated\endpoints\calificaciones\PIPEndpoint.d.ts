import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { PIP } from '../../orm/entities';
export declare class PIPEndpoint extends MiEscuelaEndpoint<PIP> {
    constructor();
    getAllowGuest(): boolean;
    handleInformes(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
    preUpdateAction(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
}

import {
  type CombinacionesPase,
  type EstadoProyeccionMotivoCombinacion
} from '@miesc/lib/database/models'
import { type AccionPase } from '@miesc/lib/database/models/AccionPase'
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
  startTransition
} from 'react'
import {
  type AlumnoTableRow,
  type PaseDeAnioOption
} from '../../../../promocion/types/types'
import { type UseAlumnoRowProps } from './types'
import {
  ColorEstadoPaseAnio,
  ESTADO_PASE_ANIO_COLOR
} from '@miesc/pagesV2/matriculacion/MatriculacionRefactor/types/estadoPaseAnio'
import { ESTADO_PASE_ANIO } from '@miesc/const/estadosPase'
import { PROYECCION_ESTADO_TIPO } from '@miesc/const/proyeccionEstadoTipo'
import { useRolesContext } from '@miesc/contexts/Role/RoleContext'
import { ESTADO_SECCION_PROYECTADA_CODES } from '@miesc/const/estadoSeccionProyectada'
import {
  isEstadoPaseAnioDeshabilitadoFn,
  ordenarAccionesPorTurno
} from './helpers'
import {
  getSelectOptionsBasedOnCombinations,
  findCombinacionByCurrentSelection
} from '../../utils/combinaciones'
import { useFiltroMateriasPendientes } from '../useFiltroMateriasPendientes'
import { parsePaseDeAnioDescripcion } from '../../utils/parsePaseDeAnioDescripcion'

export default function useAlumnoRow(props: UseAlumnoRowProps) {
  const {
    alumnosIds,
    proyeccion,
    combinacionesPase,
    allEstadoPaseAnio,
    proyeccionEstado,
    selectedSeccion,
    savedAccionPase,
    savedMotivo,
    savedEstadoPaseAnio,
    updateAlumnosProyectadosToSave,
    materiasPendientes
  } = props

  const {
    selectedRole: { nivel, rol }
  } = useRolesContext()

  const [accionPaseOptionsFiltered, setAccionPaseOptionsFiltered] = useState<
  AccionPase[]
  >([])
  const [motivosOptionsFiltered, setMotivosOptionsFiltered] = useState<
  EstadoProyeccionMotivoCombinacion[]
  >([])
  const [estadoPaseAnioSelected, setEstadoPaseAnioSelected] = useState<
  string | null
  >(null)
  const [accionPaseSelected, setAccionPaseSelected] = useState<string | null>(
    null
  )
  const [motivoSelected, setMotivoSelected] = useState<string | null>(null)
  const [motivoDisabled, setMotivoDisabled] = useState<boolean>(true)
  const [previousEstadoPaseAnio, setPreviousEstadoPaseAnio] = useState<
  string | null
  >(null)

  const pendientePorReaperturaSeccion = useMemo(
    () =>
      !proyeccion &&
      selectedSeccion?.estadoSeccionProyectada?.code ===
      ESTADO_SECCION_PROYECTADA_CODES.PARCIAL,
    [proyeccion, selectedSeccion]
  )

  const esSegundoPaso = useMemo(() => {
    return (
      proyeccionEstado[0]?.proyeccionEstadoTipo.idProyeccionEstadoTipo ===
        PROYECCION_ESTADO_TIPO.VACANTES_INFORMADAS ||
      proyeccionEstado[0]?.proyeccionEstadoTipo.idProyeccionEstadoTipo ===
        PROYECCION_ESTADO_TIPO.PUEDE_EFECTIVIZAR
    )
  }, [proyeccionEstado])

  const filtroMateriasPendientes = useFiltroMateriasPendientes({
    materiasPendientes: materiasPendientes || {
      pendientesCicloAnteriores: 0,
      pendientesCicloActual: 0
    },
    esSegundoPaso,
    estadosPaseAnio: allEstadoPaseAnio,
    combinacionesPase,
    dependenciaFuncional:
      selectedSeccion?.localizacion?.establecimiento?.dependenciaFuncional,
    anio: selectedSeccion?.anio,
    estadoPaseAnioAnterior: proyeccion?.estadoPaseAnio || null,
    rolId: Number(rol.id)
  })

  // Opciones para el selector "Pase de año" derivadas de combinaciones
  const allEstadoPaseAnioOptions: PaseDeAnioOption[] = useMemo(() => {
    if (allEstadoPaseAnio.length === 0) return []

    const promocionoDirecto =
      proyeccion?.estadoPaseAnio.idEstadoPaseAnio ===
      ESTADO_PASE_ANIO.PROMOCION_DIRECTA

    let estadosPaseFinales =
      esSegundoPaso && !promocionoDirecto
        ? allEstadoPaseAnio.filter(
          (estadoPaseAnio) =>
            estadoPaseAnio.idEstadoPaseAnio !==
              ESTADO_PASE_ANIO.PROMOCION_DIRECTA
        )
        : allEstadoPaseAnio

    if (materiasPendientes) {
      estadosPaseFinales =
        filtroMateriasPendientes.estadosPaseAnioFiltrados.filter((estado) =>
          estadosPaseFinales.some(
            (e) => e.idEstadoPaseAnio === estado.idEstadoPaseAnio
          )
        )
    }

    return estadosPaseFinales.map((estadoPaseAnio) => ({
      idEstadoPaseAnio: estadoPaseAnio.idEstadoPaseAnio,
      value: estadoPaseAnio.idEstadoPaseAnio.toString(),
      label: parsePaseDeAnioDescripcion(estadoPaseAnio.descripcionEstadoPaseAnio),
      color:
        estadoPaseAnio.idEstadoPaseAnio in ESTADO_PASE_ANIO_COLOR
          ? ESTADO_PASE_ANIO_COLOR[estadoPaseAnio.idEstadoPaseAnio]
          : ColorEstadoPaseAnio.DEFAULT
    }))
  }, [
    allEstadoPaseAnio,
    proyeccion,
    esSegundoPaso,
    materiasPendientes,
    filtroMateriasPendientes
  ])

  // Cache de combinaciones costosas por campo+valor
  const combinacionesCacheRef = useRef<
  Map<string, ReturnType<typeof getSelectOptionsBasedOnCombinations>>
  >(new Map())

  const getCombinacionesCached = useCallback(
    (campo: 'estadoPaseAnio' | 'accionPase', valor: number) => {
      const key = `${campo}:${valor}`
      const cached = combinacionesCacheRef.current.get(key)
      if (cached) return cached
      const result = getSelectOptionsBasedOnCombinations({
        combinacionesPase,
        filtro: { campo, valor }
      })
      combinacionesCacheRef.current.set(key, result)
      return result
    },
    [combinacionesPase]
  )

  // Clave estable para inicialización: evita re-ejecutar setInitialValues sin cambios reales
  const keyInicializacion = useMemo(() => {
    const idsOpcionesPaseDeAnio = (allEstadoPaseAnioOptions || []).map(
      (o) => o.idEstadoPaseAnio
    )

    return JSON.stringify({
      savedEstadoPaseAnio: savedEstadoPaseAnio ?? null,
      savedAccionPase: savedAccionPase ?? null,
      savedMotivo: savedMotivo ?? null,
      pendiente: !!pendientePorReaperturaSeccion,
      idsOpcionesPaseDeAnio
    })
  }, [
    allEstadoPaseAnioOptions,
    pendientePorReaperturaSeccion,
    savedAccionPase,
    savedEstadoPaseAnio,
    savedMotivo
  ])

  const keyInicializacionRef = useRef<string | null>(null)

  // Helper: arma los valores a actualizar en el alumno para "acción de pase"
  const buildAccionPaseValues = useCallback(
    (
      accionPase: string | null,
      opcionesAccion: AccionPase[],
      idEstadoPaseAnio?: string
    ): Partial<AlumnoTableRow> => {
      const valuesToUpdate: Partial<AlumnoTableRow> = {
        accionPase: accionPase
          ? opcionesAccion.find(
            (o) => o.idAccionPase.toString() === accionPase.toString()
          )
          : null
      }
      if (idEstadoPaseAnio) {
        valuesToUpdate.estadoPaseAnio = allEstadoPaseAnio.find(
          (e) => e.idEstadoPaseAnio.toString() === idEstadoPaseAnio
        )
      }
      return valuesToUpdate
    },
    [allEstadoPaseAnio]
  )

  const handleMotivoChange = useCallback(
    (
      motivo: string | null,
      valuesToUpdate?: Partial<AlumnoTableRow>,
      motivos?: EstadoProyeccionMotivoCombinacion[]
    ) => {
      setMotivoSelected(motivo)
      setMotivoDisabled(motivos?.length === 0)

      const newValues = valuesToUpdate ?? {}
      const motivosOptions = motivos ?? motivosOptionsFiltered

      newValues.motivo = motivo
        ? motivosOptions.find(
          (motivoOption) =>
            motivoOption.idEstadoProyeccionMotivo.toString() ===
            motivo.toString()
        )
        : null

      updateAlumnosProyectadosToSave(alumnosIds, newValues)
    },
    [motivosOptionsFiltered, updateAlumnosProyectadosToSave, alumnosIds]
  )

  // Cambio de "Sección a la que pasa"
  const handleAccionPaseChange = useCallback(
    (
      accionPase: string | null,
      idEstadoPaseAnio?: string,
      newAccionPaseOptions?: AccionPase[],
      defaultMotivo?: string
    ) => {
      startTransition(() => {
        setAccionPaseSelected(accionPase)

        const result = getCombinacionesCached('accionPase', Number(accionPase))
        const newMotivos = result.estadoProyeccionMotivo ?? []
        setMotivosOptionsFiltered(newMotivos)

        const opcionesAccion =
          newAccionPaseOptions ?? accionPaseOptionsFiltered
        const values = buildAccionPaseValues(
          accionPase,
          opcionesAccion,
          idEstadoPaseAnio
        )

        handleMotivoChange(defaultMotivo ?? null, values, newMotivos)
      })
    },
    [
      accionPaseOptionsFiltered,
      handleMotivoChange,
      getCombinacionesCached,
      buildAccionPaseValues
    ]
  )

  // Cambio de "Pase de año"
  const handleEstadoPaseAnioChange = useCallback(
    (
      idEstadoPaseAnio: string,
      defaultAccionPase?: string,
      defaultMotivo?: string
    ) => {
      startTransition(() => {
        setEstadoPaseAnioSelected(idEstadoPaseAnio)

        const result = getCombinacionesCached(
          'estadoPaseAnio',
          Number(idEstadoPaseAnio)
        )

        const newAccionesPase = result?.accionPase ?? []
        const accionesPaseOrdenadas = ordenarAccionesPorTurno(
          newAccionesPase,
          selectedSeccion?.turno.descripcionTurno ?? ''
        )

        setAccionPaseOptionsFiltered(accionesPaseOrdenadas)

        handleAccionPaseChange(
          defaultAccionPase ||
            accionesPaseOrdenadas?.[0]?.idAccionPase.toString() ||
            null,
          idEstadoPaseAnio,
          accionesPaseOrdenadas,
          defaultMotivo
        )
      })
    },
    [
      selectedSeccion?.turno.descripcionTurno,
      handleAccionPaseChange,
      getCombinacionesCached
    ]
  )

  // TODO: Función gigante, refactorizar y separar responsabilidades
  const setInitialValuesAndFilters = useCallback(
    (
      combinacionesPase: CombinacionesPase[],
      allEstadoPaseAnio: PaseDeAnioOption[],
      defaultEstadoPaseAnio: string | undefined,
      defaultAccionPase: string | undefined,
      defaultMotivo: string | undefined,
      pendientePorReaperturaSeccion: boolean
    ) => {
      if (pendientePorReaperturaSeccion && !defaultEstadoPaseAnio) {
        setAccionPaseOptionsFiltered([])
        setMotivosOptionsFiltered([])
        setMotivoSelected(null)
        setEstadoPaseAnioSelected(null)
        setAccionPaseSelected(null)
        setMotivoDisabled(true)
        return
      }

      const estadoPaseAnioPorDefecto = (
        defaultEstadoPaseAnio ||
        allEstadoPaseAnio.find(
          (estadoPaseAnio) =>
            estadoPaseAnio.idEstadoPaseAnio ===
            ESTADO_PASE_ANIO.PROMOCION_DIRECTA
        )?.idEstadoPaseAnio ||
        allEstadoPaseAnio?.[0].idEstadoPaseAnio
      )?.toString()

      if (!estadoPaseAnioPorDefecto) return

      const resultAccion = getSelectOptionsBasedOnCombinations({
        combinacionesPase,
        filtro: {
          campo: 'estadoPaseAnio',
          valor: Number(estadoPaseAnioPorDefecto)
        }
      })

      const accionesOptions = resultAccion.accionPase ?? []
      const accionesPaseOrdenadas = ordenarAccionesPorTurno(
        accionesOptions,
        selectedSeccion?.turno.descripcionTurno ?? ''
      )

      const resultMotivos = getSelectOptionsBasedOnCombinations({
        combinacionesPase,
        filtro: {
          campo: 'accionPase',
          valor: Number(
            defaultAccionPase || accionesPaseOrdenadas?.[0]?.idAccionPase
          )
        }
      })

      const motivosOptions = resultMotivos.estadoProyeccionMotivo ?? []

      setAccionPaseOptionsFiltered(accionesPaseOrdenadas)
      setMotivosOptionsFiltered(motivosOptions)
      setMotivoSelected(defaultMotivo ?? null)
      setEstadoPaseAnioSelected(estadoPaseAnioPorDefecto)
      setAccionPaseSelected(
        defaultAccionPase ??
        accionesPaseOrdenadas?.[0]?.idAccionPase.toString() ??
        null
      )
      setMotivoDisabled(motivosOptions?.length === 0)
    },
    [selectedSeccion?.turno.descripcionTurno]
  )

  useEffect(() => {
    if (keyInicializacionRef.current === keyInicializacion) return

    const estadoOriginalProyeccion = proyeccion?.estadoPaseAnio?.idEstadoPaseAnio?.toString()
    const estadoFueModificadoPorReglas = savedEstadoPaseAnio && estadoOriginalProyeccion &&
      savedEstadoPaseAnio !== estadoOriginalProyeccion

    setInitialValuesAndFilters(
      combinacionesPase,
      allEstadoPaseAnioOptions,
      savedEstadoPaseAnio ?? undefined,
      estadoFueModificadoPorReglas ? undefined : (savedAccionPase ?? undefined),
      savedMotivo ?? undefined,
      pendientePorReaperturaSeccion
    )

    keyInicializacionRef.current = keyInicializacion
  }, [
    combinacionesPase,
    allEstadoPaseAnioOptions,
    pendientePorReaperturaSeccion,
    savedEstadoPaseAnio,
    savedAccionPase,
    savedMotivo,
    setInitialValuesAndFilters,
    keyInicializacion,
    proyeccion?.estadoPaseAnio?.idEstadoPaseAnio
  ])

  useEffect(() => {
    if (
      !estadoPaseAnioSelected ||
      estadoPaseAnioSelected === previousEstadoPaseAnio
    ) {
      return
    }

    setPreviousEstadoPaseAnio(estadoPaseAnioSelected)

    if (previousEstadoPaseAnio === null) {
      return
    }

    const estadoId = Number(estadoPaseAnioSelected)
    const isPromocionConApoyo =
      estadoId === ESTADO_PASE_ANIO.CON_PROMOCION_ACOMPANADA
    const isPermanece = estadoId === ESTADO_PASE_ANIO.PERMANECE
    const shouldClearFields = !isPromocionConApoyo && !isPermanece

    if (shouldClearFields) {
      updateAlumnosProyectadosToSave(
        alumnosIds,
        {
          adjunto: '',
          observacion: ''
        },
        false
      )
    }
  }, [
    estadoPaseAnioSelected,
    previousEstadoPaseAnio,
    updateAlumnosProyectadosToSave,
    alumnosIds
  ])

  const deshabilitadoPorSegundoPaso: boolean = useMemo(() => {
    if (!estadoPaseAnioSelected || !proyeccion) return false

    const combinacionesEstadoPaseAnioActual = findCombinacionByCurrentSelection(
      {
        combinacionesPase,
        estadoPaseAnioSelected
      }
    )

    if (!combinacionesEstadoPaseAnioActual?.length) return false

    const tieneDeshabilitado = combinacionesEstadoPaseAnioActual.some(
      ({ deshabilitadoCambioAccionPase }) => deshabilitadoCambioAccionPase
    )

    const isEstadoPaseAnioDeshabilitado = isEstadoPaseAnioDeshabilitadoFn(
      proyeccion,
      nivel.idNivel
    )

    return tieneDeshabilitado && isEstadoPaseAnioDeshabilitado
  }, [combinacionesPase, estadoPaseAnioSelected, proyeccion, nivel.idNivel])

  return {
    deshabilitadoPorSegundoPaso,
    motivoDisabled,
    estadoPaseAnioSelected,
    allEstadoPaseAnioOptions,
    accionPaseSelected,
    accionPaseOptionsFiltered,
    motivoSelected,
    motivosOptionsFiltered,
    pendientePorReaperturaSeccion,
    handleEstadoPaseAnioChange,
    handleAccionPaseChange,
    handleMotivoChange
  }
}

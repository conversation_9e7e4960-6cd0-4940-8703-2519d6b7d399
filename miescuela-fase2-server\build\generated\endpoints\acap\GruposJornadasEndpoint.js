"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GruposJornadasEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const helpers_1 = require("../../../utils/helpers");
class GruposJornadasEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('acap.gruposjornadas', '/acap/gruposjornadas', dao_1.GruposJornadasDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
}
exports.GruposJornadasEndpoint = GruposJornadasEndpoint;
//# sourceMappingURL=GruposJornadasEndpoint.js.map
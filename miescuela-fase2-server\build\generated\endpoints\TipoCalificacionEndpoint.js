"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoCalificacionEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class TipoCalificacionEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.TipoCalificacionDAO.entity.toLowerCase(), '/public/' + dao_1.TipoCalificacionDAO.entity.toLowerCase(), dao_1.TipoCalificacionDAO);
    }
    getMethods() {
        return ['GET', 'POST', 'PUT', 'DELETE'];
    }
    getAllowGuest() {
        return true;
    }
}
exports.TipoCalificacionEndpoint = TipoCalificacionEndpoint;
//# sourceMappingURL=TipoCalificacionEndpoint.js.map
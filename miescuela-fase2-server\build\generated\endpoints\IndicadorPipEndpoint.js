"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicadorPipEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class IndicadorPipEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.IndicadorPipDAO.entity.toLowerCase(), '/calificaciones/' + dao_1.IndicadorPipDAO.entity.toLowerCase(), dao_1.IndicadorPipDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.IndicadorPipEndpoint = IndicadorPipEndpoint;
//# sourceMappingURL=IndicadorPipEndpoint.js.map
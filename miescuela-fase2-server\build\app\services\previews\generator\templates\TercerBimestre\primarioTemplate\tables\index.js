"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getObservacionesTableTB = exports.getAreaConocimientoTableTB = exports.getAspectosGeneralesTableTB = void 0;
var getAspectosGeneralesTableTB_1 = require("./getAspectosGeneralesTableTB");
Object.defineProperty(exports, "getAspectosGeneralesTableTB", { enumerable: true, get: function () { return __importDefault(getAspectosGeneralesTableTB_1).default; } });
var getAreaConocimientoTablesTB_1 = require("./getAreaConocimientoTablesTB");
Object.defineProperty(exports, "getAreaConocimientoTableTB", { enumerable: true, get: function () { return __importDefault(getAreaConocimientoTablesTB_1).default; } });
var getObservacionesTableTB_1 = require("./getObservacionesTableTB");
Object.defineProperty(exports, "getObservacionesTableTB", { enumerable: true, get: function () { return __importDefault(getObservacionesTableTB_1).default; } });
//# sourceMappingURL=index.js.map
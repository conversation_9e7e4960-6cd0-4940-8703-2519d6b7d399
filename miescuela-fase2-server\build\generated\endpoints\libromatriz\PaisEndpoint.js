"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaisEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PaisEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PaisDAO.entity.toLowerCase(), '/paises-provincias', dao_1.PaisDAO);
    }
    async postSelectAction(values, session, req) {
        let provinciasArgentina = await dao_1.ProvinciaDAO.query()
            .equals('pais.descripcionPais', 'ARGENTINA')
            .run(chino_sdk_1.ChinoManager.getManager());
        provinciasArgentina = provinciasArgentina.map((provincia) => ({
            id: provincia.idProvincia,
            descripcion: provincia.nombreProvincia,
        }));
        return values.map((pais) => ({
            id: pais.idPais,
            descripcion: pais.descripcionPais,
            provincias: pais.descripcionPais.match(/.*argentina*./i) !== null
                ? provinciasArgentina
                : [],
        }));
    }
    getAllowGuest() {
        return false;
    }
}
exports.PaisEndpoint = PaisEndpoint;
//# sourceMappingURL=PaisEndpoint.js.map
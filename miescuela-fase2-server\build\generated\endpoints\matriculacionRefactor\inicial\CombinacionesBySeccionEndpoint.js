"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CombinacionesBySeccionEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const CombinacionesPaseDAO_1 = require("../../../orm/dao/matriculacionRefactor/inicial/CombinacionesPaseDAO");
const MiEscuelaEndpointV2_1 = require("../../../../app/config/endpoint/MiEscuelaEndpointV2");
const dao_1 = require("../../../orm/dao");
const utils_1 = require("./utils/utils");
const formatCombinacionesPase_1 = require("./utils/helpers/formatCombinacionesPase");
const CombinacionesPaseService_1 = require("../../../../app/business/flows/matriculacion/matriculacionRefactor/inicial/autocompletadoService/CombinacionesPaseService");
class CombinacionesBySeccionEndpoint extends MiEscuelaEndpointV2_1.MiEscuelaEndpointV2 {
    constructor() {
        super('combinaciones_pase_seccion', '/matriculacion/combinaciones_pase_seccion', dao_1.SeccionV2DAO);
    }
    async postSelectAction(values, session, req) {
        var _a, _b, _c, _d, _e;
        if (values.length > 1)
            throw new Error('Only one seccion is allowed');
        if (values.length === 0)
            throw new Error('No secciones found');
        const context = chino_sdk_1.ChinoContext.fromSession(session);
        const tx = chino_sdk_1.ChinoManager.getManager();
        const idAnio = (_a = values[0].anio) === null || _a === void 0 ? void 0 : _a.idAnio;
        const idTurno = (_b = values[0].turno) === null || _b === void 0 ? void 0 : _b.idTurno;
        const idLocalizacion = (_c = values[0].localizacion) === null || _c === void 0 ? void 0 : _c.idLocalizacion;
        const idNivel = (_d = values[0].nivel) === null || _d === void 0 ? void 0 : _d.idNivel;
        const anioCicloLectivo = (_e = values[0].cicloLectivo) === null || _e === void 0 ? void 0 : _e.anio;
        if (![idAnio, idTurno, idLocalizacion, idNivel, anioCicloLectivo].every(Boolean))
            throw new Error('Faltan parametros en la sección');
        const aniosRelevantes = (0, utils_1.getAniosRelevantes)([idAnio]);
        const anioSiguienteCicloLectivo = Number(anioCicloLectivo) + 1;
        try {
            const seccionesPromises = dao_1.SeccionV2DAO.query('combinaciones-pase-endpoint')
                .equals('id_localizacion', idLocalizacion)
                .and()
                .equals('id_nivel', idNivel)
                .and()
                .equals('cicloLectivo.anio', anioSiguienteCicloLectivo)
                .and()
                .in('id_anio', aniosRelevantes)
                .setContext(context)
                .run(tx);
            const combinacionesPromises = CombinacionesPaseDAO_1.CombinacionesPaseDAO.query('default')
                .equals('id_anio', idAnio)
                .setContext(context)
                .run(tx);
            const [secciones, combinaciones] = await Promise.all([
                seccionesPromises,
                combinacionesPromises,
            ]);
            const response = await (0, formatCombinacionesPase_1.formatCombinacionesPase)(combinaciones, secciones, idAnio, idTurno);
            const processedResponse = CombinacionesPaseService_1.CombinacionesPaseService.procesarPreseleccionesRefactored(response);
            return processedResponse;
        }
        catch (error) {
            console.error(error);
            throw error;
        }
    }
}
exports.CombinacionesBySeccionEndpoint = CombinacionesBySeccionEndpoint;
//# sourceMappingURL=CombinacionesBySeccionEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsuarioRolEstablecimientoNTEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class UsuarioRolEstablecimientoNTEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.UsuarioRolEstablecimientoNTDAO.entity.toLowerCase(), '/public/' + dao_1.UsuarioRolEstablecimientoNTDAO.entity.toLowerCase(), dao_1.UsuarioRolEstablecimientoNTDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.UsuarioRolEstablecimientoNTEndpoint = UsuarioRolEstablecimientoNTEndpoint;
//# sourceMappingURL=UsuarioRolEstablecimientoNTEndpoint.js.map
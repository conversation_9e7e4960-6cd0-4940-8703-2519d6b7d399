"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreviewsEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PreviewsEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PreviewsDAO.entity.toLowerCase(), '/documents/' + dao_1.PreviewsDAO.entity.toLowerCase(), dao_1.PreviewsDAO);
    }
    getAllowGuest() {
        return true;
    }
}
exports.PreviewsEndpoint = PreviewsEndpoint;
//# sourceMappingURL=PreviewsEndpoint.js.map
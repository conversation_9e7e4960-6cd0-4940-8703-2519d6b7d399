"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolStatusEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class RolStatusEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.RolStatusDAO.entity.toLowerCase(), '/public/' + dao_1.RolStatusDAO.entity.toLowerCase(), dao_1.RolStatusDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.RolStatusEndpoint = RolStatusEndpoint;
//# sourceMappingURL=RolStatusEndpoint.js.map
import { JRRequest, JRResponse } from '@phinxlab/just-rpc';
import { EntityManager } from 'typeorm';
import { MiEscuelaEndpointCustom } from '../../../app/config';
import type { AutoevaluacionPostResponse } from '../../../app/business/flows/acap/GuardarAutoevaluacion';
export declare class GuardarAutoevaluacionEndpoint extends MiEscuelaEndpointCustom {
    exec(req: JRRe<PERSON>, manager: EntityManager, res: JRResponse): Promise<AutoevaluacionPostResponse>;
    configure(): void;
    constructor(name?: string, path?: string);
}

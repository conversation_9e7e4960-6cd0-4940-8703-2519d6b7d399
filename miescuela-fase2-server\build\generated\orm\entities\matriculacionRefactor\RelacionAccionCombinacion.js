"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RelacionAccionCombinacion = void 0;
const typeorm_1 = require("typeorm");
const AccionPase_1 = require("./AccionPase");
let RelacionAccionCombinacion = class RelacionAccionCombinacion {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        type: 'integer',
        name: 'relacion_accion_combinacion_id',
    }),
    __metadata("design:type", Number)
], RelacionAccionCombinacion.prototype, "idRelacionCombinacion", void 0);
__decorate([
    (0, typeorm_1.Column)('integer', { name: 'id_combinacion' }),
    __metadata("design:type", Number)
], RelacionAccionCombinacion.prototype, "idCombinacion", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AccionPase_1.AccionPase),
    (0, typeorm_1.JoinColumn)([
        { name: 'id_accion_pase', referencedColumnName: 'idAccionPase' },
    ]),
    __metadata("design:type", AccionPase_1.AccionPase)
], RelacionAccionCombinacion.prototype, "accionPase", void 0);
RelacionAccionCombinacion = __decorate([
    (0, typeorm_1.Index)('relacion_accion_combinacion_pkey', ['idRelacionCombinacion'], {
        unique: true,
    }),
    (0, typeorm_1.Entity)('relacion_accion_combinacion', { schema: 'public' })
], RelacionAccionCombinacion);
exports.RelacionAccionCombinacion = RelacionAccionCombinacion;
//# sourceMappingURL=RelacionAccionCombinacion.js.map
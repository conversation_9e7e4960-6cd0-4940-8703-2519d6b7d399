import { Cuentas } from '../Cuentas';
import { Alumno } from '../Alumno';
import { TipoInformes } from '../TipoInformes';
import { Previews } from './Previews';
import { Localizacion } from '../Localizacion';
export interface InformesHistory {
    informeHistoryId: number;
    createdAt: Date;
    localizacion: Localizacion;
    nombre: string;
    createdBy: Cuentas;
    alumno: Alumno;
    tipoInforme: TipoInformes;
    preview: Previews;
    seenBy: Cuentas;
}

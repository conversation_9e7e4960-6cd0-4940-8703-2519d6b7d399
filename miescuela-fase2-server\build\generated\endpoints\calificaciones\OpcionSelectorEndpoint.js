"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpcionSelectorEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class OpcionSelectorEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.OpcionSelectorDAO.entity.toLowerCase(), '/calificaciones/opciones_selectores', dao_1.OpcionSelectorDAO);
    }
    getAllowGuest() {
        return true;
    }
}
exports.OpcionSelectorEndpoint = OpcionSelectorEndpoint;
//# sourceMappingURL=OpcionSelectorEndpoint.js.map
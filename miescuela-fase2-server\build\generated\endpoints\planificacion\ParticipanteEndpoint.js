"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipanteEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class ParticipanteEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.ParticipanteDAO.entity.toLowerCase(), '/planificacion/' + dao_1.ParticipanteDAO.entity.toLowerCase(), dao_1.ParticipanteDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.ParticipanteEndpoint = ParticipanteEndpoint;
//# sourceMappingURL=ParticipanteEndpoint.js.map
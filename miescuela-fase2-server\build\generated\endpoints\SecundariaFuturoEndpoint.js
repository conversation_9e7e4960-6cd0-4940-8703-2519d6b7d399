"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecundariaFuturoEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class SecundariaFuturoEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.SecundariaFuturoDAO.entity.toLowerCase(), '/public/' + dao_1.SecundariaFuturoDAO.entity.toLowerCase(), dao_1.SecundariaFuturoDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.SecundariaFuturoEndpoint = SecundariaFuturoEndpoint;
//# sourceMappingURL=SecundariaFuturoEndpoint.js.map
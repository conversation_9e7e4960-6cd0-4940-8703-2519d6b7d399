"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
const DAO = dao_1.orientaciones.NodosDAO;
class NodosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super('orientaciones.nodos', '/orientaciones/nodos', DAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.NodosEndpoint = NodosEndpoint;
//# sourceMappingURL=NodosEndpoint.js.map
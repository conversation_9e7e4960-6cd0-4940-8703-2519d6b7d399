"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AltaMasivaAlumnoEscuelasEspeciales = void 0;
const config_1 = require("../../../app/config");
const dao_1 = require("../../orm/dao");
class AltaMasivaAlumnoEscuelasEspeciales extends config_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.AltaMasivaAlumnoEscuelasEspecialesDAO.entity.toLowerCase(), '/public/alta_masiva_alumno_escuelas_especiales', dao_1.AltaMasivaAlumnoEscuelasEspecialesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.AltaMasivaAlumnoEscuelasEspeciales = AltaMasivaAlumnoEscuelasEspeciales;
//# sourceMappingURL=AltaMasivaAlumnoEscuelasEspecialesEndpoint.js.map
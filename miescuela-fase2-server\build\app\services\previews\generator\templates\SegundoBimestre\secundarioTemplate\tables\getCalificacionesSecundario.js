"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCalificacionesSecundario = void 0;
const getCalificacionesSecundario = (tipoPeriodo, calificaciones) => {
    return `
  <table cellspacing="0" cellpadding="0">
    <thead>
      <tr class="bb-1">
        <th class="tac fs-10 ff-calibri va-middle ws-normal bold bgc-green bt-1 bl-1 br-1 bb-1" rowspan="3"
          style="width:303px;">Espacio curricular</th>
        <th class="tac fs-10 ff-calibri va-middle ws-normal bold bgc-green bt-1 br-1 bb-1" rowspan="3"
          style="width:100px;">PPI</th>
        <th class="tac fs-10 ff-calibri va-middle ws-normal bold bt-1 br-1 bgc-green" colspan="4" style="width:512px;">
                 ${tipoPeriodo === 'Tercer Bimestre' ||
        tipoPeriodo === 'Cuarto Bimestre'
        ? 'SEGUNDO CUATRIMESTRE'
        : 'PRIMER CUATRIMESTRE'}
        </th>
      </tr>
      <tr class="bb-1">
        <th class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bgc-green" colspan="4">${tipoPeriodo}
        </th>
      </tr>
      <tr class="bb-1">
        <th class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bb-1" colspan="3">Valoración</th>
        <th class="tac fs-10 ff-calibri va-middle ws-normal bold br-1 bt-1 bb-1 ">Calificación</th>
      </tr>
    </thead>
    <tbody>
      ${(() => {
        let rows = '';
        const sortedCalificaciones = calificaciones.sort((a, b) => {
            return a.espacioCurricular.localeCompare(b.espacioCurricular);
        });
        sortedCalificaciones.length > 0
            ? sortedCalificaciones.forEach(({ ppi, participacion, valoracion, calificacion, espacioCurricular, }) => {
                if (![ppi, participacion, valoracion, calificacion].every((value) => value === '')) {
                    rows += `
            <tr>
                <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr">
                    ${espacioCurricular !== null && espacioCurricular !== void 0 ? espacioCurricular : ''}
                </td>
                <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tac " dir="ltr">
                    ${ppi === '' ? '' : ppi ? 'Si' : 'No'}
                </td>
                <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 overflow-wrap tac" dir="ltr" colspan="3">
                     ${valoracion !== null && valoracion !== void 0 ? valoracion : ''}
                </td>
                <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-nowrap tac " dir="ltr">
                    ${calificacion !== null && calificacion !== void 0 ? calificacion : ''}
                </td>
            </tr>`;
                }
            })
            : (rows += ` <tr>
          <td class="cell bl-1 br-1 bold ff-calibri fs-11 tal bb-1 va-middle ws-normal" dir="ltr">
            &nbsp;
          </td>
          <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tac " dir="ltr">
            &nbsp;
          </td>
          <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tac" dir="ltr" colspan="3">
            &nbsp;
          </td>
          <td class="cell br-1 fs-11 va-middle ff-calibri bb-1 ws-normal tac " dir="ltr">
            &nbsp;
          </td>
      </tr>`);
        return rows;
    })()}
    </tbody>
  </table>
`;
};
exports.getCalificacionesSecundario = getCalificacionesSecundario;
//# sourceMappingURL=getCalificacionesSecundario.js.map
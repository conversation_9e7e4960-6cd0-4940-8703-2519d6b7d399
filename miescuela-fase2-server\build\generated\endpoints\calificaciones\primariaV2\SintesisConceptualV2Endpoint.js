"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SintesisConceptualEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../../app/config/endpoint/MiEscuelaEndpoints");
const helpers_1 = require("../../../../utils/helpers");
const SintesisConceptualV2DAO_1 = require("../../../orm/dao/calificaciones/SintesisConceptualV2DAO");
class SintesisConceptualEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(SintesisConceptualV2DAO_1.SintesisConceptualV2DAO.entity.toLowerCase(), `/v2/calificaciones/${SintesisConceptualV2DAO_1.SintesisConceptualV2DAO.entity.toLowerCase()}`, SintesisConceptualV2DAO_1.SintesisConceptualV2DAO);
    }
    async preInsertAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    async preUpdateAction(req, session) {
        req.body = (0, helpers_1.checkLibbyArray)(req.body);
    }
    getAllowGuest() {
        return false;
    }
}
exports.SintesisConceptualEndpoint = SintesisConceptualEndpoint;
//# sourceMappingURL=SintesisConceptualV2Endpoint.js.map
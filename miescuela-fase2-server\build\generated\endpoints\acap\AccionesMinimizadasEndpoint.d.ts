import { MiEscuelaEndpoint } from '../../../app/config/endpoint/MiEscuelaEndpoints';
import { AccionMinimizada, AccionMinimizada as Entity } from '../../orm/entities';
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { ObjectType } from 'typeorm';
export declare class AccionesMinimizadasEndpoint extends MiEscuelaEndpoint<Entity> {
    constructor();
    postSelectAction(values: Array<AccionMinimizada>, session: ChinoSessionDTO, req: JRRequest): Promise<Array<AccionMinimizada> | Array<ObjectType<AccionMinimizada>>>;
    getAllowGuest(): boolean;
}

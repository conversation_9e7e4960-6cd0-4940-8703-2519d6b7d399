"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrearEstudianteEspecialEndpoint = void 0;
const entities_1 = require("../orm/entities");
const MiEscuelaEndpointV2_1 = require("../../app/config/endpoint/MiEscuelaEndpointV2");
const dao_1 = require("../orm/dao");
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const dayjs_1 = __importDefault(require("dayjs"));
const Log = new chino_sdk_1.ChinoLogManager('CrearEstudianteEspecialEndpoint');
class CrearEstudianteEspecialEndpoint extends MiEscuelaEndpointV2_1.MiEscuelaEndpointV2 {
    constructor() {
        super(dao_1.CrearEstudianteEspecialDAO.entity.toLowerCase(), '/crear-estudiante', dao_1.CrearEstudianteEspecialDAO);
    }
    getAllowGuest() {
        return false;
    }
    async preInsertAction(req, session) {
        try {
            const body = req.body;
            if (!(body === null || body === void 0 ? void 0 : body.nombre) ||
                !(body === null || body === void 0 ? void 0 : body.apellido) ||
                !(body === null || body === void 0 ? void 0 : body.tipoDocumento) ||
                !(body === null || body === void 0 ? void 0 : body.documento) ||
                !(body === null || body === void 0 ? void 0 : body.pais) ||
                !(body === null || body === void 0 ? void 0 : body.provincia) ||
                !(body === null || body === void 0 ? void 0 : body.genero) ||
                !(body === null || body === void 0 ? void 0 : body.fechaNacimiento)) {
                throw chino_sdk_1.ChinoCustomError.CustomException('Faltan campos obligatorios', 400);
            }
            const em = chino_sdk_1.ChinoManager.getManager();
            const existente = await em.getRepository(entities_1.Persona).findOne({
                where: {
                    documento: body.documento,
                    tipoDocumento: { idTipoDocumento: body.tipoDocumento },
                },
            });
            req.body = {
                ...(existente ? { idPersona: existente.idPersona } : {}),
                nombre: body.nombre,
                apellido: body.apellido,
                documento: body.documento,
                tipoDocumento: { idTipoDocumento: body.tipoDocumento },
                fechaNacimiento: (0, dayjs_1.default)(body.fechaNacimiento).format('YYYY-MM-DD'),
                genero: { idGenero: body.genero },
                paisNacimiento: { idPais: body.pais },
                nacionalidad: { idPais: body.pais },
                provinciaNacimiento: { idProvincia: body.provincia },
            };
            req._crearEst_echo = {
                tipoDocumento: body.tipoDocumento,
                genero: body.genero,
                pais: body.pais,
                provincia: body.provincia,
            };
            return req.body;
        }
        catch (err) {
            Log.error('Error en preInsertAction /crear-estudiante:', err);
            throw chino_sdk_1.ChinoCustomError.CustomException('Error al crear estudiante', 400);
        }
    }
    async postInsertAction(value, session, req, tx) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        try {
            const em = tx !== null && tx !== void 0 ? tx : chino_sdk_1.ChinoManager.getManager();
            const persona = value;
            if (!(persona === null || persona === void 0 ? void 0 : persona.idPersona)) {
                throw chino_sdk_1.ChinoCustomError.CustomException('No se pudo obtener la persona insertada', 500);
            }
            let alumno = await em.getRepository(entities_1.Alumno).findOne({
                where: { persona: { idPersona: persona.idPersona } },
            });
            if (!alumno) {
                const [estado, condicion] = await Promise.all([
                    em.getRepository(entities_1.EstadoMatricula).findOne({
                        where: [{ descripcionEstadoMatricula: 'Inscripto' }],
                    }),
                    em.getRepository(entities_1.Condicion).findOne({
                        where: [{ descripcionCondicion: 'No regular' }],
                    }),
                ]);
                if (!estado)
                    throw chino_sdk_1.ChinoCustomError.CustomException('No se encontró estado "Inscripto"', 500);
                if (!condicion)
                    throw chino_sdk_1.ChinoCustomError.CustomException('No se encontró condición "No Regular"', 500);
                alumno = em.getRepository(entities_1.Alumno).create({
                    persona: { idPersona: persona.idPersona },
                    estadoMatricula: estado,
                    condicion: condicion,
                });
                alumno = await em.getRepository(entities_1.Alumno).save(alumno);
            }
            const echo = req._crearEst_echo || {};
            return {
                idAlumno: alumno.idAlumno,
                nombre: persona.nombre,
                apellido: persona.apellido,
                fechaNacimiento: (0, dayjs_1.default)(persona.fechaNacimiento).format('YYYY-MM-DD'),
                tipoDocumento: (_a = echo.tipoDocumento) !== null && _a !== void 0 ? _a : (_b = persona === null || persona === void 0 ? void 0 : persona.tipoDocumento) === null || _b === void 0 ? void 0 : _b.idTipoDocumento,
                documento: persona.documento,
                paisNacimiento: (_c = echo.pais) !== null && _c !== void 0 ? _c : (_d = persona === null || persona === void 0 ? void 0 : persona.paisNacimiento) === null || _d === void 0 ? void 0 : _d.idPais,
                provinciaNacimiento: (_e = echo.provincia) !== null && _e !== void 0 ? _e : (_f = persona === null || persona === void 0 ? void 0 : persona.provinciaNacimiento) === null || _f === void 0 ? void 0 : _f.idProvincia,
                genero: (_g = echo.genero) !== null && _g !== void 0 ? _g : (_h = persona === null || persona === void 0 ? void 0 : persona.genero) === null || _h === void 0 ? void 0 : _h.idGenero,
            };
        }
        catch (err) {
            Log.error('Error en postInsertAction /crear-estudiante:', err);
            throw chino_sdk_1.ChinoCustomError.CustomException('Error al devolver estudiante creado', 500);
        }
    }
}
exports.CrearEstudianteEspecialEndpoint = CrearEstudianteEspecialEndpoint;
//# sourceMappingURL=CrearEstudianteEspecialEnpoint.js.map
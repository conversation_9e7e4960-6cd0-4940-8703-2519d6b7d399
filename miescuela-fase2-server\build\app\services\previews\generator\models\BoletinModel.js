"use strict";
// import { Periodo } from "generated/orm/entities";
// import { LEVEL_TYPES } from 'app/const';
// import {
//   Anio,
//   CicloLectivo,
//   CicloLectivoAdultos,
//   Turno,
// } from 'generated/orm/models';
// import { Alumno, Localizacion } from 'generated/orm/models';
// export interface BoletinModel {
//   alumno: Alumno;
//   localizacion: Localizacion;
//   cicloLectivo: CicloLectivo | CicloLectivoAdultos;
//   anio: Anio;
//   turno: Turno;
//   periodo: any; //Periodo
//   nivel: LEVEL_TYPES;
//   calificaciones: any[];
// }
//# sourceMappingURL=BoletinModel.js.map
import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpoint } from '../../../../app/config/endpoint/MiEscuelaEndpoints';
import { SintesisConceptual } from '../../../orm/entities';
export declare class SintesisConceptualEndpoint extends MiEscuelaEndpoint<SintesisConceptual> {
    constructor();
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
    preUpdateAction(req: JRRequest, session: ChinoSessionDTO): Promise<void>;
    getAllowGuest(): boolean;
}

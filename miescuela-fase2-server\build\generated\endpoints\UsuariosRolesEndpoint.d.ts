import { ChinoContext, EntityManager } from '@phinxlab/chino-sdk';
import { JRRequest } from '@phinxlab/just-rpc';
import { MiEscuelaEndpointCustom } from '../../app/config/endpoint/MiEscuelaEndpointCustom';
import { Persona, UsuarioRolDependenciaFuncional, UsuarioRolEstablecimiento, UsuarioRolTraverseAction } from '../../generated/orm/entities';
export declare class UsuariosRolesEndpoint extends MiEscuelaEndpointCustom {
    constructor(name?: string, path?: string);
    configure(): void;
    exec(req: JRRequest, tx: EntityManager): Promise<any>;
    selectPersona(tx: EntityManager, cuil: string, documento: string, lastName: string, context: ChinoContext, idUsuario?: number): Promise<any[]>;
    updatePersona(tx: EntityManager, idUsuario: number, email: string, cuil: string, documento: string, idPersona: number, context: ChinoContext): Promise<Persona>;
    insertPersona(tx: EntityManager, name: string, lastName: string, documento: string, idUsuario: number, email: string, cuil: string, idTipoDocumento: number, context: ChinoContext): Promise<Persona>;
    insertUsuarioRolEstablecimiento(tx: EntityManager, idUsuario: string, nivel: string, idRol: number, context: ChinoContext, permissionMap?: string[]): Promise<UsuarioRolEstablecimiento>;
    insertUsuarioRolDependenciaFuncional(tx: EntityManager, idRolEstablecimiento: string, idDependenciaFuncional: string, context: ChinoContext): Promise<UsuarioRolDependenciaFuncional>;
    insertUsuarioRolTraverseAction(tx: EntityManager, idRolEstablecimiento: string, idTraverseAction: number, idDistritoEscolar: any[], context: ChinoContext): Promise<UsuarioRolTraverseAction>;
    insertUsuarioRolTraverseActionEspeciales(tx: EntityManager, idRolEstablecimiento: string, idTraverseAction: number, value: string[], context: ChinoContext): Promise<UsuarioRolTraverseAction>;
    selectLocalizacionesByNivel(tx: EntityManager, nivel: string, context: ChinoContext): Promise<any[]>;
    addUser(req: JRRequest, tx: EntityManager, context: ChinoContext): Promise<any>;
    insertRolEquipoACAP(tx: EntityManager, idUsuario: any, req: JRRequest, context: ChinoContext): Promise<{
        status: number;
        message: string;
        el: UsuarioRolEstablecimiento | import("@phinxlab/chino-sdk").ObjectType<UsuarioRolEstablecimiento>;
    }>;
    switchInsertRol(req: JRRequest, tx: EntityManager, context: ChinoContext): Promise<{
        status: number;
        message: string;
    } | undefined>;
    insertRol(tx: EntityManager, idUsuario: string, idDistritoEscolar: any[], idDependenciaFuncional: string, nivel: string, context: ChinoContext, insertType?: string): Promise<{
        status: number;
        message: string;
    }>;
    insertRolTecEspNorm(tx: EntityManager, idUsuario: string, nivel: string, idDependenciaFuncional: string, rolNomenclatura: string, context: ChinoContext, localizaciones?: any[]): Promise<{
        status: number;
        message: string;
    }>;
    insertRolDirecArea(tx: EntityManager, idUsuario: string, DF: any[], context: ChinoContext): Promise<{
        status: number;
        message: string;
    } | undefined>;
    insertRolDireccionAcap(tx: EntityManager, idUsuario: string, DF: number[][], context: ChinoContext): Promise<{
        status: number;
        message: string;
    }>;
    insertRolServicioAEscuelas(tx: EntityManager, idUsuario: string, context: ChinoContext): Promise<{
        status: number;
        message: string;
    } | undefined>;
    insertRolPorLocalizacion(tx: EntityManager, idUsuario: string, user: {
        idRolUsuario: number;
        DF: number;
        localizaciones: number[];
        nivel?: number;
    }, context: ChinoContext): Promise<{
        status: number;
        message: string;
    }>;
    insertRolInclusionEscolar(tx: EntityManager, idUsuario: string, context: ChinoContext): Promise<{
        status: number;
        message: string;
    }>;
    insertImpersonador(tx: EntityManager, idUsuario: string, context: ChinoContext): Promise<{
        status: number;
        message: string;
    }>;
}

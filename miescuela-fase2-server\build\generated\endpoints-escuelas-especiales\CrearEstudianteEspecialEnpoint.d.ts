import { Persona } from '../orm/entities';
import { MiEscuelaEndpointV2 } from '../../app/config/endpoint/MiEscuelaEndpointV2';
import { JRRequest } from '@phinxlab/just-rpc';
import { ChinoSessionDTO, EntityManager } from '@phinxlab/chino-sdk';
export declare class CrearEstudianteEspecialEndpoint extends MiEscuelaEndpointV2<Persona> {
    constructor();
    getAllowGuest(): boolean;
    preInsertAction(req: JRRequest, session: ChinoSessionDTO): Promise<Persona>;
    postInsertAction(value: Persona | Function, session: ChinoSessionDTO, req: JRRequest, tx: EntityManager): Promise<Persona>;
}

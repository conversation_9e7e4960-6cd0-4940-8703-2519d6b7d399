import { Pool, PoolClient, QueryResult } from 'pg';
import { LogManager } from '../log';
export declare class DBPool {
    protected pool: Pool;
    protected connectionQuery: string;
    log: LogManager;
    constructor(config: any, name: string);
    configure(config: any): void;
    getConnectionQuery(): string;
    getPool(): Pool;
}
export declare class DB {
    dbPool: DBPool;
    private log;
    constructor(config: any, name?: string);
    getConnection(): Promise<PoolClient>;
    query(sql: string, values?: Array<any>): Promise<QueryResult>;
}

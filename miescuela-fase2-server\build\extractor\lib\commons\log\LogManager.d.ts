/**
 *
 */
export declare class LogManager {
    #private;
    constructor(name: string);
    info(text: any, ...optionalParams: any[]): void;
    debug(text: any, ...optionalParams: any[]): void;
    fatal(text: any, ...optionalParams: any[]): void;
    warn(text: any, ...optionalParams: any[]): void;
    error(text: any, ...optionalParams: any[]): void;
    verbose(text: any, ...optionalParams: any[]): void;
    static create(name: string): LogManager;
}
export declare function logReturn(xx: any): any;

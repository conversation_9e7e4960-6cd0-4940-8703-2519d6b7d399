"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropositosAdicionalesEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PropositosAdicionalesEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PropositosAdicionalesDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PropositosAdicionalesDAO.entity.toLowerCase(), dao_1.PropositosAdicionalesDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.PropositosAdicionalesEndpoint = PropositosAdicionalesEndpoint;
//# sourceMappingURL=PropositosAdicionalesEndpoint.js.map
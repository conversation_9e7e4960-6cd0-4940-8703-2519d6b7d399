{"version": 3, "file": "PruebaEndpoint.js", "sourceRoot": "", "sources": ["../../../src/generated/endpoints/PruebaEndpoint.ts"], "names": [], "mappings": ";;;AAAA,iDAAqE;AAErE,+FAA4F;AAC5F,8CAA8D;AAC9D,mDAA8D;AAE9D,MAAa,cAAe,SAAQ,iDAAuB;IACzD,YAAY,IAAI,GAAG,YAAY,EAAE,IAAI,GAAG,oBAAoB;QAC1D,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACpB,CAAC;IACD,KAAK,CAAC,IAAI,CACR,GAAc,EACd,OAAsB,EACtB,GAAe;QAEf,IAAI;YACF,QAAQ,GAAG,CAAC,MAAM,EAAE;gBAClB,KAAK,KAAK,CAAC,CAAC;oBACV,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,sBAAW,CAAC,CAAC,IAAI,EAAE,CAAC;oBACpE,OAAO,WAAW,CAAC;iBACpB;gBAED;oBACE,MAAM,sBAAU,CAAC,eAAe,CAC9B,sBAAsB,EACtB,uBAAW,CAAC,eAAe,CAC5B,CAAC;aACL;SACF;QAAC,OAAO,CAAM,EAAE;YACf,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACxB,oDAAoD;YACpD,MAAM,sBAAU,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,uBAAW,CAAC,WAAW,CAAC,CAAC;SACtE;IACH,CAAC;IACD,SAAS;QACP,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,mBAAQ,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;CACF;AAhCD,wCAgCC"}
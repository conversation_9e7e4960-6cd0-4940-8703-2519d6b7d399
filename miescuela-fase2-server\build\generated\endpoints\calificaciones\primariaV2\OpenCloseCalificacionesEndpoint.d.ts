import { AspectosGenerales } from '../../../orm/entities';
import { ChinoEndpointMethod } from '@phinxlab/chino-sdk';
import { MiEscuelaEndpointV2 } from '../../../../app/config/endpoint/MiEscuelaEndpointV2';
export declare class OpenCloseCalificacionesEndpoint extends MiEscuelaEndpointV2<AspectosGenerales> {
    constructor();
    protected getMethods(): ChinoEndpointMethod[];
    configure(): void;
}

import { ChinoSessionDTO } from '@phinxlab/chino-sdk';
import { MiEscuelaEndpoint } from '../../app/config/endpoint/MiEscuelaEndpoints';
import { DeudaAcademica } from '../orm/entities';
export declare class DeudaAcademicaEndpoint extends MiEscuelaEndpoint<DeudaAcademica> {
    constructor();
    postSelectAction(values: any, session: ChinoSessionDTO, req: any): Promise<any>;
    getAllowGuest(): boolean;
}

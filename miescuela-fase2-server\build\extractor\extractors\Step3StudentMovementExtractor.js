"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Step3StudentMovementExtractor = void 0;
const utils_1 = require("../utils");
const tables = {
    studentMovement: 'alumnomovimiento',
    studentState: 'estadoalumno',
    student: 'alumno',
    person: 'persona',
    documentType: 'tipodocumento',
    country: 'pais',
    city: 'ciudad',
};
class Step3StudentMovementExtractor {
    async run(context, source) {
        for (const [key, table] of Object.entries(tables)) {
            context[key] = await (0, utils_1.getFromTable)(table, source);
        }
        return context;
    }
}
exports.Step3StudentMovementExtractor = Step3StudentMovementExtractor;
//# sourceMappingURL=Step3StudentMovementExtractor.js.map
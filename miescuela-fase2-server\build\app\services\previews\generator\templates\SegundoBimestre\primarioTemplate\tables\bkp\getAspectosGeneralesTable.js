"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ cicloLectivo, anio, tipoPeriodo, vinculoPedagogico, promocionAcompanada, espaciosConsolidandoPregunta, espaciosConsolidando, apoyoPregunta, apoyo, organizaParticipa, compromisoAprendizaje, }) => `
    <table class="tabla">
        <tr class="title-table">
            <td style="background-color: #e2eedb; font-weight: bold; font-size: 12px;">
                ${cicloLectivo} - ${anio}
            </td>
            <td style="background-color: #e2eedb; border-bottom: 0px; padding-top: 42px; font-weight: bold; font-size: 12px;">
                ${tipoPeriodo}
            </td>
        </tr>
        <tr>
            <div>
                <td style=" background-color: #ececec; font-weight: bold;">ASPECTOS GENERALES</td>
            </div>
            <td style="background-color: #e2eedb;"></td>
        </tr>
        <tr>
            <td>Mantuvo el vinculo pedagógico.</td>
            <td>${vinculoPedagogico}</td>
        </tr>
        <tr>
            <td>Se encuentra en promoción acompañada</td>
            <td>${promocionAcompanada}</td>
        </tr>
        <tr>
            <td>En relación al aprendizaje 2020, ¿se encuentra consolidado algunos espacios curriculares?</td>
            <td>${espaciosConsolidandoPregunta}</td>
        </tr>
        <tr>
            <td>¿Cuáles?</td>
            <td>${espaciosConsolidando ? espaciosConsolidando : '-'}</td>
        </tr>
        <tr>
            <td>¿Posee apoyos/acompañamientos?</td>
            <td>${apoyoPregunta}</td>
        </tr>
        <tr>
            <td>¿Cuáles?</td>
            <td>${apoyo
    ? (() => {
        let apoyos = '';
        apoyo.forEach((a, i) => {
            i === 0 ? (apoyos += `${a}`) : (apoyos += `, ${a}`);
        });
        return apoyos;
    })()
    : '-'}</td>
        </tr>
        <tr>
            <td>¿Se organiza y participa en actividades propuestas a traves de los diversos formatos ajustándose a las pautas de trabajo?</td>
            <td> ${organizaParticipa}</td>
        </tr>
        <tr> 
            <td>¿Se compromete con su aprendizaje reconociendo logros y dificultades?</td>
            <td> ${compromisoAprendizaje}</td>
        </tr>
    </table>
`;
//# sourceMappingURL=getAspectosGeneralesTable.js.map
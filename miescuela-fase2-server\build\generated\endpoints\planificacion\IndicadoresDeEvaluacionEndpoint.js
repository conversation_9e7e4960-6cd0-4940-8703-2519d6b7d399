"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicadoresDeEvaluacionEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class IndicadoresDeEvaluacionEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.IndicadoresDeEvaluacionDAO.entity.toLowerCase(), '/planificacion/' + dao_1.IndicadoresDeEvaluacionDAO.entity.toLowerCase(), dao_1.IndicadoresDeEvaluacionDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.IndicadoresDeEvaluacionEndpoint = IndicadoresDeEvaluacionEndpoint;
//# sourceMappingURL=IndicadoresDeEvaluacionEndpoint.js.map
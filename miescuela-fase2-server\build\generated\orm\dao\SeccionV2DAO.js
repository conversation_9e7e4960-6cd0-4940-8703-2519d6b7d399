"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeccionV2DAO = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const MiEscuelaDAO_1 = require("../../../app/config/DAO/MiEscuelaDAO");
const entities_1 = require("../entities");
const EspacioCurricularSeccionDAO_1 = require("./EspacioCurricularSeccionDAO");
const EspacioCurricularDAO_1 = require("./EspacioCurricularDAO");
const typeorm_1 = require("typeorm");
const _1 = require(".");
const const_1 = require("../../../app/const");
const traverseActions_1 = require("../../../app/const/traverseActions");
const getLevel_1 = require("../../../app/business/bootstrap/helper/getLevel");
const validations_1 = require("../../../app/business/flows/matriculacion/matriculacionRefactor/inicial/promocionEstadoInicialService/validations");
const normalizeDescription = (description) => {
    return description
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^a-zA-Z0-9]+/g, '')
        .replace(/s/g, '')
        .trim()
        .toUpperCase();
};
class SeccionV2DAO extends MiEscuelaDAO_1.MiEscuelaDAO {
    constructor() {
        super(entities_1.Seccion, 'seccion');
    }
    configure() {
        this.createAspect('minimal').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
        ]);
        this.createAspect('onlyCicloLectivo').addRelations([
            'cicloLectivo',
            'localizacion',
            'nivel',
            'anio',
        ]);
        this.createAspect('onlyAnio').addRelations([
            'anio',
            'localizacion',
            'cicloLectivo',
            'nivel',
        ]);
        this.createAspect('onlyTurno').addRelations([
            'turno',
            'localizacion',
            'cicloLectivo',
            'nivel',
        ]);
        this.createAspect('onlyNivel').addRelations([
            'nivel',
            'localizacion',
            'cicloLectivo',
        ]);
        this.createAspect('anio_turno').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'anio',
            'anio.nivel',
            'turno',
        ]);
        // added aspect as it is used on SeccionEndpoint to set prop matCount
        this.createAspect('presentismo').addRelations([
            'anio',
            'turno',
            'localizacion',
            'nivel',
            'cicloLectivo',
        ]);
        this.createAspect('onlyPlanEstudioNivel').addRelations([
            'planEstudioNivel',
            'localizacion',
            'nivel',
            'cicloLectivo',
        ]);
        this.createAspect('cicloLectivo_planEstudioNivel').addRelations([
            'cicloLectivo',
            'planEstudioNivel',
            'anio',
        ]);
        this.createAspect('cicloLectivo_anio').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'anio',
        ]);
        this.createAspect('anio_nivel_cicloLectivo').addRelations([
            'anio',
            'nivel',
            'cicloLectivo',
            'localizacion',
        ]);
        this.createAspect('anio_cicloLectivo_nivel_turno').addRelations([
            'anio',
            'cicloLectivo',
            'nivel',
            'turno',
            'localizacion',
        ]);
        this.createAspect('nivel_planEstudioNivel_planEstudio').addRelations([
            'localizacion',
            'cicloLectivo',
            'nivel',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
        ]);
        this.createAspect('acap_inscripcionAccionesSeleccionadas').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'turno',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
            'planEstudioNivel.planEstudio.orientaciones',
            'planEstudioNivel.planEstudio.orientaciones.orientacion',
        ]);
        this.createAspect('proyeccion_formProyeccionDialog').addRelations([
            'nivel',
            'anio',
            'turno',
            'cicloLectivo',
            'localizacion',
            'localizacion.establecimiento',
            'localizacion.establecimiento.dependenciaFuncional',
        ]);
        this.createAspect('promocion_promotionDetail').addRelations([
            'cicloLectivo',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
            'anio',
            'turno',
            'nivel',
        ]);
        this.createAspect('hook_useGetLocalizacionSeccionProperties').addRelations([
            'anio',
            'turno',
            'nivel',
            'localizacion',
            'cicloLectivo',
        ]);
        this.createAspect('legajo_modalAgregarECPendiente').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'anio',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
        ]);
        this.createAspect('matriculacion_Adults').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'cicloLectivoAdultos',
        ]);
        this.createAspect('matriculacion_ScaleBC').addRelations([
            'ciclo',
            'nivel',
            'localizacion',
            'cicloLectivo',
        ]);
        this.createAspect('matriculacion_Initial').addRelations([
            'localizacion',
            'cicloLectivo',
            'turno',
            'nivel',
            'ciclo',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
        ]);
        this.createAspect('matriculacion_InitialAdults').addRelations([
            'turno',
            'nivel',
            'cicloLectivoAdultos',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
            'cicloAdultos',
            'localizacion',
            'cicloLectivo',
        ]);
        this.createAspect('alertas').addRelations([
            'cicloLectivo',
            'nivel',
            'localizacion',
        ]);
        this.createAspect('planEstudioNivel_turno_anio').addRelations([
            'planEstudioNivel',
            'turno',
            'anio',
        ]);
        this.createAspect('informe_iel').addRelations([
            'anio',
            'localizacion',
            'localizacion.establecimiento',
            'localizacion.establecimiento.dependenciaFuncional',
            'localizacion.establecimiento.distritoEscolar',
            'turno',
            'nivel',
        ]);
        this.createAspect('copiado').addRelations([
            'createdBy',
            'anio',
            'ciclo',
            'cicloAdultos',
            'cicloLectivo',
            'cicloLectivoAdultos',
            'localizacion',
            'nivel',
            'planEstudioNivel',
            'turno',
            'unidadEducativa',
        ]);
        this.createAspect('AniosCheckService').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'anio',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
            'planEstudioNivel.planEstudio.planEstudioBasico',
        ]);
        this.createAspect('EspacioCurricularEndpoint').addRelations([
            'anio',
            'planEstudioNivel',
            'planEstudioNivel.modalidadNivel',
            'nivel',
            'localizacion',
            'cicloLectivo',
        ]);
        this.createAspect('boletines').addRelations([
            'anio',
            'cicloLectivo',
            'localizacion',
            'nivel',
            'turno',
            'localizacion.establecimiento',
            'localizacion.establecimiento.distritoEscolar',
            'localizacion.establecimiento.dependenciaFuncional',
        ]);
        this.createAspect('save').addRelations([
            'anio',
            'nivel',
            'cicloLectivo',
            'localizacion',
            'planEstudioNivel',
            'planEstudioNivel.planEstudio',
        ]);
        // TODO: C3 tiene que terminar de pulir este aspect porque tiene cosas de más
        this.createAspect('promocion-estado-service').addRelations([
            'anio',
            'anio.nivel',
            'cicloLectivo',
            'cicloLectivo.estadoCicloLectivo',
            'cicloLectivoAdultos',
            'cicloLectivoAdultos.cicloLectivo',
            'cicloLectivoAdultos.cicloLectivo.estadoCicloLectivo',
            'localizacion',
            'localizacion.deletedBy',
            'localizacion.deletedBy.deletedBy',
            'nivel',
        ]);
        this.createAspect('combinaciones-pase-endpoint').addRelations([
            'anio',
            'localizacion',
            'cicloLectivo',
            'nivel',
            'turno',
        ]);
        this.createAspect('validacion-seccion-destino').addRelations([
            'localizacion',
            'cicloLectivo',
            'anio',
            'nivel',
        ]);
        this.createAspect('cantidad-por-localizacion').addRelations([
            'localizacion',
            'cicloLectivo',
        ]);
        this.createAspect('secciones-localizacion').addRelations([
            'localizacion',
            'cicloLectivo',
            'nivel',
        ]);
        this.createAspect('escuelas-especiales-grupo').addRelations([
            'grupo',
            'localizacion',
            'grupo.turno',
            'grupo.jornada',
            'grupo.ciclo',
            'grupo.nivel',
            'grupo.cicloLectivo',
            'grupo.planEstudio',
        ]);
        this.createAspect('escuelas-especiales-matriculados').addRelations([
            'localizacion',
            'grupo',
            'grupo.turno',
            'grupo.jornada',
            'grupo.ciclo',
            'grupo.nivel',
            'grupo.cicloLectivo',
            'grupo.planEstudio',
        ]);
        this.createAspect('minimal-especiales').addRelations([
            'localizacion',
            'nivel',
            'cicloLectivo',
            'grupo',
        ]);
        this.createAspect('secciones-proyectadas-validation').addRelations([
            'localizacion',
            'cicloLectivo',
            'nivel',
        ]);
    }
    async preAction(event, value, txEntityManager, context) {
        var _a, _b, _c;
        if (event === 'Update') {
            await this.sectionName50CharactersLength(value, txEntityManager, context);
            value.updatedBy = { idUsuario: Number(context.session.user.id) };
            value.updatedAt = new Date();
            // ACTUALMENTE NO SE PUEDE CAMBIAR NI EL AÑIO NI EL PLAN DE ESTUDIO
            if ((_a = value === null || value === void 0 ? void 0 : value.anio) === null || _a === void 0 ? void 0 : _a.idAnio) {
                // Obtenemos la version actual de seccion para comparaciones.
                const oldValue = await this.findByPK(value.idSeccion, { isSelect: true, context, aspect: 'cicloLectivo_planEstudioNivel' }, txEntityManager);
                // Throweamos si cambia el ciclo lectivo.
                if (value.cicloLectivo.idCicloLectivo !==
                    oldValue.cicloLectivo.idCicloLectivo)
                    throw chino_sdk_1.ChinoError.CustomException('No se puede cambiar el cicloLectivo de una sección.', 400);
                if (value.anio.idAnio.toString() !== oldValue.anio.idAnio.toString() ||
                    value.planEstudioNivel.idPlanEstudioNivel.toString() !==
                        oldValue.planEstudioNivel.idPlanEstudioNivel.toString()) {
                    // Si cambia el año y/o el plan de estudios.
                    // Hay que actualizar los ECS.
                    const oldECS = await EspacioCurricularSeccionDAO_1.EspacioCurricularSeccionDAO.query('efectivizacion')
                        .setContext(context)
                        .equals('seccion', value.idSeccion)
                        .run(txEntityManager);
                    const newEC = await EspacioCurricularDAO_1.EspacioCurricularDAO.query('efectivizacion')
                        .setContext(context)
                        .equals('planEstudio', value.planEstudioNivel.planEstudio.idPlanEstudio)
                        .equals('materia.anio', value.anio.idAnio)
                        .run(txEntityManager);
                    await this.checkECS(value, txEntityManager, context, oldECS, newEC);
                }
            }
            // Para agregar alguna forma de completar los ECs qeu falten por error de datos o si de alguna mmanera se
            // Deberiamos en qcada update fijarnos si tenemos todos los ec o si nos falta 1
            // no importa si tenemos menos ya que si se deshabilito ya no aparecera directamente
        }
        if (event === 'Insert') {
            await this.sectionName50CharactersLength(value, txEntityManager, context);
            value.createdBy = { idUsuario: Number(context.session.user.id) };
            value.createdAt = new Date();
        }
        if (event === 'Insert' || event === 'Update') {
            try {
                const user = context.session.user;
                const nivelId = (_c = (_b = user.groupSelected) === null || _b === void 0 ? void 0 : _b.nivel) === null || _c === void 0 ? void 0 : _c.idNivel;
                if (nivelId === getLevel_1.NIVEL_ID.INICIAL) {
                    const validator = new validations_1.PuedeProyectarValidator();
                    await validator.validate(txEntityManager, context);
                }
            }
            catch (error) {
                console.warn('PuedeProyectarValidator execution in SeccionDAO:', error.message);
            }
        }
        return value;
    }
    async postAction(event, values, txEntityManager, context) {
        const user = context.session.user;
        if (event === 'Insert') {
            values = Array.isArray(values) ? values : [values];
            // Buscamos los EC del establecimmineto by plan estudio y anio
            const espaciosCurriculares = await EspacioCurricularDAO_1.EspacioCurricularDAO.query('new_seccion')
                .setContext(context)
                .in('planEstudio', [
                ...new Set(values.map((seccion) => seccion.planEstudioNivel.planEstudio.idPlanEstudio)),
            ])
                .in('materia.anio', [
                ...new Set(values.map((seccion) => seccion.anio.idAnio)),
            ])
                .in('localizacion.idLocalizacion', [
                ...new Set(values.map((seccion) => seccion.localizacion.idLocalizacion)),
            ])
                .run(txEntityManager);
            const resultA = this.filterEspaciosCurricularesDuplicados(espaciosCurriculares);
            const resultB = this.filterEspaciosCurriculares(resultA);
            // CREAMOS LOS ECS para la secciones nuevas
            const toSaveECS = [];
            values.forEach((s) => {
                const espacios = resultB.filter((ec) => ec.materia.anio.idAnio === s.anio.idAnio &&
                    ec.planEstudio.idPlanEstudio ===
                        s.planEstudioNivel.planEstudio.idPlanEstudio);
                espacios.forEach((e) => {
                    toSaveECS.push({
                        espacioCurricular: e,
                        seccion: s,
                        activo: true,
                        createdAt: new Date(),
                        createdBy: user.id,
                    });
                });
            });
            // Creamos los ECS de la nueva seccion para los espacios correspondientes
            await txEntityManager
                .getRepository(entities_1.EspacioCurricularSeccion)
                .save(toSaveECS);
            // Y eso estodo lo necesario
            await this.assignToCoordJE(values, txEntityManager, context);
        }
        else if (event.toLocaleLowerCase() === 'remove') {
            await this.removeSeccionOfUres(values, txEntityManager, context);
        }
        return values;
    }
    /**
     * Elimina duplicados de la lista de Espacios Curriculares basándose en la descripción de la materia, año y plan de estudio.
     * En caso de encontrar duplicados, conserva el Espacio Curricular con el menor valor de `idMateria`.
     *
     * @param {EspacioCurricular[]} ecList - Lista de Espacios Curriculares a filtrar.
     * @returns {EspacioCurricular[]} Lista de Espacios Curriculares única sin duplicados.
     */
    filterEspaciosCurricularesDuplicados(ecList) {
        const uniqueECs = new Map();
        function generateUniqueKey(ec) {
            return `${ec.materia.descripcion}-${ec.materia.anio.idAnio}-${ec.planEstudio.idPlanEstudio}`;
        }
        ecList.forEach((ec) => {
            var _a;
            const key = generateUniqueKey(ec);
            const currentEC = (_a = uniqueECs.get(key)) !== null && _a !== void 0 ? _a : {
                materia: { idMateria: Infinity },
            };
            if (ec.materia.idMateria < currentEC.materia.idMateria) {
                uniqueECs.set(key, ec);
            }
        });
        return Array.from(uniqueECs.values());
    }
    generateKey(ec) {
        return `${normalizeDescription(ec.descripcion)}-${ec.materia.anio.idAnio}-${ec.planEstudio.idPlanEstudio}`;
    }
    /**
     * Filtra y elimina duplicados en una lista de Espacios Curriculares basándose en la descripción normalizada,
     * año de la materia y el plan de estudio. Si se encuentran duplicados con el mismo año y plan de estudio,
     * se prioriza conservar el Espacio Curricular cuya descripción no contenga paréntesis.
     *
     * @param {EspacioCurricular[]} ecList - Lista de Espacios Curriculares a filtrar.
     * @returns {EspacioCurricular[]} Lista de Espacios Curriculares única sin duplicados.
     */
    filterEspaciosCurriculares(ecList) {
        const uniqueECs = new Map();
        ecList.forEach((ec) => {
            var _a;
            const key = this.generateKey(ec);
            if (!uniqueECs.has(key) ||
                (/\([^)]*\)/.test(((_a = uniqueECs.get(key)) === null || _a === void 0 ? void 0 : _a.descripcion) || '') &&
                    !/\([^)]*\)/.test(ec.descripcion))) {
                uniqueECs.set(key, ec);
            }
        });
        return Array.from(uniqueECs.values());
    }
    async removeSeccionOfUres(values, tx, ctx) {
        const coordJeUres = await _1.UsuarioRolEstablecimientoV2DAO.query('sidemenuSeccionDAO')
            .equals('rolUsuario', const_1.ROLES.ID_COORDINADOR_JE)
            .setContext(ctx)
            .run(tx);
        if (coordJeUres.length > 0) {
            const currentUrtas = await _1.UsuarioRolTraverseActionDAO.query()
                .in('usuarioRolEstablecimiento', coordJeUres.map((coord) => coord.idRolEstablecimiento))
                .equals('traverseAction', traverseActions_1.TRAVERSE_ACTIONS.SECCION)
                .setContext(ctx)
                .run(tx);
            await _1.UsuarioRolTraverseActionDAO.save(currentUrtas.map((urta) => ({
                ...urta,
                value: urta.value.filter((idSeccion) => !values.map((seccion) => seccion.idSeccion).includes(idSeccion)),
            })), tx, {
                isUpdate: true,
                context: ctx,
                aspect: 'save',
            });
        }
    }
    async assignToCoordJE(secciones, tx, ctx) {
        for (const seccion of secciones) {
            const coordJeUres = await _1.UsuarioRolEstablecimientoV2DAO.query('sidemenuSeccionDAO')
                .equals('rolUsuario', const_1.ROLES.ID_COORDINADOR_JE)
                .equals('id_localizacion', seccion.localizacion.idLocalizacion)
                .setContext(ctx)
                .run(tx);
            if (coordJeUres.length > 0) {
                const currentUrtas = await _1.UsuarioRolTraverseActionDAO.query('traverse')
                    .in('usuarioRolEstablecimiento', coordJeUres.map((coord) => coord.idRolEstablecimiento))
                    .equals('traverseAction', traverseActions_1.TRAVERSE_ACTIONS.SECCION)
                    .setContext(ctx)
                    .run(tx);
                const traverseActionsSoloIdRelaciones = currentUrtas.map((current) => ({
                    ...current,
                    traverseAction: {
                        traverseActionId: current.traverseAction.traverseActionId,
                    },
                    usuarioRolEstablecimiento: {
                        idRolEstablecimiento: current.usuarioRolEstablecimiento.idRolEstablecimiento,
                    },
                }));
                await _1.UsuarioRolTraverseActionDAO.save(traverseActionsSoloIdRelaciones.map((urta) => ({
                    ...urta,
                    value: [...urta.value, seccion.idSeccion],
                })), tx, {
                    isUpdate: true,
                    context: ctx,
                    aspect: 'save',
                });
            }
        }
    }
    // Prevent default. Put activo = false and do an update.
    async remove(value, txEntityManager, options) {
        // Verify it can access the value through Traverse.
        await this.findByPK(value.idSeccion, options || { context: new chino_sdk_1.ChinoContext() }, txEntityManager);
        // Check it doesn't have any alumnos.
        const am = await _1.AlumnoMovimientoDAO.query('CountByIdSeccion')
            .setContext((options === null || options === void 0 ? void 0 : options.context) || new chino_sdk_1.ChinoContext())
            .equals('seccion', value.idSeccion)
            .limit(1)
            .run(txEntityManager);
        if (am.length > 0)
            throw chino_sdk_1.ChinoError.CustomException('No se puede eliminar una sección que contenga alumnos.', 400);
        const proyQ = _1.ProyeccionDAO.query('CountByIdSeccion')
            .setContext((options === null || options === void 0 ? void 0 : options.context) || new chino_sdk_1.ChinoContext())
            .equals('seccionDestino', value.idSeccion)
            .limit(1);
        if (options &&
            options.context.session.user.groupSelected.localizacion
                .idCicloLectivo)
            proyQ.equals('cicloLectivo', options.context.session.user.groupSelected.localizacion
                .idCicloLectivo);
        else
            throw 'Invalid state.';
        const proy = await proyQ.run(txEntityManager);
        if (proy.length > 0)
            throw chino_sdk_1.ChinoError.CustomException('No se puede eliminar una sección que tenga proyecciones/promociones del ciclo lectivo actual hacia ella.', 400);
        // Update the value copied_to to null of the original if this is the copy of another section.
        await (txEntityManager === null || txEntityManager === void 0 ? void 0 : txEntityManager.query('UPDATE seccion SET copied_to = null WHERE copied_to = $1', [value.idSeccion]));
        // Make the update.
        await (txEntityManager === null || txEntityManager === void 0 ? void 0 : txEntityManager.update(entities_1.Seccion, {
            idSeccion: value.idSeccion,
        }, {
            activo: false,
        }));
        // Return input. Just 'cause.
        return value;
    }
    async checkECS(value, txEntityManager, context, newECS, newEC, aspect) {
        const goodECS = [];
        let goodECAux = [];
        newEC = newEC.filter((ec) => ec.planEstudio.idPlanEstudio ===
            value.planEstudioNivel.planEstudio.idPlanEstudio &&
            ec.materia.anio.idAnio === value.anio.idAnio);
        newEC = this.filterEspaciosCurricularesDuplicados(newEC);
        newEC = this.filterEspaciosCurriculares(newEC);
        newECS = newECS.filter((ecs) => ecs.seccion.idSeccion === value.idSeccion);
        // Obtenemos los ECS correctos, más la lista de EC que ya tienen ECS correcto.
        newECS
            .filter((ecs) => {
            return newEC
                .map((ec) => ec.idEspacioCurricular)
                .includes(ecs.espacioCurricular.idEspacioCurricular);
        })
            .forEach((ecs) => {
            goodECS.push(ecs.idEspacioCurricularSeccion);
            goodECAux.push(ecs.espacioCurricular.idEspacioCurricular);
        });
        // Quitamos los ECS que no necesitamos editar
        newECS = newECS.filter((ecs) => !goodECS.includes(ecs.idEspacioCurricularSeccion));
        // Y los EC que no necesitamos agregar.
        newEC = newEC.filter((ec) => !goodECAux.includes(ec.idEspacioCurricular));
        // Desactivamos los ECS que quedaron mal.
        newECS.forEach((ecs) => (ecs.activo = false));
        // Buscamos si existen ECS desactivados con las propiedades que queremos:
        const ECStoBeActivated = newEC.length === 0
            ? []
            : await txEntityManager.getRepository(entities_1.EspacioCurricularSeccion).find({
                relations: ['espacioCurricular'],
                where: {
                    seccion: { idSeccion: value.idSeccion },
                    espacioCurricular: {
                        idEspacioCurricular: (0, typeorm_1.In)(newEC.map((ec) => ec.idEspacioCurricular)),
                    },
                },
            });
        // Las activamos.
        ECStoBeActivated.forEach((ecs) => (ecs.activo = true));
        // Filtramos nuevamente para ver que ECs aún faltan.
        goodECAux = ECStoBeActivated.map((ecs) => ecs.espacioCurricular.idEspacioCurricular);
        newEC = newEC.filter((ec) => !goodECAux.includes(ec.idEspacioCurricular));
        // Concatenamos:
        // El array de viejos ECS que hay que borrar
        const ECSToDB = newECS.concat(
        // Con los que hay que reactivar.
        ECStoBeActivated.concat(
        // Con los que hay que crear.
        newEC.map((ec) => ({
            seccion: value,
            espacioCurricular: ec,
            activo: true,
            seccionAulaVirtuals: [],
        }))));
        // Save to DB
        return this.saveECS(txEntityManager, context, ECSToDB, value, goodECS.length, newECS, aspect);
    }
    async saveECS(tx, context, ecs, seccion, nNotEdited, newECS, aspect) {
        if (ecs.length === 0)
            return ecs;
        const ecsSave = await tx.getRepository(entities_1.EspacioCurricularSeccion).save(ecs);
        if (ecsSave.length != ecs.filter((ecs) => ecs.activo).length + nNotEdited)
            throw chino_sdk_1.ChinoError.PermissionException('No tienes permisos');
        return ecsSave;
    }
    async getMatriculados(tx, context, secciones, aspect = 'shorted_relation') {
        const seccionMap = new Map();
        if (secciones.length === 0)
            return seccionMap;
        const matriculados = await _1.AlumnoMovimientoDAO.query(aspect)
            .setContext(context)
            .in('seccion', secciones)
            .run(tx);
        for (const seccion of secciones) {
            seccionMap.set(seccion, matriculados.filter((am) => am.seccion.idSeccion === seccion));
        }
        return seccionMap;
    }
    async sectionName50CharactersLength(value, txEntityManager, context) {
        if (value.nombreSeccion && value.nombreSeccion.length > 50) {
            throw chino_sdk_1.ChinoError.CustomException('El nombre de la sección no puede tener más de 50 caracteres.', 400);
        }
    }
}
const i = new SeccionV2DAO();
exports.SeccionV2DAO = i;
//# sourceMappingURL=SeccionV2DAO.js.map
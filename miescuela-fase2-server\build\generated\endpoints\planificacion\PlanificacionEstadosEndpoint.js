"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanificacionEstadosEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../../orm/dao");
class PlanificacionEstadosEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.PlanificacionEstadosDAO.entity.toLowerCase(), '/planificacion/' + dao_1.PlanificacionEstadosDAO.entity.toLowerCase(), dao_1.PlanificacionEstadosDAO);
    }
    getMethods() {
        return ['GET', 'POST', 'PUT', 'DELETE'];
    }
    getAllowGuest() {
        return false;
    }
}
exports.PlanificacionEstadosEndpoint = PlanificacionEstadosEndpoint;
//# sourceMappingURL=PlanificacionEstadosEndpoint.js.map
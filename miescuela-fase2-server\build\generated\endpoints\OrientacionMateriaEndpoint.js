"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrientacionMateriaEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class OrientacionMateriaEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.OrientacionMateriaDAO.entity.toLowerCase(), '/public/' + dao_1.OrientacionMateriaDAO.entity.toLowerCase(), dao_1.OrientacionMateriaDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.OrientacionMateriaEndpoint = OrientacionMateriaEndpoint;
//# sourceMappingURL=OrientacionMateriaEndpoint.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EstudiantesInscriptosEndpoint = void 0;
const chino_sdk_1 = require("@phinxlab/chino-sdk");
const helpers_1 = require("../../utils/helpers");
const MiEscuelaEspecialesEndpoint_1 = require("../../app/config/endpoint/MiEscuelaEspecialesEndpoint");
const escuelasEspeciales_1 = require("../orm/dao/escuelasEspeciales");
const Log = new chino_sdk_1.ChinoLogManager('InscripcionAlumnoEndpoint');
class EstudiantesInscriptosEndpoint extends MiEscuelaEspecialesEndpoint_1.MiEscuelaEspecialesEndpoint {
    constructor() {
        super(escuelasEspeciales_1.EstudiantesInscriptosDAO.entity.toLowerCase(), '/inscriptos', escuelasEspeciales_1.EstudiantesInscriptosDAO);
    }
    getAllowGuest() {
        return false;
    }
    async postSelectAction(values, session, req) {
        try {
            const entityManager = chino_sdk_1.ChinoManager.getManager();
            const context = chino_sdk_1.ChinoContext.fromSession(session);
            const query = req.query || {};
            const limit = query.limit ? parseInt(query.limit) : 40;
            let page;
            if (query.offset) {
                const offset = parseInt(query.offset);
                page = (0, helpers_1.calculatePageFromOffset)(offset, limit);
            }
            else {
                page = 1;
            }
            const ciclo = await escuelasEspeciales_1.cicloEspecialDAO
                .query('')
                .setContext(context)
                .run(entityManager);
            const cicloEspecialMap = ciclo.map((c) => ({
                id: c.idCiclo,
                descripcion: c.descripcion,
            }));
            const data = this.formatResponse(values, cicloEspecialMap);
            const nombreValue = (0, helpers_1.extractQueryValue)(query, 'alumno.persona.nombre', 'includes');
            const apellidoValue = (0, helpers_1.extractQueryValue)(query, 'alumno.persona.apellido', 'includes');
            const documentoValue = (0, helpers_1.extractQueryValue)(query, 'alumno.persona.documento', 'includes');
            const cicloLectivoValue = (0, helpers_1.extractQueryValue)(query, 'cicloLectivo', 'equals');
            const nivelValue = (0, helpers_1.extractQueryValue)(query, 'nivel', 'equals');
            const cicloEspecialValue = (0, helpers_1.extractQueryValue)(query, 'cicloEspecial', 'equals');
            let estudiantesInscriptosCount = [];
            const queryBuilder = escuelasEspeciales_1.EstudiantesInscriptosDAO.query('count').setContext(context);
            queryBuilder.equals('cicloLectivo', cicloLectivoValue);
            if (apellidoValue || nombreValue || documentoValue) {
                queryBuilder
                    .and()
                    .groupStart()
                    .includes('alumno.persona.apellido', apellidoValue)
                    .or()
                    .includes('alumno.persona.nombre', nombreValue)
                    .or()
                    .includes('alumno.persona.documento', documentoValue)
                    .groupEnd();
            }
            if (nivelValue) {
                queryBuilder.equals('nivel', nivelValue);
            }
            if (cicloEspecialValue) {
                queryBuilder.equals('cicloEspecial', cicloEspecialValue);
            }
            estudiantesInscriptosCount = await queryBuilder.run(entityManager);
            const totalCount = estudiantesInscriptosCount.length;
            return {
                data,
                total: totalCount,
                page,
                limit,
                totalPages: Math.ceil(totalCount / limit) === 0
                    ? 1
                    : Math.ceil(totalCount / limit),
            };
        }
        catch (error) {
            Log.error('Error al buscar inscriptos:', error);
            throw new Error('Error al buscar inscriptos.');
        }
    }
    catch(error) {
        Log.error('Error al buscar inscriptos:', error);
        throw new Error('Error al buscar inscriptos.');
    }
    async preSelectAction(req, session) {
        try {
            req.headers['x-chino-aspect'] = 'especiales-default';
            if (!req.query.cicloLectivo) {
                throw new chino_sdk_1.ChinoCustomError('El parámetro cicloLectivo es obligatorio para realizar la consulta', 400);
            }
            if (!req.query.limit || !req.query.offset) {
                throw new chino_sdk_1.ChinoCustomError('El parámetro limit y offset son obligatorios para realizar la consulta', 400);
            }
            const user = session.user;
        }
        catch (error) {
            Log.error('Error al buscar inscriptos:', error);
            throw new chino_sdk_1.ChinoCustomError(`Error al obtener registros. ${error}`, error.status || 500);
        }
    }
    formatResponse(values, cicloEspecialMap) {
        return values.map((row) => {
            var _a, _b;
            const persona = row.alumno.persona;
            const cicloEspecial = cicloEspecialMap.find((c) => c.id === row.cicloEspecial);
            return {
                idInscripcionAlumno: Number(row.idInscripcionAlumno),
                idAlumno: row.alumno.idAlumno,
                nombre: persona.nombre,
                apellido: persona.apellido,
                fechaNacimiento: persona.fechaNacimiento,
                paisNacimiento: persona.paisNacimiento
                    ? {
                        id: persona.paisNacimiento.idPais,
                        descripcion: persona.paisNacimiento.descripcionPais,
                    }
                    : null,
                genero: persona.genero
                    ? {
                        id: persona.genero.idGenero,
                        descripcion: persona.genero.descripcionGenero,
                    }
                    : null,
                documento: persona.documento,
                tipoDocumento: persona.tipoDocumento
                    ? {
                        id: persona.tipoDocumento.idTipoDocumento,
                        descripcion: persona.tipoDocumento.descripcionTipoDocumento,
                    }
                    : null,
                ciclo: cicloEspecial
                    ? {
                        id: cicloEspecial.id,
                        descripcion: cicloEspecial.descripcion,
                    }
                    : null,
                cue: (_a = row.localizacion.establecimiento) === null || _a === void 0 ? void 0 : _a.cue,
                idEstablecimiento: (_b = row.localizacion.establecimiento) === null || _b === void 0 ? void 0 : _b.idEstablecimiento,
                nivel: row.nivel
                    ? {
                        id: row.nivel.idNivel,
                        descripcion: row.nivel.descripcionNivel,
                    }
                    : null,
                turno: row.turno
                    ? {
                        id: row.turno.idTurno,
                        descripcion: row.turno.descripcionTurno,
                    }
                    : null,
                cicloLectivo: row.cicloLectivo
                    ? {
                        id: row.cicloLectivo.idCicloLectivo,
                        descripcion: row.cicloLectivo.anio,
                    }
                    : null,
            };
        });
    }
}
exports.EstudiantesInscriptosEndpoint = EstudiantesInscriptosEndpoint;
//# sourceMappingURL=EstudiantesInscriptosEndpoint.js.map
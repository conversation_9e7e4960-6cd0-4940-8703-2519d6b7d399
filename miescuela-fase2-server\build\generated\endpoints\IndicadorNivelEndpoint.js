"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicadorNivelEndpoint = void 0;
const MiEscuelaEndpoints_1 = require("../../app/config/endpoint/MiEscuelaEndpoints");
const dao_1 = require("../orm/dao");
class IndicadorNivelEndpoint extends MiEscuelaEndpoints_1.MiEscuelaEndpoint {
    constructor() {
        super(dao_1.IndicadorNivelDAO.entity.toLowerCase(), '/calificaciones/' + dao_1.IndicadorNivelDAO.entity.toLowerCase(), dao_1.IndicadorNivelDAO);
    }
    getAllowGuest() {
        return false;
    }
}
exports.IndicadorNivelEndpoint = IndicadorNivelEndpoint;
//# sourceMappingURL=IndicadorNivelEndpoint.js.map
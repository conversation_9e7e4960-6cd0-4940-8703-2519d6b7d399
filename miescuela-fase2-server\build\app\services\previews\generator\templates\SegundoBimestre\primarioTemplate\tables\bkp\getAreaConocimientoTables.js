"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const getAreaConocimientoTable = ({ espacioCurricular, descripcionTipoPeriodo, nombreSeccion, columns, }) => {
    return `
      <table class="tabla">
        <tr class="title-table">
            <td style="background-color: #e2eedb; font-weight: bold; font-size: 12px;">
                ${nombreSeccion}
            </td>
            <td style="background-color: #e2eedb; border-bottom: 0px; font-weight: bold; font-size: 12px;">
                ${descripcionTipoPeriodo}
            </td>
        </tr>
        <tr>
                <td>
                    <div class="text-divide">
                      <b>${espacioCurricular}</b>
                      <div></div>
                    </div>
                </td>
            <td style="border-top: 1px solid #000000;"></td>
        </tr>
      ${(() => {
        let rows = '';
        columns.forEach((column) => {
            rows += ` <tr>
                      <td>${column.name}</td>
                      <td>${column.value}</td>
                  </tr>`;
        });
        return rows;
    })()}
    </table>`;
};
exports.default = (areasConocimiento) => `
  <style>
    * {
      font-family: Arial, Helvetica, sans-serif;
    }
    td, th{
        border: 1px solid black;
        border-top: 0px;
        border-left: 0px;
        padding-left: 20px;
        padding-right: 20px;
        width: 50%;
        font-size: 9px;
    }
    .page-container{
        margin: auto;
        line-height: 17px;
    }
    .text-divide{
      display: flex;
      justify-content: space-between;
      font-size: 12px;
    }
    .tabla{
        margin-top:16px;
        caption-side: bottom;
        margin-left: auto;
        margin-right: auto;
        border-collapse: collapse;
        min-height: 24%;
        width: 100%;
        flex-flow: column;
        align-items: center;
        border: 2px solid black;
        border-right: 2px solid black;
        border-bottom: 2px solid black;
    }
    .title-table{
        text-align: center;
        height: 61px;
    }
    @page {
      size: A4;
      margin: 0;
    }
    @media print {
      .page-container
      {
        page-break-after: always;
      }
    }
  </style>
  <div class="page-container">
    ${(() => {
    let rows = '';
    areasConocimiento.forEach((areaConocimiento) => {
        rows += getAreaConocimientoTable(areaConocimiento);
    });
    return rows;
})()}
  </div>
`;
//# sourceMappingURL=getAreaConocimientoTables.js.map